//! 风险管理配置模块

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tracing::{info, warn};

use crate::types::*;
use crate::engine::RiskEngineConfig;
use crate::rules::RuleEngineConfig;
use crate::metrics::MetricsCalculatorConfig;
use sigmax_core::{SigmaXResult, SigmaXError};

/// 风险管理系统配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskSystemConfig {
    /// 风险引擎配置
    pub risk_engine: RiskEngineConfig,
    /// 规则引擎配置
    pub rule_engine: RuleEngineConfig,
    /// 指标计算器配置
    pub metrics_calculator: MetricsCalculatorConfig,
    /// 迁移配置
    pub migration: MigrationConfig,
    /// 监控配置
    pub monitoring: MonitoringConfig,
}

/// 迁移配置
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct MigrationConfig {
    /// 是否启用新引擎
    pub enable_new_engine: bool,
    /// 是否启用双写模式
    pub enable_dual_write: bool,
    /// 是否启用结果对比
    pub enable_result_comparison: bool,
    /// 新引擎流量比例 (0-100)
    pub new_engine_traffic_percentage: u8,
    /// 自动回滚错误率阈值
    pub auto_rollback_error_rate: f64,
    /// 自动回滚响应时间阈值（毫秒）
    pub auto_rollback_response_time_ms: u64,
}

impl Default for MigrationConfig {
    fn default() -> Self {
        Self {
            enable_new_engine: false,
            enable_dual_write: true,
            enable_result_comparison: true,
            new_engine_traffic_percentage: 0,
            auto_rollback_error_rate: 5.0,
            auto_rollback_response_time_ms: 1000,
        }
    }
}

/// 监控配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonitoringConfig {
    /// 是否启用详细监控
    pub enable_detailed_monitoring: bool,
    /// 是否记录性能指标
    pub enable_performance_metrics: bool,
    /// 是否记录一致性检查
    pub enable_consistency_checks: bool,
    /// 指标收集间隔（秒）
    pub metrics_collection_interval_seconds: u64,
    /// 日志级别
    pub log_level: String,
}

impl Default for MonitoringConfig {
    fn default() -> Self {
        Self {
            enable_detailed_monitoring: true,
            enable_performance_metrics: true,
            enable_consistency_checks: true,
            metrics_collection_interval_seconds: 60,
            log_level: "info".to_string(),
        }
    }
}

impl Default for RiskSystemConfig {
    fn default() -> Self {
        Self {
            risk_engine: RiskEngineConfig::default(),
            rule_engine: RuleEngineConfig::default(),
            metrics_calculator: MetricsCalculatorConfig::default(),
            migration: MigrationConfig::default(),
            monitoring: MonitoringConfig::default(),
        }
    }
}

/// 配置管理器
pub struct ConfigManager {
    config: RiskSystemConfig,
    config_path: Option<String>,
}

impl ConfigManager {
    /// 创建新的配置管理器
    pub fn new() -> Self {
        Self {
            config: RiskSystemConfig::default(),
            config_path: None,
        }
    }

    /// 从文件加载配置
    pub fn load_from_file(path: &str) -> SigmaXResult<Self> {
        info!("从文件加载配置: {}", path);

        let content = std::fs::read_to_string(path)
            .map_err(|e| SigmaXError::Config(format!("读取配置文件失败: {}", e)))?;

        let config: RiskSystemConfig = if path.ends_with(".toml") {
            toml::from_str(&content)
                .map_err(|e| SigmaXError::Config(format!("解析TOML配置失败: {}", e)))?
        } else if path.ends_with(".json") {
            serde_json::from_str(&content)
                .map_err(|e| SigmaXError::Config(format!("解析JSON配置失败: {}", e)))?
        } else {
            return Err(SigmaXError::Config(
                "不支持的配置文件格式，仅支持 .toml 和 .json".to_string()
            ));
        };

        info!("成功加载配置文件");
        Ok(Self {
            config,
            config_path: Some(path.to_string()),
        })
    }

    /// 从环境变量加载配置
    pub fn load_from_env() -> SigmaXResult<Self> {
        info!("从环境变量加载配置");

        let mut config = RiskSystemConfig::default();

        // 风险引擎配置
        if let Ok(val) = std::env::var("RISK_ENGINE_PARALLEL_EXECUTION") {
            config.risk_engine.parallel_execution = val.parse().unwrap_or(true);
        }
        if let Ok(val) = std::env::var("RISK_ENGINE_MAX_EXECUTION_TIME_MS") {
            config.risk_engine.max_execution_time_ms = val.parse().unwrap_or(5000);
        }
        if let Ok(val) = std::env::var("RISK_ENGINE_ENABLE_CACHE") {
            config.risk_engine.enable_cache = val.parse().unwrap_or(true);
        }

        // 规则引擎配置
        if let Ok(val) = std::env::var("RULE_ENGINE_MAX_EXECUTION_TIME_MS") {
            config.rule_engine.max_rule_execution_time_ms = val.parse().unwrap_or(1000);
        }
        if let Ok(val) = std::env::var("RULE_ENGINE_ENABLE_CACHE") {
            config.rule_engine.enable_rule_cache = val.parse().unwrap_or(true);
        }

        // 迁移配置
        if let Ok(val) = std::env::var("MIGRATION_ENABLE_NEW_ENGINE") {
            config.migration.enable_new_engine = val.parse().unwrap_or(false);
        }
        if let Ok(val) = std::env::var("MIGRATION_ENABLE_DUAL_WRITE") {
            config.migration.enable_dual_write = val.parse().unwrap_or(true);
        }
        if let Ok(val) = std::env::var("MIGRATION_TRAFFIC_PERCENTAGE") {
            config.migration.new_engine_traffic_percentage = val.parse().unwrap_or(0);
        }

        info!("成功从环境变量加载配置");
        Ok(Self {
            config,
            config_path: None,
        })
    }

    /// 保存配置到文件
    pub fn save_to_file(&self, path: &str) -> SigmaXResult<()> {
        info!("保存配置到文件: {}", path);

        let content = if path.ends_with(".toml") {
            toml::to_string_pretty(&self.config)
                .map_err(|e| SigmaXError::Config(format!("序列化TOML配置失败: {}", e)))?
        } else if path.ends_with(".json") {
            serde_json::to_string_pretty(&self.config)
                .map_err(|e| SigmaXError::Config(format!("序列化JSON配置失败: {}", e)))?
        } else {
            return Err(SigmaXError::Config(
                "不支持的配置文件格式，仅支持 .toml 和 .json".to_string()
            ));
        };

        std::fs::write(path, content)
            .map_err(|e| SigmaXError::Config(format!("写入配置文件失败: {}", e)))?;

        info!("成功保存配置文件");
        Ok(())
    }

    /// 获取配置
    pub fn get_config(&self) -> &RiskSystemConfig {
        &self.config
    }

    /// 更新配置
    pub fn update_config(&mut self, new_config: RiskSystemConfig) {
        info!("更新系统配置");
        self.config = new_config;
    }

    /// 更新迁移配置
    pub fn update_migration_config(&mut self, migration_config: MigrationConfig) {
        info!("更新迁移配置");
        self.config.migration = migration_config;
    }

    /// 验证配置
    pub fn validate_config(&self) -> SigmaXResult<()> {
        info!("验证配置");

        // 验证风险引擎配置
        if self.config.risk_engine.max_execution_time_ms == 0 {
            return Err(SigmaXError::Config(
                "风险引擎最大执行时间不能为0".to_string()
            ));
        }

        // 验证规则引擎配置
        if self.config.rule_engine.max_rule_execution_time_ms == 0 {
            return Err(SigmaXError::Config(
                "规则引擎最大执行时间不能为0".to_string()
            ));
        }

        // 验证迁移配置
        if self.config.migration.new_engine_traffic_percentage > 100 {
            return Err(SigmaXError::Config(
                "新引擎流量比例不能超过100%".to_string()
            ));
        }

        if self.config.migration.auto_rollback_error_rate < 0.0 || self.config.migration.auto_rollback_error_rate > 100.0 {
            return Err(SigmaXError::Config(
                "自动回滚错误率阈值必须在0-100之间".to_string()
            ));
        }

        info!("配置验证通过");
        Ok(())
    }

    /// 重新加载配置
    pub fn reload(&mut self) -> SigmaXResult<()> {
        if let Some(path) = &self.config_path {
            info!("重新加载配置文件: {}", path);
            let new_manager = Self::load_from_file(path)?;
            self.config = new_manager.config;
            info!("配置重新加载成功");
        } else {
            warn!("没有配置文件路径，无法重新加载");
        }
        Ok(())
    }
}

/// 预定义配置模板
pub mod templates {
    use super::*;

    /// 创建保守型风险配置
    pub fn create_conservative_config() -> RiskSystemConfig {
        RiskSystemConfig {
            risk_engine: RiskEngineConfig {
                parallel_execution: false,
                max_execution_time_ms: 3000,
                enable_cache: true,
                default_risk_level: RiskLevel::Low,
            },
            rule_engine: RuleEngineConfig {
                max_rule_execution_time_ms: 500,
                enable_rule_cache: true,
                cache_expiry_seconds: 600,
            },
            metrics_calculator: MetricsCalculatorConfig {
                var_confidence_levels: vec![0.95, 0.99],
                volatility_window_days: 60,
                correlation_window_days: 120,
                use_ewma: true,
                ewma_lambda: 0.94,
            },
            migration: MigrationConfig::default(),
            monitoring: MonitoringConfig::default(),
        }
    }

    /// 创建激进型风险配置
    pub fn create_aggressive_config() -> RiskSystemConfig {
        RiskSystemConfig {
            risk_engine: RiskEngineConfig {
                parallel_execution: true,
                max_execution_time_ms: 1000,
                enable_cache: true,
                default_risk_level: RiskLevel::Medium,
            },
            rule_engine: RuleEngineConfig {
                max_rule_execution_time_ms: 200,
                enable_rule_cache: true,
                cache_expiry_seconds: 300,
            },
            metrics_calculator: MetricsCalculatorConfig {
                var_confidence_levels: vec![0.90, 0.95],
                volatility_window_days: 20,
                correlation_window_days: 40,
                use_ewma: true,
                ewma_lambda: 0.96,
            },
            migration: MigrationConfig::default(),
            monitoring: MonitoringConfig::default(),
        }
    }

    /// 创建平衡型风险配置
    pub fn create_balanced_config() -> RiskSystemConfig {
        RiskSystemConfig::default()
    }
}
