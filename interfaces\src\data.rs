//! 数据相关接口定义
//!
//! 定义数据提供者、市场数据、历史数据等接口契约

use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use sigmax_core::{TradingPair, TimeFrame, Candle, SigmaXResult};
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use std::collections::HashMap;

// ============================================================================
// 核心数据接口
// ============================================================================

/// 通用数据提供者接口
#[async_trait]
pub trait DataProvider: Send + Sync {
    /// 获取实时价格
    async fn get_price(&self, trading_pair: &TradingPair) -> DataResult<Price>;
    
    /// 获取历史K线数据
    async fn get_historical_candles(
        &self,
        trading_pair: &TradingPair,
        timeframe: TimeFrame,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> DataResult<Vec<Candle>>;
}

/// 市场数据提供者接口
#[async_trait]
pub trait MarketDataProvider: Send + Sync {
    /// 获取实时市场数据
    async fn get_market_data(&self, trading_pair: &TradingPair) -> DataResult<MarketData>;
    
    /// 订阅实时数据流
    async fn subscribe_market_data(&self, trading_pairs: Vec<TradingPair>) -> DataResult<MarketDataStream>;
    
    /// 取消订阅
    async fn unsubscribe_market_data(&self, trading_pairs: Vec<TradingPair>) -> DataResult<()>;
    
    /// 获取市场深度
    async fn get_order_book(&self, trading_pair: &TradingPair, depth: usize) -> DataResult<OrderBook>;
    
    /// 获取最近交易
    async fn get_recent_trades(&self, trading_pair: &TradingPair, limit: usize) -> DataResult<Vec<Trade>>;
}

/// 历史数据提供者接口
#[async_trait]
pub trait HistoricalDataProvider: Send + Sync {
    /// 获取历史K线数据
    async fn get_candles(
        &self,
        trading_pair: &TradingPair,
        timeframe: TimeFrame,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> DataResult<Vec<Candle>>;
    
    /// 获取历史交易数据
    async fn get_historical_trades(
        &self,
        trading_pair: &TradingPair,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> DataResult<Vec<Trade>>;
    
    /// 获取历史订单簿快照
    async fn get_historical_order_book(
        &self,
        trading_pair: &TradingPair,
        timestamp: DateTime<Utc>,
    ) -> DataResult<OrderBook>;
}

/// 价格提供者接口
#[async_trait]
pub trait PriceProvider: Send + Sync {
    /// 获取实时价格
    async fn get_current_price(&self, trading_pair: &TradingPair) -> PriceResult;
    
    /// 获取批量价格
    async fn get_batch_prices(&self, trading_pairs: Vec<TradingPair>) -> DataResult<HashMap<String, Price>>;
    
    /// 获取价格历史
    async fn get_price_history(
        &self,
        trading_pair: &TradingPair,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> DataResult<Vec<PricePoint>>;
}

// ============================================================================
// 数据模型
// ============================================================================

/// 价格信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Price {
    pub symbol: String,
    pub price: Decimal,
    pub timestamp: DateTime<Utc>,
    pub source: String,
}

/// 市场数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MarketData {
    pub trading_pair: TradingPair,
    pub price: Decimal,
    pub volume_24h: Decimal,
    pub change_24h: Decimal,
    pub change_percent_24h: Decimal,
    pub high_24h: Decimal,
    pub low_24h: Decimal,
    pub timestamp: DateTime<Utc>,
}

/// 市场数据流
#[derive(Debug)]
pub struct MarketDataStream {
    // TODO: 实现实时数据流
    pub stream_id: String,
}

/// 订单簿
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrderBook {
    pub trading_pair: TradingPair,
    pub bids: Vec<OrderBookEntry>,
    pub asks: Vec<OrderBookEntry>,
    pub timestamp: DateTime<Utc>,
}

/// 订单簿条目
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrderBookEntry {
    pub price: Decimal,
    pub quantity: Decimal,
}

/// 交易记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Trade {
    pub id: String,
    pub trading_pair: TradingPair,
    pub price: Decimal,
    pub quantity: Decimal,
    pub side: TradeSide,
    pub timestamp: DateTime<Utc>,
}

/// 交易方向
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TradeSide {
    Buy,
    Sell,
}

/// 价格点
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PricePoint {
    pub price: Decimal,
    pub timestamp: DateTime<Utc>,
}

// ============================================================================
// 结果类型
// ============================================================================

/// 数据结果类型
pub type DataResult<T> = Result<T, DataError>;

/// 价格结果类型
pub type PriceResult = Result<Price, DataError>;

// ============================================================================
// 错误类型
// ============================================================================

/// 数据错误
#[derive(Debug, thiserror::Error)]
pub enum DataError {
    #[error("Data not found: {resource}")]
    NotFound { resource: String },
    
    #[error("Data provider unavailable: {provider}")]
    ProviderUnavailable { provider: String },
    
    #[error("Invalid time range: start={start}, end={end}")]
    InvalidTimeRange { start: DateTime<Utc>, end: DateTime<Utc> },
    
    #[error("Rate limit exceeded: {provider}")]
    RateLimitExceeded { provider: String },
    
    #[error("Data parsing failed: {reason}")]
    ParsingFailed { reason: String },
    
    #[error("Network error: {message}")]
    NetworkError { message: String },
    
    #[error("Authentication failed: {provider}")]
    AuthenticationFailed { provider: String },
    
    #[error("Invalid trading pair: {trading_pair}")]
    InvalidTradingPair { trading_pair: String },
    
    #[error("Data quality issue: {issue}")]
    DataQualityIssue { issue: String },
    
    #[error("Timeout: operation timed out after {timeout_ms}ms")]
    Timeout { timeout_ms: u64 },
    
    #[error("Configuration error: {message}")]
    Configuration { message: String },
}