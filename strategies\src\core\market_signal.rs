//! 市场信号识别模块
//!
//! 提供市场状态分析和信号识别功能，为智能策略切换做准备
//! 
//! ## 设计说明
//! 当前实现为基础架构，智能切换功能暂时注释，专注于单策略模式的稳定运行

use serde::{Deserialize, Serialize};
use std::collections::VecDeque;
use chrono::{DateTime, Utc, Duration};
use sigmax_core::{SigmaXResult, SigmaXError, Candle};
use tracing::{info, debug};
use rust_decimal::prelude::ToPrimitive;

/// 市场状态枚举
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum MarketState {
    /// 牛市趋势 - 适合动量策略
    BullTrend { 
        strength: f64, 
        duration: Duration 
    },
    /// 熊市趋势 - 适合反向策略或暂停
    BearTrend { 
        strength: f64, 
        duration: Duration 
    },
    /// 横盘震荡 - 适合网格策略
    Sideways { 
        volatility: f64, 
        range: PriceRange 
    },
    /// 突破准备 - 适合突破策略
    Consolidation { 
        compression_ratio: f64 
    },
    /// 高波动混乱 - 需要保守策略
    Chaos { 
        volatility_spike: f64 
    },
    /// 流动性枯竭 - 暂停交易
    IlliquidMarket { 
        spread_ratio: f64 
    },
    /// 未知状态 - 默认状态
    Unknown,
}

impl Default for MarketState {
    fn default() -> Self {
        Self::Unknown
    }
}

impl Eq for MarketState {}

impl std::hash::Hash for MarketState {
    fn hash<H: std::hash::Hasher>(&self, state: &mut H) {
        match self {
            MarketState::BullTrend { strength, duration } => {
                0u8.hash(state);
                ((strength * 1000000.0) as i64).hash(state);
                duration.num_seconds().hash(state);
            }
            MarketState::BearTrend { strength, duration } => {
                1u8.hash(state);
                ((strength * 1000000.0) as i64).hash(state);
                duration.num_seconds().hash(state);
            }
            MarketState::Sideways { volatility, range } => {
                2u8.hash(state);
                ((volatility * 1000000.0) as i64).hash(state);
                range.hash(state);
            }
            MarketState::Consolidation { compression_ratio } => {
                3u8.hash(state);
                ((compression_ratio * 1000000.0) as i64).hash(state);
            }
            MarketState::Chaos { volatility_spike } => {
                4u8.hash(state);
                ((volatility_spike * 1000000.0) as i64).hash(state);
            }
            MarketState::IlliquidMarket { spread_ratio } => {
                5u8.hash(state);
                ((spread_ratio * 1000000.0) as i64).hash(state);
            }
            MarketState::Unknown => {
                6u8.hash(state);
            }
        }
    }
}

/// 价格区间
#[derive(Debug, Clone, PartialEq, Default, Serialize, Deserialize)]
pub struct PriceRange {
    pub support: f64,
    pub resistance: f64,
    pub mid_point: f64,
}

impl Eq for PriceRange {}

impl std::hash::Hash for PriceRange {
    fn hash<H: std::hash::Hasher>(&self, state: &mut H) {
        // 将浮点数转换为整数进行哈希
        ((self.support * 1000000.0) as i64).hash(state);
        ((self.resistance * 1000000.0) as i64).hash(state);
        ((self.mid_point * 1000000.0) as i64).hash(state);
    }
}

impl PriceRange {
    pub fn new(support: f64, resistance: f64) -> Self {
        Self {
            support,
            resistance,
            mid_point: (support + resistance) / 2.0,
        }
    }
    
    pub fn width(&self) -> f64 {
        self.resistance - self.support
    }
    
    pub fn width_percentage(&self) -> f64 {
        self.width() / self.mid_point * 100.0
    }
}

/// 市场信号类型
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum MarketSignal {
    /// 趋势信号
    Trending { 
        direction: TrendDirection, 
        strength: f64,
        confidence: f64,
    },
    /// 震荡信号
    Ranging { 
        volatility: f64, 
        support: f64, 
        resistance: f64,
        confidence: f64,
    },
    /// 突破信号
    Breakout { 
        direction: BreakoutDirection, 
        volume_confirmation: bool,
        confidence: f64,
    },
    /// 高波动信号
    HighVolatility { 
        vix_level: f64,
        confidence: f64,
    },
    /// 流动性信号
    LowLiquidity { 
        spread_ratio: f64,
        confidence: f64,
    },
    /// 无明确信号
    NoSignal,
}

/// 趋势方向
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum TrendDirection {
    Up,
    Down,
}

/// 突破方向
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum BreakoutDirection {
    Upward,
    Downward,
}

/// 技术指标数据
#[derive(Debug, Clone)]
pub struct TechnicalIndicators {
    pub sma_20: f64,
    pub sma_50: f64,
    pub ema_12: f64,
    pub ema_26: f64,
    pub rsi: f64,
    pub volatility: f64,
    pub volume_ratio: f64,
    pub price: f64,
    pub timestamp: DateTime<Utc>,
}

/// 市场信号检测器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MarketSignalConfig {
    /// 趋势强度阈值
    pub trend_strength_threshold: f64,
    /// 波动率阈值
    pub volatility_threshold: f64,
    /// RSI超买超卖阈值
    pub rsi_overbought: f64,
    pub rsi_oversold: f64,
    /// 成交量比率阈值
    pub volume_ratio_threshold: f64,
    /// 信号置信度阈值
    pub confidence_threshold: f64,
}

impl Default for MarketSignalConfig {
    fn default() -> Self {
        Self {
            trend_strength_threshold: 0.7,
            volatility_threshold: 0.05,
            rsi_overbought: 70.0,
            rsi_oversold: 30.0,
            volume_ratio_threshold: 1.5,
            confidence_threshold: 0.6,
        }
    }
}

/// 市场信号检测器
/// 
/// 注意：当前实现为基础架构，智能切换功能暂时注释
pub struct MarketSignalDetector {
    /// 配置
    config: MarketSignalConfig,
    /// 价格历史
    price_history: VecDeque<f64>,
    /// 成交量历史
    volume_history: VecDeque<f64>,
    /// 技术指标历史
    indicator_history: VecDeque<TechnicalIndicators>,
    /// 当前市场状态
    current_state: MarketState,
    /// 最大历史长度
    max_history_length: usize,
}

impl MarketSignalDetector {
    /// 创建新的市场信号检测器
    pub fn new(config: MarketSignalConfig) -> Self {
        Self {
            config,
            price_history: VecDeque::new(),
            volume_history: VecDeque::new(),
            indicator_history: VecDeque::new(),
            current_state: MarketState::Unknown,
            max_history_length: 200, // 保留200个数据点
        }
    }
    
    /// 使用默认配置创建
    pub fn with_default_config() -> Self {
        Self::new(MarketSignalConfig::default())
    }
    
    /// 更新市场数据并分析信号
    pub async fn update_and_analyze(&mut self, candle: &Candle) -> SigmaXResult<MarketSignal> {
        // 1. 更新历史数据
        self.update_history(candle).await?;
        
        // 2. 计算技术指标
        let indicators = self.calculate_indicators().await?;
        
        // 3. 分析市场信号
        let signal = self.analyze_market_signal(&indicators).await?;
        
        // 4. 更新市场状态
        self.update_market_state(&signal).await?;
        
        debug!("市场信号分析完成: {:?}", signal);
        Ok(signal)
    }
    
    /// 获取当前市场状态
    pub fn get_current_state(&self) -> &MarketState {
        &self.current_state
    }
    
    /// 检查是否有足够的历史数据进行分析
    pub fn has_sufficient_data(&self) -> bool {
        self.price_history.len() >= 50 // 至少需要50个数据点
    }

    /// 更新历史数据
    async fn update_history(&mut self, candle: &Candle) -> SigmaXResult<()> {
        let price = candle.close.to_f64().unwrap_or(0.0);
        let volume = candle.volume.to_f64().unwrap_or(0.0);

        // 添加新数据
        self.price_history.push_back(price);
        self.volume_history.push_back(volume);

        // 限制历史长度
        while self.price_history.len() > self.max_history_length {
            self.price_history.pop_front();
        }
        while self.volume_history.len() > self.max_history_length {
            self.volume_history.pop_front();
        }

        Ok(())
    }

    /// 计算技术指标
    async fn calculate_indicators(&self) -> SigmaXResult<TechnicalIndicators> {
        if !self.has_sufficient_data() {
            return Err(SigmaXError::Data("历史数据不足，无法计算技术指标".to_string()));
        }

        let prices: Vec<f64> = self.price_history.iter().cloned().collect();
        let volumes: Vec<f64> = self.volume_history.iter().cloned().collect();
        let current_price = *prices.last().unwrap();

        // 计算移动平均线
        let sma_20 = self.calculate_sma(&prices, 20)?;
        let sma_50 = self.calculate_sma(&prices, 50)?;
        let ema_12 = self.calculate_ema(&prices, 12)?;
        let ema_26 = self.calculate_ema(&prices, 26)?;

        // 计算RSI
        let rsi = self.calculate_rsi(&prices, 14)?;

        // 计算波动率
        let volatility = self.calculate_volatility(&prices, 20)?;

        // 计算成交量比率
        let volume_ratio = self.calculate_volume_ratio(&volumes, 20)?;

        Ok(TechnicalIndicators {
            sma_20,
            sma_50,
            ema_12,
            ema_26,
            rsi,
            volatility,
            volume_ratio,
            price: current_price,
            timestamp: Utc::now(),
        })
    }

    /// 分析市场信号
    async fn analyze_market_signal(&self, indicators: &TechnicalIndicators) -> SigmaXResult<MarketSignal> {
        // 如果数据不足，返回无信号
        if !self.has_sufficient_data() {
            return Ok(MarketSignal::NoSignal);
        }

        // 1. 检查趋势信号
        if let Some(trend_signal) = self.check_trend_signal(indicators)? {
            return Ok(trend_signal);
        }

        // 2. 检查震荡信号
        if let Some(range_signal) = self.check_range_signal(indicators)? {
            return Ok(range_signal);
        }

        // 3. 检查高波动信号
        if let Some(volatility_signal) = self.check_volatility_signal(indicators)? {
            return Ok(volatility_signal);
        }

        // 4. 检查流动性信号
        if let Some(liquidity_signal) = self.check_liquidity_signal(indicators)? {
            return Ok(liquidity_signal);
        }

        // 默认返回无信号
        Ok(MarketSignal::NoSignal)
    }

    /// 更新市场状态
    async fn update_market_state(&mut self, signal: &MarketSignal) -> SigmaXResult<()> {
        let new_state = match signal {
            MarketSignal::Trending { direction, strength, .. } => {
                match direction {
                    TrendDirection::Up => MarketState::BullTrend {
                        strength: *strength,
                        duration: Duration::minutes(1) // 简化实现
                    },
                    TrendDirection::Down => MarketState::BearTrend {
                        strength: *strength,
                        duration: Duration::minutes(1)
                    },
                }
            },
            MarketSignal::Ranging { volatility, support, resistance, .. } => {
                MarketState::Sideways {
                    volatility: *volatility,
                    range: PriceRange::new(*support, *resistance)
                }
            },
            MarketSignal::HighVolatility { vix_level, .. } => {
                MarketState::Chaos {
                    volatility_spike: *vix_level
                }
            },
            MarketSignal::LowLiquidity { spread_ratio, .. } => {
                MarketState::IlliquidMarket {
                    spread_ratio: *spread_ratio
                }
            },
            _ => MarketState::Unknown,
        };

        if new_state != self.current_state {
            info!("市场状态变化: {:?} -> {:?}", self.current_state, new_state);
            self.current_state = new_state;
        }

        Ok(())
    }

    // ==================== 技术指标计算方法 ====================

    /// 计算简单移动平均线
    fn calculate_sma(&self, prices: &[f64], period: usize) -> SigmaXResult<f64> {
        if prices.len() < period {
            return Err(SigmaXError::Data(format!("数据不足，需要{}个数据点", period)));
        }

        let sum: f64 = prices.iter().rev().take(period).sum();
        Ok(sum / period as f64)
    }

    /// 计算指数移动平均线
    fn calculate_ema(&self, prices: &[f64], period: usize) -> SigmaXResult<f64> {
        if prices.len() < period {
            return Err(SigmaXError::Data(format!("数据不足，需要{}个数据点", period)));
        }

        let multiplier = 2.0 / (period as f64 + 1.0);
        let mut ema = prices[prices.len() - period];

        for &price in prices.iter().rev().take(period - 1) {
            ema = (price * multiplier) + (ema * (1.0 - multiplier));
        }

        Ok(ema)
    }

    /// 计算RSI
    fn calculate_rsi(&self, prices: &[f64], period: usize) -> SigmaXResult<f64> {
        if prices.len() < period + 1 {
            return Err(SigmaXError::Data(format!("数据不足，需要{}个数据点", period + 1)));
        }

        let mut gains = Vec::new();
        let mut losses = Vec::new();

        for i in 1..=period {
            let change = prices[prices.len() - i] - prices[prices.len() - i - 1];
            if change > 0.0 {
                gains.push(change);
                losses.push(0.0);
            } else {
                gains.push(0.0);
                losses.push(-change);
            }
        }

        let avg_gain: f64 = gains.iter().sum::<f64>() / period as f64;
        let avg_loss: f64 = losses.iter().sum::<f64>() / period as f64;

        if avg_loss == 0.0 {
            return Ok(100.0);
        }

        let rs = avg_gain / avg_loss;
        let rsi = 100.0 - (100.0 / (1.0 + rs));

        Ok(rsi)
    }

    /// 计算波动率
    fn calculate_volatility(&self, prices: &[f64], period: usize) -> SigmaXResult<f64> {
        if prices.len() < period {
            return Err(SigmaXError::Data(format!("数据不足，需要{}个数据点", period)));
        }

        let recent_prices: Vec<f64> = prices.iter().rev().take(period).cloned().collect();
        let mean = recent_prices.iter().sum::<f64>() / period as f64;

        let variance = recent_prices.iter()
            .map(|price| (price - mean).powi(2))
            .sum::<f64>() / period as f64;

        Ok(variance.sqrt() / mean) // 相对波动率
    }

    /// 计算成交量比率
    fn calculate_volume_ratio(&self, volumes: &[f64], period: usize) -> SigmaXResult<f64> {
        if volumes.len() < period {
            return Err(SigmaXError::Data(format!("数据不足，需要{}个数据点", period)));
        }

        let current_volume = *volumes.last().unwrap();
        let avg_volume: f64 = volumes.iter().rev().take(period).sum::<f64>() / period as f64;

        if avg_volume == 0.0 {
            return Ok(1.0);
        }

        Ok(current_volume / avg_volume)
    }

    // ==================== 信号检测方法 ====================

    /// 检查趋势信号
    fn check_trend_signal(&self, indicators: &TechnicalIndicators) -> SigmaXResult<Option<MarketSignal>> {
        let price = indicators.price;
        let sma_20 = indicators.sma_20;
        let sma_50 = indicators.sma_50;
        let ema_12 = indicators.ema_12;
        let ema_26 = indicators.ema_26;

        // 判断趋势方向和强度
        let trend_signals = vec![
            price > sma_20,
            sma_20 > sma_50,
            ema_12 > ema_26,
            indicators.rsi > 50.0,
        ];

        let bullish_count = trend_signals.iter().filter(|&&x| x).count();
        let bearish_count = trend_signals.len() - bullish_count;

        let strength = (bullish_count as f64 - bearish_count as f64).abs() / trend_signals.len() as f64;
        let confidence = if strength > self.config.trend_strength_threshold { 0.8 } else { 0.4 };

        if strength > self.config.trend_strength_threshold {
            let direction = if bullish_count > bearish_count {
                TrendDirection::Up
            } else {
                TrendDirection::Down
            };

            return Ok(Some(MarketSignal::Trending {
                direction,
                strength,
                confidence,
            }));
        }

        Ok(None)
    }

    /// 检查震荡信号
    fn check_range_signal(&self, indicators: &TechnicalIndicators) -> SigmaXResult<Option<MarketSignal>> {
        // 简化实现：基于波动率和价格位置判断
        if indicators.volatility < self.config.volatility_threshold {
            let prices: Vec<f64> = self.price_history.iter().cloned().collect();
            let recent_prices: Vec<f64> = prices.iter().rev().take(20).cloned().collect();

            let support = recent_prices.iter().fold(f64::INFINITY, |a, &b| a.min(b));
            let resistance = recent_prices.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));

            let range_width = (resistance - support) / indicators.price;

            if range_width > 0.02 && range_width < 0.15 { // 2%-15%的价格区间
                return Ok(Some(MarketSignal::Ranging {
                    volatility: indicators.volatility,
                    support,
                    resistance,
                    confidence: 0.7,
                }));
            }
        }

        Ok(None)
    }

    /// 检查波动率信号
    fn check_volatility_signal(&self, indicators: &TechnicalIndicators) -> SigmaXResult<Option<MarketSignal>> {
        if indicators.volatility > self.config.volatility_threshold * 3.0 {
            return Ok(Some(MarketSignal::HighVolatility {
                vix_level: indicators.volatility,
                confidence: 0.9,
            }));
        }

        Ok(None)
    }

    /// 检查流动性信号
    fn check_liquidity_signal(&self, indicators: &TechnicalIndicators) -> SigmaXResult<Option<MarketSignal>> {
        if indicators.volume_ratio < 0.5 { // 成交量低于平均值的50%
            return Ok(Some(MarketSignal::LowLiquidity {
                spread_ratio: 1.0 / indicators.volume_ratio,
                confidence: 0.8,
            }));
        }

        Ok(None)
    }
}
