//! 配置服务模块
//!
//! 提供模块化的配置管理服务，每个配置类型都有专门的服务负责
//!
//! ## 模块结构
//! - `traits`: 配置服务接口定义
//! - `manager`: 配置管理器，统一协调各配置服务
//! - `system_config_service`: 系统核心配置服务
//! - `trading_config_service`: 交易配置服务
//! - `risk_config_service`: 风险配置服务
//! - `strategy_config_service`: 策略配置服务
//! - `notification_config_service`: 通知配置服务
//! - `api_config_service`: API配置服务
//! - `cache_config_service`: 缓存配置服务
//! - `monitoring_config_service`: 监控配置服务
//! - `global_access`: 全局配置访问函数
//!
//! ## 设计原则
//! - **单一职责**: 每个配置服务只负责一种配置类型
//! - **模块化**: 配置服务可独立开发、测试和部署
//! - **可扩展**: 新增配置类型时不影响现有代码
//! - **向后兼容**: 保持现有API的兼容性

pub mod traits;
pub mod manager;
pub mod system_config_service;
pub mod trading_config_service;
pub mod risk_config_service;
pub mod strategy_config_service;
pub mod notification_config_service;
pub mod api_config_service;
pub mod cache_config_service;
pub mod monitoring_config_service;
pub mod global_access;

// 重新导出核心类型
pub use traits::ConfigService;
pub use manager::ConfigManager;
pub use system_config_service::SystemConfigService;
pub use trading_config_service::TradingConfigService;
pub use risk_config_service::RiskConfigService;
pub use strategy_config_service::StrategyConfigService;
pub use notification_config_service::NotificationConfigService;
pub use api_config_service::ApiConfigService;
pub use cache_config_service::CacheConfigService;
pub use monitoring_config_service::MonitoringConfigService;
pub use global_access::*;

// TODO: 待实现的复杂配置服务
// - DatabaseConfigService: 数据库配置服务
// - StrategyTemplateService: 策略模板服务  
// - ExchangeConfigService: 交易所配置服务
