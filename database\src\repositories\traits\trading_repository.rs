//! 交易配置仓储接口
//!
//! 定义交易配置数据访问的标准接口

use async_trait::async_trait;
use sigmax_core::{SigmaXResult, TradingConfig};
use uuid::Uuid;
use chrono::{DateTime, Utc};

/// 交易配置仓储接口
///
/// 提供交易配置的数据访问功能，包括：
/// - 配置的增删改查
/// - 配置验证和重置
/// - 配置历史记录
#[async_trait]
pub trait TradingRepository: Send + Sync {
    // ============================================================================
    // 交易配置管理 - 与其他配置服务保持一致的接口
    // ============================================================================

    /// 获取当前启用的交易配置
    ///
    /// # Returns
    ///
    /// 返回当前启用的交易配置，如果没有启用的配置则返回默认配置
    async fn get_trading_config(&self) -> SigmaXResult<TradingConfig>;

    /// 保存交易配置
    ///
    /// # Arguments
    ///
    /// * `config` - 要保存的交易配置
    async fn save_trading_config(&self, config: &TradingConfig) -> SigmaXResult<()>;

    /// 重置交易配置为默认值
    async fn reset_trading_config(&self) -> SigmaXResult<()>;

    // ============================================================================
    // 交易配置记录管理 - 完整的CRUD操作
    // ============================================================================

    /// 获取当前启用的交易配置
    ///
    /// # Returns
    ///
    /// 返回当前启用的交易配置，如果没有启用的配置则返回默认配置
    async fn get_config(&self) -> SigmaXResult<TradingConfig>;

    /// 根据ID获取交易配置
    ///
    /// # Arguments
    ///
    /// * `id` - 配置ID
    async fn get_config_by_id(&self, id: Uuid) -> SigmaXResult<Option<TradingConfig>>;

    /// 根据名称获取交易配置
    ///
    /// # Arguments
    ///
    /// * `name` - 配置名称
    async fn get_config_by_name(&self, name: &str) -> SigmaXResult<Option<TradingConfig>>;

    /// 保存交易配置
    ///
    /// # Arguments
    ///
    /// * `config` - 要保存的交易配置
    ///
    /// # Returns
    ///
    /// 返回保存后的配置ID
    async fn save_config(&self, config: &TradingConfig) -> SigmaXResult<Uuid>;

    /// 更新交易配置
    ///
    /// # Arguments
    ///
    /// * `id` - 配置ID
    /// * `config` - 更新的配置数据
    async fn update_config(&self, id: Uuid, config: &TradingConfig) -> SigmaXResult<()>;

    /// 删除交易配置
    ///
    /// # Arguments
    ///
    /// * `id` - 配置ID
    async fn delete_config(&self, id: Uuid) -> SigmaXResult<()>;

    /// 启用指定配置
    ///
    /// # Arguments
    ///
    /// * `id` - 配置ID
    ///
    /// # Note
    ///
    /// 启用指定配置时，会自动禁用其他所有配置
    async fn enable_config(&self, id: Uuid) -> SigmaXResult<()>;

    /// 禁用指定配置
    ///
    /// # Arguments
    ///
    /// * `id` - 配置ID
    async fn disable_config(&self, id: Uuid) -> SigmaXResult<()>;

    /// 重置为默认配置
    ///
    /// # Returns
    ///
    /// 返回重置后的配置ID
    async fn reset_to_default(&self) -> SigmaXResult<Uuid>;

    /// 获取所有配置列表
    ///
    /// # Arguments
    ///
    /// * `enabled_only` - 是否只返回启用的配置
    async fn list_configs(&self, enabled_only: bool) -> SigmaXResult<Vec<TradingConfigRecord>>;

    /// 检查配置是否存在
    ///
    /// # Arguments
    ///
    /// * `name` - 配置名称
    async fn config_exists(&self, name: &str) -> SigmaXResult<bool>;

    /// 获取配置统计信息
    async fn get_statistics(&self) -> SigmaXResult<TradingConfigStatistics>;
}

/// 交易配置记录
///
/// 包含完整的配置信息和元数据
#[derive(Debug, Clone)]
pub struct TradingConfigRecord {
    /// 配置ID
    pub id: Uuid,
    /// 配置名称
    pub name: String,
    /// 配置描述
    pub description: Option<String>,
    /// 是否启用
    pub enabled: bool,
    /// 交易配置参数
    pub config: TradingConfig,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 更新时间
    pub updated_at: DateTime<Utc>,
    /// 创建者
    pub created_by: Option<String>,
}

/// 交易配置统计信息
#[derive(Debug, Clone)]
pub struct TradingConfigStatistics {
    /// 总配置数量
    pub total_configs: u64,
    /// 启用的配置数量
    pub enabled_configs: u64,
    /// 最后更新时间
    pub last_updated: Option<DateTime<Utc>>,
    /// 当前活跃配置ID
    pub active_config_id: Option<Uuid>,
}
