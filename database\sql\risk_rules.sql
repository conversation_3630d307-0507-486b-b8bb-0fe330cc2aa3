-- risk_rules 表结构（从线上数据库导出）
-- 导出时间: 2025-08-05 18:15:47

DROP TABLE IF EXISTS "public"."risk_rules" CASCADE;

CREATE TABLE "public"."risk_rules" (
  "id" UUID NOT NULL DEFAULT gen_random_uuid(),
  "name" VARCHAR(255) NOT NULL,
  "description" TEXT,
  "category" VARCHAR(50) NOT NULL,
  "rule_type" VARCHAR(100) NOT NULL,
  "parameters" JSONB NOT NULL DEFAULT '{}'::jsonb,
  "conditions" JSONB DEFAULT '{}'::jsonb,
  "enabled" BOOLEAN NOT NULL DEFAULT true,
  "priority" INTEGER NOT NULL DEFAULT 100,
  "strategy_type" VARCHAR(100),
  "trading_pairs" JSONB DEFAULT '[]'::jsonb,
  "execution_count" BIGINT DEFAULT 0,
  "success_count" BIGINT DEFAULT 0,
  "failure_count" BIGINT DEFAULT 0,
  "last_executed_at" TIMESTAMPTZ,
  "created_at" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  "updated_at" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  "created_by" VARCHAR(255),
  "updated_by" VARCHAR(255),
  "version" INTEGER NOT NULL DEFAULT 1,
  "is_active" BOOLEAN NOT NULL DEFAULT true,
  PRIMARY KEY ("id")
);

-- 索引

CREATE UNIQUE INDEX unified_risk_rules_pkey ON public.risk_rules USING btree (id);
CREATE INDEX idx_unified_risk_rules_category ON public.risk_rules USING btree (category);
CREATE INDEX idx_unified_risk_rules_type ON public.risk_rules USING btree (rule_type);
CREATE INDEX idx_unified_risk_rules_enabled ON public.risk_rules USING btree (enabled);
CREATE INDEX idx_unified_risk_rules_priority ON public.risk_rules USING btree (priority DESC);
CREATE INDEX idx_unified_risk_rules_strategy ON public.risk_rules USING btree (strategy_type);
CREATE INDEX idx_unified_risk_rules_active ON public.risk_rules USING btree (is_active, enabled);
CREATE INDEX idx_unified_risk_rules_execution ON public.risk_rules USING btree (enabled, is_active, priority DESC, category);

-- 约束

ALTER TABLE "public"."risk_rules" ADD CONSTRAINT "2200_17874_1_not_null" CHECK id IS NOT NULL;
ALTER TABLE "public"."risk_rules" ADD CONSTRAINT "2200_17874_2_not_null" CHECK name IS NOT NULL;
ALTER TABLE "public"."risk_rules" ADD CONSTRAINT "2200_17874_4_not_null" CHECK category IS NOT NULL;
ALTER TABLE "public"."risk_rules" ADD CONSTRAINT "2200_17874_5_not_null" CHECK rule_type IS NOT NULL;
ALTER TABLE "public"."risk_rules" ADD CONSTRAINT "2200_17874_6_not_null" CHECK parameters IS NOT NULL;
ALTER TABLE "public"."risk_rules" ADD CONSTRAINT "2200_17874_8_not_null" CHECK enabled IS NOT NULL;
ALTER TABLE "public"."risk_rules" ADD CONSTRAINT "2200_17874_9_not_null" CHECK priority IS NOT NULL;
ALTER TABLE "public"."risk_rules" ADD CONSTRAINT "2200_17874_16_not_null" CHECK created_at IS NOT NULL;
ALTER TABLE "public"."risk_rules" ADD CONSTRAINT "2200_17874_17_not_null" CHECK updated_at IS NOT NULL;
ALTER TABLE "public"."risk_rules" ADD CONSTRAINT "2200_17874_20_not_null" CHECK version IS NOT NULL;
ALTER TABLE "public"."risk_rules" ADD CONSTRAINT "2200_17874_21_not_null" CHECK is_active IS NOT NULL;

ALTER TABLE "public"."risk_rules" OWNER TO "neondb_owner";