//! 规则引擎实现

use async_trait::async_trait;
use std::collections::HashMap;
use std::sync::Arc;
use std::time::Instant;
use tracing::{debug, warn, error};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use serde::{Serialize, Deserialize};

use crate::types::*;
use sigmax_core::{SigmaXResult, SigmaXError, Order};

/// 规则引擎接口
#[async_trait]
pub trait RuleEngine: Send + Sync {
    /// 执行规则检查
    async fn execute_rules(
        &self,
        rules: &[RiskRule],
        context: &RuleExecutionContext,
    ) -> SigmaXResult<Vec<RuleExecutionResult>>;
    
    /// 获取适用的规则
    async fn get_applicable_rules(
        &self,
        context: &RiskContext,
    ) -> SigmaXResult<Vec<RiskRule>>;
    
    /// 验证规则参数
    fn validate_rule_parameters(&self, rule: &RiskRule) -> SigmaXResult<()>;
}

/// 规则执行上下文
#[derive(Debug, Clone)]
pub struct RuleExecutionContext {
    pub order: Option<Order>,
    pub positions: Option<Vec<Position>>,
    pub portfolio: Option<PortfolioState>,
    pub market_data: Option<MarketData>,
    pub risk_context: RiskContext,
}

/// 市场数据
#[derive(Debug, Clone)]
pub struct MarketData {
    pub price: rust_decimal::Decimal,
    pub volume_24h: rust_decimal::Decimal,
    pub volatility: f64,
    pub bid_ask_spread: f64,
    pub timestamp: DateTime<Utc>,
}

/// 规则执行结果
#[derive(Debug, Clone)]
pub struct RuleExecutionResult {
    pub rule_id: Uuid,
    pub rule_name: String,
    pub result: RuleResult,
    pub execution_time_ms: u64,
    pub details: Option<String>,
    pub risk_contribution: f64,
}

/// 规则执行结果枚举
#[derive(Debug, Clone)]
pub enum RuleResult {
    Pass,
    Fail { reason: String, severity: RiskLevel },
    Skip { reason: String },
    Error { error: String },
}

/// 规则仓储接口
#[async_trait]
pub trait RuleRepository: Send + Sync {
    /// 获取所有启用的规则
    async fn get_enabled_rules(&self) -> SigmaXResult<Vec<RiskRule>>;
    
    /// 根据策略类型获取规则
    async fn get_rules_by_strategy(&self, strategy_type: &str) -> SigmaXResult<Vec<RiskRule>>;
    
    /// 根据交易对获取规则
    async fn get_rules_by_trading_pair(&self, trading_pair: &str) -> SigmaXResult<Vec<RiskRule>>;
    
    /// 保存规则执行记录
    async fn save_execution_record(&self, record: &RuleExecutionRecord) -> SigmaXResult<()>;
}

/// 规则执行记录
#[derive(Debug, Clone)]
pub struct RuleExecutionRecord {
    pub id: Uuid,
    pub rule_id: Uuid,
    pub execution_time: DateTime<Utc>,
    pub result: RuleResult,
    pub execution_time_ms: u64,
    pub context_summary: String,
}

/// 规则执行器接口
pub trait RuleExecutor: Send + Sync {
    /// 执行规则
    fn execute(
        &self,
        rule: &RiskRule,
        context: &RuleExecutionContext,
    ) -> SigmaXResult<RuleResult>;
    
    /// 验证规则参数
    fn validate_parameters(&self, parameters: &RuleParameters) -> SigmaXResult<()>;
}

/// 默认规则引擎实现
pub struct DefaultRuleEngine {
    /// 规则仓储
    repository: Arc<dyn RuleRepository>,
    /// 规则执行器映射
    executors: HashMap<RuleType, Box<dyn RuleExecutor>>,
    /// 配置
    config: RuleEngineConfig,
}

/// 规则引擎配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RuleEngineConfig {
    /// 最大规则执行时间（毫秒）
    pub max_rule_execution_time_ms: u64,
    /// 是否启用规则缓存
    pub enable_rule_cache: bool,
    /// 缓存过期时间（秒）
    pub cache_expiry_seconds: u64,
}

impl Default for RuleEngineConfig {
    fn default() -> Self {
        Self {
            max_rule_execution_time_ms: 1000,
            enable_rule_cache: true,
            cache_expiry_seconds: 300,
        }
    }
}

impl DefaultRuleEngine {
    /// 创建新的规则引擎
    pub fn new(
        repository: Arc<dyn RuleRepository>,
        config: Option<RuleEngineConfig>,
    ) -> Self {
        let mut engine = Self {
            repository,
            executors: HashMap::new(),
            config: config.unwrap_or_default(),
        };
        
        // 注册默认的规则执行器
        engine.register_default_executors();
        engine
    }
    
    /// 注册默认的规则执行器
    fn register_default_executors(&mut self) {
        self.executors.insert(RuleType::PositionLimit, Box::new(PositionLimitExecutor));
        self.executors.insert(RuleType::OrderSize, Box::new(OrderSizeExecutor));
        self.executors.insert(RuleType::DailyLoss, Box::new(DailyLossExecutor));
        self.executors.insert(RuleType::Volatility, Box::new(VolatilityExecutor));
        self.executors.insert(RuleType::Concentration, Box::new(ConcentrationExecutor));
        self.executors.insert(RuleType::Liquidity, Box::new(LiquidityExecutor));
        self.executors.insert(RuleType::TimeWindow, Box::new(TimeWindowExecutor));
    }
    
    /// 注册自定义规则执行器
    pub fn register_executor(&mut self, rule_type: RuleType, executor: Box<dyn RuleExecutor>) {
        self.executors.insert(rule_type, executor);
    }
    
    /// 过滤适用的规则
    fn filter_applicable_rules(&self, rules: Vec<RiskRule>, context: &RiskContext) -> Vec<RiskRule> {
        rules.into_iter()
            .filter(|rule| {
                // 检查策略类型匹配
                if let Some(strategy_type) = &context.strategy_type {
                    if !rule.strategy_types.is_empty() && !rule.strategy_types.contains(strategy_type) {
                        return false;
                    }
                }
                
                // 检查交易对匹配
                if let Some(trading_pair) = &context.trading_pair {
                    let pair_str = format!("{}_{}", trading_pair.base, trading_pair.quote);
                    if !rule.trading_pairs.is_empty() && !rule.trading_pairs.contains(&pair_str) {
                        return false;
                    }
                }
                
                // 检查规则是否启用
                rule.enabled
            })
            .collect()
    }
}

#[async_trait]
impl RuleEngine for DefaultRuleEngine {
    async fn execute_rules(
        &self,
        rules: &[RiskRule],
        context: &RuleExecutionContext,
    ) -> SigmaXResult<Vec<RuleExecutionResult>> {
        let mut results = Vec::with_capacity(rules.len());
        
        for rule in rules {
            let start_time = Instant::now();
            
            // 获取对应的执行器
            let executor = match self.executors.get(&rule.rule_type) {
                Some(executor) => executor,
                None => {
                    warn!("未找到规则类型 {:?} 的执行器", rule.rule_type);
                    results.push(RuleExecutionResult {
                        rule_id: rule.id,
                        rule_name: rule.name.clone(),
                        result: RuleResult::Error {
                            error: format!("未找到规则类型 {:?} 的执行器", rule.rule_type),
                        },
                        execution_time_ms: 0,
                        details: None,
                        risk_contribution: 0.0,
                    });
                    continue;
                }
            };
            
            // 执行规则
            let result = match executor.execute(rule, context) {
                Ok(result) => result,
                Err(e) => {
                    error!("规则 {} 执行失败: {}", rule.name, e);
                    RuleResult::Error {
                        error: format!("规则执行失败: {}", e),
                    }
                }
            };
            
            let execution_time_ms = start_time.elapsed().as_millis() as u64;
            
            // 计算风险贡献度
            let risk_contribution = match &result {
                RuleResult::Fail { severity, .. } => {
                    match severity {
                        RiskLevel::Low => 0.25,
                        RiskLevel::Medium => 0.5,
                        RiskLevel::High => 0.75,
                        RiskLevel::Critical => 1.0,
                    }
                }
                _ => 0.0,
            };
            
            results.push(RuleExecutionResult {
                rule_id: rule.id,
                rule_name: rule.name.clone(),
                result,
                execution_time_ms,
                details: None,
                risk_contribution,
            });
            
            // 记录执行历史（异步，不阻塞主流程）
            let record = RuleExecutionRecord {
                id: Uuid::new_v4(),
                rule_id: rule.id,
                execution_time: Utc::now(),
                result: results.last().unwrap().result.clone(),
                execution_time_ms,
                context_summary: format!("策略: {:?}, 交易对: {:?}", 
                                       context.risk_context.strategy_type,
                                       context.risk_context.trading_pair),
            };
            
            if let Err(e) = self.repository.save_execution_record(&record).await {
                warn!("保存规则执行记录失败: {}", e);
            }
        }
        
        Ok(results)
    }
    
    async fn get_applicable_rules(&self, context: &RiskContext) -> SigmaXResult<Vec<RiskRule>> {
        // 获取所有启用的规则
        let all_rules = self.repository.get_enabled_rules().await?;
        
        // 过滤适用的规则
        let applicable_rules = self.filter_applicable_rules(all_rules, context);
        
        // 按优先级排序
        let mut sorted_rules = applicable_rules;
        sorted_rules.sort_by(|a, b| b.priority.cmp(&a.priority));
        
        debug!("找到 {} 个适用的规则", sorted_rules.len());
        Ok(sorted_rules)
    }
    
    fn validate_rule_parameters(&self, rule: &RiskRule) -> SigmaXResult<()> {
        if let Some(executor) = self.executors.get(&rule.rule_type) {
            executor.validate_parameters(&rule.parameters)
        } else {
            Err(SigmaXError::ValidationError(
                format!("未找到规则类型 {:?} 的执行器", rule.rule_type)
            ))
        }
    }
}

// ============================================================================
// 具体规则执行器实现（简化版本）
// ============================================================================

/// 持仓限制规则执行器
struct PositionLimitExecutor;

impl RuleExecutor for PositionLimitExecutor {
    fn execute(&self, rule: &RiskRule, context: &RuleExecutionContext) -> SigmaXResult<RuleResult> {
        if let RuleParameters::PositionLimit { max_position_ratio, max_absolute_amount: _ } = &rule.parameters {
            // 简化的持仓检查逻辑
            if let Some(portfolio) = &context.portfolio {
                let total_value = portfolio.total_value;
                
                // 计算当前持仓比例（简化计算）
                let current_position_ratio = if total_value > rust_decimal::Decimal::ZERO {
                    0.2 // 模拟当前持仓比例
                } else {
                    0.0
                };
                
                if current_position_ratio > *max_position_ratio {
                    return Ok(RuleResult::Fail {
                        reason: format!("持仓比例 {:.2}% 超过限制 {:.2}%", 
                                      current_position_ratio * 100.0, 
                                      max_position_ratio * 100.0),
                        severity: RiskLevel::High,
                    });
                }
            }
            
            Ok(RuleResult::Pass)
        } else {
            Ok(RuleResult::Error {
                error: "规则参数类型不匹配".to_string(),
            })
        }
    }
    
    fn validate_parameters(&self, parameters: &RuleParameters) -> SigmaXResult<()> {
        if let RuleParameters::PositionLimit { max_position_ratio, .. } = parameters {
            if *max_position_ratio <= 0.0 || *max_position_ratio > 1.0 {
                return Err(SigmaXError::ValidationError(
                    "持仓比例限制必须在 0-1 之间".to_string()
                ));
            }
        }
        Ok(())
    }
}

// 其他规则执行器的简化实现
struct OrderSizeExecutor;
struct DailyLossExecutor;
struct VolatilityExecutor;
struct ConcentrationExecutor;
struct LiquidityExecutor;
struct TimeWindowExecutor;

// 为简化，这里只提供基本的实现框架
impl RuleExecutor for OrderSizeExecutor {
    fn execute(&self, _rule: &RiskRule, _context: &RuleExecutionContext) -> SigmaXResult<RuleResult> {
        Ok(RuleResult::Pass) // 简化实现
    }
    fn validate_parameters(&self, _parameters: &RuleParameters) -> SigmaXResult<()> { Ok(()) }
}

impl RuleExecutor for DailyLossExecutor {
    fn execute(&self, _rule: &RiskRule, _context: &RuleExecutionContext) -> SigmaXResult<RuleResult> {
        Ok(RuleResult::Pass) // 简化实现
    }
    fn validate_parameters(&self, _parameters: &RuleParameters) -> SigmaXResult<()> { Ok(()) }
}

impl RuleExecutor for VolatilityExecutor {
    fn execute(&self, _rule: &RiskRule, _context: &RuleExecutionContext) -> SigmaXResult<RuleResult> {
        Ok(RuleResult::Pass) // 简化实现
    }
    fn validate_parameters(&self, _parameters: &RuleParameters) -> SigmaXResult<()> { Ok(()) }
}

impl RuleExecutor for ConcentrationExecutor {
    fn execute(&self, _rule: &RiskRule, _context: &RuleExecutionContext) -> SigmaXResult<RuleResult> {
        Ok(RuleResult::Pass) // 简化实现
    }
    fn validate_parameters(&self, _parameters: &RuleParameters) -> SigmaXResult<()> { Ok(()) }
}

impl RuleExecutor for LiquidityExecutor {
    fn execute(&self, _rule: &RiskRule, _context: &RuleExecutionContext) -> SigmaXResult<RuleResult> {
        Ok(RuleResult::Pass) // 简化实现
    }
    fn validate_parameters(&self, _parameters: &RuleParameters) -> SigmaXResult<()> { Ok(()) }
}

impl RuleExecutor for TimeWindowExecutor {
    fn execute(&self, _rule: &RiskRule, _context: &RuleExecutionContext) -> SigmaXResult<RuleResult> {
        Ok(RuleResult::Pass) // 简化实现
    }
    fn validate_parameters(&self, _parameters: &RuleParameters) -> SigmaXResult<()> { Ok(()) }
}
