//! 风险管理数据仓储层

use async_trait::async_trait;
#[cfg(feature = "database")]
use sqlx::{PgPool, Row};
use std::collections::HashMap;
use tracing::{debug, error, info};
use uuid::Uuid;

use crate::types::*;
use crate::rules::{RuleRepository, RuleExecutionRecord};
use sigmax_core::{SigmaXResult, SigmaXError};

/// SQL 数据库仓储实现
#[cfg(feature = "database")]
pub struct SqlRuleRepository {
    pool: PgPool,
}

#[cfg(feature = "database")]
impl SqlRuleRepository {
    /// 创建新的 SQL 仓储实例
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    /// 将数据库行转换为风险规则
    fn row_to_risk_rule(&self, row: &sqlx::postgres::PgRow) -> SigmaXResult<RiskRule> {
        let id: Uuid = row.try_get("id")?;
        let name: String = row.try_get("name")?;
        let description: Option<String> = row.try_get("description")?;
        let rule_type_str: String = row.try_get("rule_type")?;
        let parameters_json: serde_json::Value = row.try_get("parameters")?;
        let conditions_json: Option<serde_json::Value> = row.try_get("conditions")?;
        let enabled: bool = row.try_get("enabled")?;
        let priority: i32 = row.try_get("priority")?;
        let strategy_types: Vec<String> = row.try_get("strategy_types")?;
        let trading_pairs: Vec<String> = row.try_get("trading_pairs")?;
        let created_at: chrono::DateTime<chrono::Utc> = row.try_get("created_at")?;
        let updated_at: chrono::DateTime<chrono::Utc> = row.try_get("updated_at")?;

        // 解析规则类型
        let rule_type = self.parse_rule_type(&rule_type_str)?;
        
        // 解析规则参数
        let parameters = self.parse_rule_parameters(&rule_type_str, &parameters_json)?;
        
        // 解析规则条件
        let conditions = if let Some(conditions_json) = conditions_json {
            serde_json::from_value(conditions_json)
                .map_err(|e| SigmaXError::SerializationError(format!("解析规则条件失败: {}", e)))?
        } else {
            RuleConditions {
                market_conditions: None,
                strategy_conditions: None,
                time_conditions: None,
                custom_conditions: None,
            }
        };

        Ok(RiskRule {
            id,
            name,
            description,
            rule_type,
            parameters,
            conditions,
            enabled,
            priority,
            strategy_types,
            trading_pairs,
            created_at,
            updated_at,
        })
    }

    /// 解析规则类型
    fn parse_rule_type(&self, rule_type_str: &str) -> SigmaXResult<RuleType> {
        match rule_type_str {
            "position_limit" => Ok(RuleType::PositionLimit),
            "order_size" => Ok(RuleType::OrderSize),
            "daily_loss" => Ok(RuleType::DailyLoss),
            "volatility" => Ok(RuleType::Volatility),
            "concentration" => Ok(RuleType::Concentration),
            "liquidity" => Ok(RuleType::Liquidity),
            "time_window" => Ok(RuleType::TimeWindow),
            _ => Ok(RuleType::Custom(rule_type_str.to_string())),
        }
    }

    /// 解析规则参数
    fn parse_rule_parameters(&self, rule_type: &str, params_json: &serde_json::Value) -> SigmaXResult<RuleParameters> {
        match rule_type {
            "position_limit" => {
                let max_ratio = params_json.get("max_position_ratio")
                    .and_then(|v| v.as_f64())
                    .unwrap_or(0.8);
                let max_amount = params_json.get("max_absolute_amount")
                    .and_then(|v| v.as_f64())
                    .map(rust_decimal::Decimal::from_f64_retain)
                    .flatten();
                
                Ok(RuleParameters::PositionLimit {
                    max_position_ratio: max_ratio,
                    max_absolute_amount: max_amount,
                })
            }
            "order_size" => {
                let max_ratio = params_json.get("max_order_ratio")
                    .and_then(|v| v.as_f64())
                    .unwrap_or(0.1);
                let max_size = params_json.get("max_absolute_size")
                    .and_then(|v| v.as_f64())
                    .map(rust_decimal::Decimal::from_f64_retain)
                    .flatten();
                let min_size = params_json.get("min_order_size")
                    .and_then(|v| v.as_f64())
                    .map(rust_decimal::Decimal::from_f64_retain)
                    .flatten();
                
                Ok(RuleParameters::OrderSize {
                    max_order_ratio: max_ratio,
                    max_absolute_size: max_size,
                    min_order_size: min_size,
                })
            }
            "daily_loss" => {
                let max_ratio = params_json.get("max_loss_ratio")
                    .and_then(|v| v.as_f64())
                    .unwrap_or(0.05);
                let max_loss = params_json.get("max_absolute_loss")
                    .and_then(|v| v.as_f64())
                    .map(rust_decimal::Decimal::from_f64_retain)
                    .flatten();
                let reset_time = params_json.get("reset_time")
                    .and_then(|v| v.as_str())
                    .unwrap_or("00:00:00")
                    .to_string();
                
                Ok(RuleParameters::DailyLoss {
                    max_loss_ratio: max_ratio,
                    max_absolute_loss: max_loss,
                    reset_time,
                })
            }
            "volatility" => {
                let max_vol = params_json.get("max_volatility")
                    .and_then(|v| v.as_f64())
                    .unwrap_or(0.3);
                let lookback = params_json.get("lookback_period_days")
                    .and_then(|v| v.as_u64())
                    .unwrap_or(30) as u32;
                
                Ok(RuleParameters::Volatility {
                    max_volatility: max_vol,
                    lookback_period_days: lookback,
                })
            }
            _ => {
                // 对于未知类型，使用自定义参数
                let mut params = HashMap::new();
                if let serde_json::Value::Object(map) = params_json {
                    for (k, v) in map {
                        params.insert(k.clone(), v.clone());
                    }
                }
                Ok(RuleParameters::Custom { parameters: params })
            }
        }
    }
}

#[cfg(feature = "database")]
#[async_trait]
impl RuleRepository for SqlRuleRepository {
    async fn get_enabled_rules(&self) -> SigmaXResult<Vec<RiskRule>> {
        debug!("获取所有启用的风险规则");

        let query = r#"
            SELECT id, name, description, rule_type, parameters, conditions, 
                   enabled, priority, strategy_types, trading_pairs, 
                   created_at, updated_at
            FROM unified_risk_rules_new 
            WHERE enabled = true 
            ORDER BY priority DESC
        "#;

        let rows = sqlx::query(query)
            .fetch_all(&self.pool)
            .await
            .map_err(|e| SigmaXError::DatabaseError(format!("查询启用规则失败: {}", e)))?;

        let mut rules = Vec::new();
        for row in rows {
            match self.row_to_risk_rule(&row) {
                Ok(rule) => rules.push(rule),
                Err(e) => {
                    error!("解析规则失败: {}", e);
                    continue;
                }
            }
        }

        info!("成功获取 {} 个启用的风险规则", rules.len());
        Ok(rules)
    }

    async fn get_rules_by_strategy(&self, strategy_type: &str) -> SigmaXResult<Vec<RiskRule>> {
        debug!("获取策略 {} 的风险规则", strategy_type);

        let query = r#"
            SELECT id, name, description, rule_type, parameters, conditions, 
                   enabled, priority, strategy_types, trading_pairs, 
                   created_at, updated_at
            FROM unified_risk_rules_new 
            WHERE enabled = true 
              AND (strategy_types = '{}' OR $1 = ANY(strategy_types))
            ORDER BY priority DESC
        "#;

        let rows = sqlx::query(query)
            .bind(strategy_type)
            .fetch_all(&self.pool)
            .await
            .map_err(|e| SigmaXError::DatabaseError(format!("查询策略规则失败: {}", e)))?;

        let mut rules = Vec::new();
        for row in rows {
            match self.row_to_risk_rule(&row) {
                Ok(rule) => rules.push(rule),
                Err(e) => {
                    error!("解析规则失败: {}", e);
                    continue;
                }
            }
        }

        info!("成功获取策略 {} 的 {} 个风险规则", strategy_type, rules.len());
        Ok(rules)
    }

    async fn get_rules_by_trading_pair(&self, trading_pair: &str) -> SigmaXResult<Vec<RiskRule>> {
        debug!("获取交易对 {} 的风险规则", trading_pair);

        let query = r#"
            SELECT id, name, description, rule_type, parameters, conditions, 
                   enabled, priority, strategy_types, trading_pairs, 
                   created_at, updated_at
            FROM unified_risk_rules_new 
            WHERE enabled = true 
              AND (trading_pairs = '{}' OR $1 = ANY(trading_pairs))
            ORDER BY priority DESC
        "#;

        let rows = sqlx::query(query)
            .bind(trading_pair)
            .fetch_all(&self.pool)
            .await
            .map_err(|e| SigmaXError::DatabaseError(format!("查询交易对规则失败: {}", e)))?;

        let mut rules = Vec::new();
        for row in rows {
            match self.row_to_risk_rule(&row) {
                Ok(rule) => rules.push(rule),
                Err(e) => {
                    error!("解析规则失败: {}", e);
                    continue;
                }
            }
        }

        info!("成功获取交易对 {} 的 {} 个风险规则", trading_pair, rules.len());
        Ok(rules)
    }

    async fn save_execution_record(&self, record: &RuleExecutionRecord) -> SigmaXResult<()> {
        debug!("保存规则执行记录: {}", record.rule_id);

        let result_str = match &record.result {
            crate::rules::RuleResult::Pass => "pass",
            crate::rules::RuleResult::Fail { .. } => "fail",
            crate::rules::RuleResult::Skip { .. } => "skip",
            crate::rules::RuleResult::Error { .. } => "error",
        };

        let query = r#"
            INSERT INTO rule_execution_history_new 
            (id, rule_id, execution_time, result, execution_time_ms, context_summary)
            VALUES ($1, $2, $3, $4, $5, $6)
        "#;

        sqlx::query(query)
            .bind(record.id)
            .bind(record.rule_id)
            .bind(record.execution_time)
            .bind(result_str)
            .bind(record.execution_time_ms as i64)
            .bind(&record.context_summary)
            .execute(&self.pool)
            .await
            .map_err(|e| SigmaXError::DatabaseError(format!("保存执行记录失败: {}", e)))?;

        debug!("成功保存规则执行记录");
        Ok(())
    }
}

/// 内存中的规则仓储实现（用于测试）
pub struct InMemoryRuleRepository {
    rules: std::sync::RwLock<Vec<RiskRule>>,
}

impl InMemoryRuleRepository {
    pub fn new() -> Self {
        Self {
            rules: std::sync::RwLock::new(Vec::new()),
        }
    }

    pub fn add_rule(&self, rule: RiskRule) {
        let mut rules = self.rules.write().unwrap();
        rules.push(rule);
    }

    pub fn clear(&self) {
        let mut rules = self.rules.write().unwrap();
        rules.clear();
    }
}

#[async_trait]
impl RuleRepository for InMemoryRuleRepository {
    async fn get_enabled_rules(&self) -> SigmaXResult<Vec<RiskRule>> {
        let rules = self.rules.read().unwrap();
        Ok(rules.iter().filter(|r| r.enabled).cloned().collect())
    }

    async fn get_rules_by_strategy(&self, strategy_type: &str) -> SigmaXResult<Vec<RiskRule>> {
        let rules = self.rules.read().unwrap();
        Ok(rules.iter()
            .filter(|r| r.enabled && (r.strategy_types.is_empty() || r.strategy_types.contains(&strategy_type.to_string())))
            .cloned()
            .collect())
    }

    async fn get_rules_by_trading_pair(&self, trading_pair: &str) -> SigmaXResult<Vec<RiskRule>> {
        let rules = self.rules.read().unwrap();
        Ok(rules.iter()
            .filter(|r| r.enabled && (r.trading_pairs.is_empty() || r.trading_pairs.contains(&trading_pair.to_string())))
            .cloned()
            .collect())
    }

    async fn save_execution_record(&self, _record: &RuleExecutionRecord) -> SigmaXResult<()> {
        // 内存实现不保存执行记录
        Ok(())
    }
}
