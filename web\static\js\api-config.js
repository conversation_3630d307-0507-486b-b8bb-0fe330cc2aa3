// SigmaX Trading System - 统一API配置中心
// 这是唯一的API配置文件，所有其他文件都应该从这里获取配置

/**
 * 统一API配置中心
 * 所有API相关的配置都在这里定义，其他文件不应该重复定义
 */
window.SigmaXAPIConfig = {
    // 基础配置
    host: '127.0.0.1',
    port: 8080,
    protocol: 'http',
    apiPath: '/api/v1',  // 统一使用 /api/v1 (与后端保持一致)

    // 计算属性 - 完整URL
    get baseUrl() {
        return `${this.protocol}://${this.host}:${this.port}`;
    },

    get apiBaseUrl() {
        return `${this.baseUrl}${this.apiPath}`;
    },

    // API端点定义
    endpoints: {
        // 系统相关
        health: '/health',
        status: '/status',

        // 回测相关
        backtestFiles: '/backtest/files',
        backtestFileValidate: '/backtest/files/{filename}/validate',
        backtestStats: '/backtest/stats',

        // 引擎相关
        engines: '/engines',
        engineConfig: '/engines/{engineId}/backtest/config',
        engineStart: '/engines/{engineId}/start',
        engineStop: '/engines/{engineId}/stop',
        engineStatus: '/engines/{engineId}/status',
        backtestProgress: '/engines/{engineId}/backtest/progress',

        // 策略相关
        strategies: '/strategies',
        strategyCreate: '/strategies',
        strategyUpdate: '/strategies/{strategyId}',
        strategyDelete: '/strategies/{strategyId}',

        // 交易相关
        orders: '/orders',
        trades: '/trades',
        positions: '/positions',

        // 风险管理
        riskConfig: '/risk/config',
        riskRules: '/risk/rules',

        // 市场数据
        marketData: '/market/data',
        klines: '/market/klines',

        // 系统配置
        systemConfig: '/config',
        tradingConfig: '/config/trading',
        notificationConfig: '/config/notifications'
    },

    // 请求配置
    request: {
        timeout: 30000,
        retryAttempts: 3,
        retryInterval: 2000
    },

    /**
     * 获取完整的API端点URL
     * @param {string} endpoint 端点名称或路径
     * @param {Object} params URL参数替换
     * @returns {string} 完整的API URL
     */
    getUrl(endpoint, params = {}) {
        console.log(`🔍 getUrl调用: endpoint="${endpoint}", type=${typeof endpoint}`);
        console.log(`🔍 endpoint.startsWith('/')=${endpoint && endpoint.startsWith && endpoint.startsWith('/')}`);

        let endpointPath;

        // 简化逻辑：优先支持直接路径
        if (endpoint && typeof endpoint === 'string' && endpoint.startsWith('/')) {
            // 直接传入路径，如 '/backtest/files'
            endpointPath = endpoint;
            console.log(`🔄 兼容模式：直接使用路径 ${endpoint}`);
        } else if (endpoint && this.endpoints && this.endpoints[endpoint]) {
            // 传入端点名称，如 'backtestFiles'
            endpointPath = this.endpoints[endpoint];
            console.log(`✅ 标准模式：${endpoint} -> ${endpointPath}`);
        } else {
            // 错误处理
            console.error(`❌ 未知的API端点: ${endpoint}`);
            console.log('📋 传入的端点:', endpoint);
            console.log('📋 端点类型:', typeof endpoint);
            console.log('� this.endpoints存在:', !!this.endpoints);
            if (this.endpoints) {
                console.log('📋 可用端点:', Object.keys(this.endpoints));
                console.log('📋 可用路径:', Object.values(this.endpoints));
            }

            // 作为最后的尝试，直接使用传入的值
            if (endpoint && typeof endpoint === 'string') {
                console.log(`� 最后尝试：直接使用传入值 ${endpoint}`);
                endpointPath = endpoint.startsWith('/') ? endpoint : '/' + endpoint;
            } else {
                throw new Error(`未知的API端点: ${endpoint}`);
            }
        }

        let url = this.apiBaseUrl + endpointPath;

        // 替换URL参数
        if (params && typeof params === 'object') {
            for (const [key, value] of Object.entries(params)) {
                url = url.replace(`{${key}}`, encodeURIComponent(value));
            }
        }

        console.log(`🔗 生成URL: ${endpoint} -> ${url}`);
        return url;
    },

    /**
     * 获取健康检查URL
     * @returns {string} 健康检查URL
     */
    getHealthUrl() {
        return this.getUrl('health');
    },

    /**
     * 获取回测文件列表URL
     * @returns {string} 回测文件列表URL
     */
    getBacktestFilesUrl() {
        return this.getUrl('backtestFiles');
    },

    /**
     * 获取引擎配置URL
     * @param {string} engineId 引擎ID
     * @returns {string} 引擎配置URL
     */
    getEngineConfigUrl(engineId) {
        return this.getUrl('engineConfig', { engineId });
    },

    /**
     * 验证URL格式
     * @param {string} url URL字符串
     * @returns {boolean} 是否为有效URL
     */
    isValidUrl(url) {
        try {
            new URL(url);
            return true;
        } catch {
            return false;
        }
    },

    /**
     * 设置环境配置
     * @param {Object} config 环境配置
     */
    setEnvironment(config) {
        if (config.host) this.host = config.host;
        if (config.port) this.port = config.port;
        if (config.protocol) this.protocol = config.protocol;
        if (config.apiPath) this.apiPath = config.apiPath;

        console.log('🔧 API配置已更新:', {
            host: this.host,
            port: this.port,
            protocol: this.protocol,
            apiPath: this.apiPath,
            apiBaseUrl: this.apiBaseUrl
        });
    },

    /**
     * 从URL参数加载配置
     */
    loadFromUrlParams() {
        const params = new URLSearchParams(window.location.search);

        const config = {};
        if (params.has('host')) config.host = params.get('host');
        if (params.has('port')) config.port = parseInt(params.get('port'));
        if (params.has('protocol')) config.protocol = params.get('protocol');
        if (params.has('api_path')) config.apiPath = params.get('api_path');

        if (Object.keys(config).length > 0) {
            this.setEnvironment(config);
            console.log('📋 从URL参数加载配置:', config);
        }
    },

    /**
     * 从localStorage加载配置
     */
    loadFromStorage() {
        try {
            const saved = localStorage.getItem('sigmaxApiConfig');
            if (saved) {
                const config = JSON.parse(saved);
                this.setEnvironment(config);
                console.log('💾 从localStorage加载配置:', config);
            }
        } catch (error) {
            console.warn('⚠️ 加载保存的配置失败:', error);
        }
    },

    /**
     * 保存配置到localStorage
     */
    saveToStorage() {
        try {
            const config = {
                host: this.host,
                port: this.port,
                protocol: this.protocol,
                apiPath: this.apiPath
            };
            localStorage.setItem('sigmaxApiConfig', JSON.stringify(config));
            console.log('💾 配置已保存到localStorage');
        } catch (error) {
            console.warn('⚠️ 保存配置失败:', error);
        }
    },

    /**
     * 重置为默认配置
     */
    reset() {
        this.host = '127.0.0.1';
        this.port = 8080;
        this.protocol = 'http';
        this.apiPath = '/api/v1';
        localStorage.removeItem('sigmaxApiConfig');
        console.log('🔄 API配置已重置为默认值');
    },

    /**
     * 获取当前配置信息
     * @returns {Object} 当前配置
     */
    getConfig() {
        return {
            host: this.host,
            port: this.port,
            protocol: this.protocol,
            apiPath: this.apiPath,
            baseUrl: this.baseUrl,
            apiBaseUrl: this.apiBaseUrl
        };
    },

    /**
     * 验证配置有效性
     * @returns {boolean} 配置是否有效
     */
    validate() {
        const errors = [];

        if (!this.host) errors.push('主机地址不能为空');
        if (!this.port || this.port < 1 || this.port > 65535) errors.push('端口号无效');
        if (!['http', 'https'].includes(this.protocol)) errors.push('协议必须是http或https');
        if (!this.apiPath.startsWith('/')) errors.push('API路径必须以/开头');

        if (errors.length > 0) {
            console.error('❌ API配置验证失败:', errors);
            return false;
        }

        return true;
    },

    /**
     * 初始化配置
     */
    init() {
        console.log('🚀 初始化SigmaX API配置中心...');

        // 加载顺序：默认值 -> localStorage -> URL参数
        this.loadFromStorage();
        this.loadFromUrlParams();

        // 验证配置
        if (!this.validate()) {
            console.warn('⚠️ 配置验证失败，使用默认配置');
            this.reset();
        }

        console.log('✅ API配置中心初始化完成:', this.getConfig());
    }
};

// 立即初始化（不等待DOM）
window.SigmaXAPIConfig.init();

// 也在DOM加载完成后再次初始化（确保万无一失）
document.addEventListener('DOMContentLoaded', () => {
    if (!window.SigmaXAPIConfig.host) {
        window.SigmaXAPIConfig.init();
    }
});

// 兼容性别名 - 逐步迁移其他代码
window.APIConfig = window.SigmaXAPIConfig;

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = window.SigmaXAPIConfig;
}
