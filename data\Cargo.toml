[package]
name = "sigmax-data"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
repository.workspace = true

[dependencies]
sigmax-core.workspace = true
sigmax-database = { path = "../database" }
tokio.workspace = true
serde.workspace = true
serde_json.workspace = true
chrono.workspace = true
anyhow.workspace = true
tracing.workspace = true
sqlx.workspace = true
async-trait = "0.1"
uuid.workspace = true
rust_decimal.workspace = true

[dev-dependencies]
tempfile = "3.0"
