//! 风险配置服务
//!
//! 专门负责风险管理相关配置的管理

use std::sync::Arc;
use std::sync::atomic::{AtomicBool, Ordering};
use async_trait::async_trait;
use tracing::{info, error};

use crate::error::{ApiError, ApiResult};
use super::traits::{ConfigService, ConfigStatistics};
use sigmax_core::{RiskManagementConfig, ConfigCache};
use sigmax_database::DatabaseManager;
use sigmax_database::repositories::sqlx::SqlRiskRepository;
use sigmax_database::repositories::traits::RiskRepository;

/// 风险配置服务
///
/// 负责管理风险管理相关配置，包括：
/// - 最大回撤限制
/// - 日损失限制
/// - 投资组合风险限制
/// - 持仓大小限制
/// - 止损设置
#[derive(Clone)]
pub struct RiskConfigService {
    /// 风险仓储 - 使用专门的风险仓储
    repository: Arc<SqlRiskRepository>,
    /// 配置缓存
    cache: Arc<ConfigCache>,
    /// 初始化状态
    is_initialized: Arc<AtomicBool>,
}

impl RiskConfigService {
    /// 创建新的风险配置服务
    pub async fn new(database: Arc<DatabaseManager>) -> ApiResult<Self> {
        let repository = Arc::new(SqlRiskRepository::new(database));
        let cache = Arc::new(ConfigCache::new(std::time::Duration::from_secs(300))); // 5分钟缓存

        Ok(Self {
            repository,
            cache,
            is_initialized: Arc::new(AtomicBool::new(false)),
        })
    }

    /// 获取风险配置
    pub async fn get_config(&self) -> ApiResult<RiskManagementConfig> {
        if !self.is_initialized() {
            return Err(ApiError::BadRequest("RiskConfigService not initialized".to_string()));
        }

        self.cache.get_or_load_risk_config(|| {
            self.repository.get_risk_config()
        })
        .await
        .map_err(|e| {
            error!("Failed to get risk config: {}", e);
            ApiError::from(e)
        })
    }

    /// 根据ID获取风险配置
    pub async fn get_config_by_id(&self, id: uuid::Uuid) -> ApiResult<Option<RiskManagementConfig>> {
        if !self.is_initialized() {
            return Err(ApiError::BadRequest("RiskConfigService not initialized".to_string()));
        }

        self.repository
            .get_risk_config_by_id(id)
            .await
            .map_err(|e| {
                error!("Failed to get risk config by id {}: {}", id, e);
                ApiError::from(e)
            })
    }

    /// 根据策略类型获取风险配置
    pub async fn get_config_by_strategy_type(&self, strategy_type: &str) -> ApiResult<Option<RiskManagementConfig>> {
        if !self.is_initialized() {
            return Err(ApiError::BadRequest("RiskConfigService not initialized".to_string()));
        }

        self.repository
            .get_risk_config_by_strategy_type(strategy_type)
            .await
            .map_err(|e| {
                error!("Failed to get risk config by strategy type {}: {}", strategy_type, e);
                ApiError::from(e)
            })
    }

    /// 获取所有启用的风险配置
    pub async fn get_enabled_configs(&self) -> ApiResult<Vec<RiskManagementConfig>> {
        if !self.is_initialized() {
            return Err(ApiError::BadRequest("RiskConfigService not initialized".to_string()));
        }

        self.repository
            .get_enabled_risk_configs()
            .await
            .map_err(|e| {
                error!("Failed to get enabled risk configs: {}", e);
                ApiError::from(e)
            })
    }

    /// 保存风险配置
    pub async fn save_config(&self, config: &RiskManagementConfig) -> ApiResult<()> {
        if !self.is_initialized() {
            return Err(ApiError::BadRequest("RiskConfigService not initialized".to_string()));
        }

        // 验证配置
        self.validate_config(config)?;

        // 保存到数据库
        self.repository
            .save_risk_config(config)
            .await
            .map_err(|e| {
                error!("Failed to save risk config: {}", e);
                ApiError::from(e)
            })?;

        // 清除缓存
        self.cache.clear_config_cache("risk").await;
        info!("Risk config saved successfully and cache cleared");

        Ok(())
    }

    /// 更新风险配置
    pub async fn update_config(&self, id: uuid::Uuid, config: &RiskManagementConfig) -> ApiResult<()> {
        if !self.is_initialized() {
            return Err(ApiError::BadRequest("RiskConfigService not initialized".to_string()));
        }

        // 验证配置
        self.validate_config(config)?;

        // 更新到数据库
        self.repository
            .update_risk_config(id, config)
            .await
            .map_err(|e| {
                error!("Failed to update risk config {}: {}", id, e);
                ApiError::from(e)
            })?;

        // 清除缓存
        self.cache.clear_config_cache("risk").await;
        info!("Risk config {} updated successfully and cache cleared", id);

        Ok(())
    }

    /// 删除风险配置
    pub async fn delete_config(&self, id: uuid::Uuid) -> ApiResult<()> {
        if !self.is_initialized() {
            return Err(ApiError::BadRequest("RiskConfigService not initialized".to_string()));
        }

        // 删除配置
        self.repository
            .delete_risk_config(id)
            .await
            .map_err(|e| {
                error!("Failed to delete risk config {}: {}", id, e);
                ApiError::from(e)
            })?;

        // 清除缓存
        self.cache.clear_config_cache("risk").await;
        info!("Risk config {} deleted successfully and cache cleared", id);

        Ok(())
    }

    /// 重置风险配置为默认值
    pub async fn reset_config(&self) -> ApiResult<()> {
        if !self.is_initialized() {
            return Err(ApiError::BadRequest("RiskConfigService not initialized".to_string()));
        }

        // 重置到默认配置
        self.repository
            .reset_risk_config()
            .await
            .map_err(|e| {
                error!("Failed to reset risk config: {}", e);
                ApiError::from(e)
            })?;

        // 清除缓存
        self.cache.clear_config_cache("risk").await;
        info!("Risk config reset to default and cache cleared");

        Ok(())
    }

    /// 验证风险配置
    fn validate_config(&self, config: &RiskManagementConfig) -> ApiResult<()> {
        // 使用新的统一验证系统
        config.validate_complete().map_err(|e| {
            error!("Risk config validation failed: {}", e);
            ApiError::BadRequest(format!("风险配置验证失败: {}", e))
        })?;

        // 额外的业务逻辑验证
        if config.name.trim().is_empty() {
            return Err(ApiError::BadRequest("配置名称不能为空".to_string()));
        }

        if config.name.len() > 255 {
            return Err(ApiError::BadRequest("配置名称长度不能超过255个字符".to_string()));
        }

        // 验证策略类型（如果提供）
        if let Some(strategy_type) = &config.strategy_type {
            if strategy_type.trim().is_empty() {
                return Err(ApiError::BadRequest("策略类型不能为空字符串".to_string()));
            }

            // 验证策略类型是否在允许的列表中
            let allowed_strategy_types = ["grid_trading", "dca", "momentum", "mean_reversion"];
            if !allowed_strategy_types.contains(&strategy_type.as_str()) {
                return Err(ApiError::BadRequest(format!(
                    "不支持的策略类型: {}，支持的类型: {:?}",
                    strategy_type,
                    allowed_strategy_types
                )));
            }
        }

        Ok(())
    }
}

#[async_trait]
impl ConfigService for RiskConfigService {
    async fn initialize(&self) -> ApiResult<()> {
        info!("🔧 Initializing RiskConfigService...");

        // 预加载风险配置到缓存
        match self.cache.get_or_load_risk_config(|| {
            self.repository.get_risk_config()
        }).await {
            Ok(_) => {
                self.is_initialized.store(true, Ordering::Relaxed);
                info!("✅ RiskConfigService initialized successfully");
                Ok(())
            }
            Err(e) => {
                error!("❌ RiskConfigService initialization failed: {}", e);
                Err(ApiError::from(e))
            }
        }
    }

    async fn reload(&self) -> ApiResult<()> {
        info!("🔄 Reloading RiskConfigService...");
        
        // 清除缓存
        self.cache.clear_config_cache("risk").await;
        
        // 重新加载配置
        self.cache.get_or_load_risk_config(|| {
            self.repository.get_risk_config()
        })
        .await
        .map_err(|e| {
            error!("Failed to reload risk config: {}", e);
            ApiError::from(e)
        })?;

        info!("✅ RiskConfigService reloaded successfully");
        Ok(())
    }

    fn is_initialized(&self) -> bool {
        self.is_initialized.load(Ordering::Relaxed)
    }

    fn service_name(&self) -> &'static str {
        "RiskConfigService"
    }

    async fn clear_cache(&self) -> ApiResult<()> {
        self.cache.clear_config_cache("risk").await;
        Ok(())
    }

    async fn get_statistics(&self) -> ApiResult<ConfigStatistics> {
        Ok(ConfigStatistics {
            config_count: 1,
            cache_hit_rate: 0.0, // TODO: 实现缓存命中率统计
            last_updated: None, // TODO: 实现最后更新时间跟踪
            is_initialized: self.is_initialized(),
        })
    }
}
