use axum::{
    extract::State,
    http::StatusCode,
    response::Json,
};
use serde_json::{json, Value};
use chrono::Utc;
use sigmax_database::{OrderRepository, TradeRepository, StrategyRepository};

use crate::{
    error::{ApiError, ApiResult, ApiResponse},
    state::AppState,
};

/// 获取系统健康
/// GET /api/v1/health
pub async fn get_health(
    State(_state): State<AppState>,
) -> Result<Json<ApiResponse<Value>>, StatusCode> {
    // TODO: 检查各组件的实际健康状态
    let health_data = json!({
        "status": "ok",
        "healthy": true,
        "timestamp": Utc::now()
    });

    let response = ApiResponse::success(health_data, "系统运行正常");
    Ok(Json(response))
}

/// 获取系统状态
pub async fn get_system_status(
    State(state): State<AppState>,
) -> ApiResult<Json<ApiResponse<Value>>> {
    // 获取系统运行时间
    let uptime = state.start_time.elapsed();

    // 检查数据库连接状态
    let database_status = match state.database.health_check().await {
        Ok(_) => "connected",
        Err(_) => "disconnected",
    };

    // 获取基本统计信息
    let orders_count = match state.order_repository.get_all_orders().await {
        Ok(orders) => orders.len(),
        Err(_) => 0,
    };

    let trades_count = match state.trade_repository.get_trades().await {
        Ok(trades) => trades.len(),
        Err(_) => 0,
    };

    let strategies_count = match state.strategy_repository.get_all_strategies().await {
        Ok(strategies) => strategies.len(),
        Err(_) => 0,
    };

    let status_data = json!({
        "status": "running",
        "timestamp": Utc::now(),
        "uptime_seconds": uptime.as_secs(),
        "database_status": database_status,
        "statistics": {
            "total_orders": orders_count,
            "total_trades": trades_count,
            "total_strategies": strategies_count
        },
        "version": "0.1.0",
        "environment": "development"
    });

    let response = ApiResponse::success(status_data, "获取系统状态成功");
    Ok(Json(response))
}

/// 获取基本订单列表
pub async fn get_basic_orders(
    State(state): State<AppState>,
) -> ApiResult<Json<Value>> {
    match state.order_repository.get_all_orders().await {
        Ok(orders) => {
            let response = json!({
                "orders": orders,
                "total": orders.len(),
                "timestamp": Utc::now()
            });
            Ok(Json(response))
        }
        Err(e) => {
            tracing::error!("Failed to get orders: {}", e);
            Err(ApiError::Internal(
                "Failed to retrieve orders".to_string()
            ))
        }
    }
}

/// 获取基本交易列表
pub async fn get_basic_trades(
    State(state): State<AppState>,
) -> ApiResult<Json<Value>> {
    match state.trade_repository.get_trades().await {
        Ok(trades) => {
            let response = json!({
                "trades": trades,
                "total": trades.len(),
                "timestamp": Utc::now()
            });
            Ok(Json(response))
        }
        Err(e) => {
            tracing::error!("Failed to get trades: {}", e);
            Err(ApiError::Internal(
                "Failed to retrieve trades".to_string()
            ))
        }
    }
}
