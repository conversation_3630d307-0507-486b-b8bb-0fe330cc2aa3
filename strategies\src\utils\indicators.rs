//! 技术指标计算工具
//!
//! 提供策略开发所需的技术指标计算功能

use std::collections::VecDeque;
use sigmax_core::Candle;
use rust_decimal::prelude::*;

/// 技术指标计算器
pub struct TechnicalIndicators;

impl TechnicalIndicators {
    /// 计算简单移动平均线 (SMA)
    ///
    /// # 参数
    /// - `prices`: 价格序列
    /// - `period`: 计算周期
    ///
    /// # 返回
    /// 移动平均值，如果数据不足返回None
    pub fn sma(prices: &[f64], period: usize) -> Option<f64> {
        if prices.len() < period {
            return None;
        }

        let sum: f64 = prices.iter().rev().take(period).sum();
        Some(sum / period as f64)
    }

    /// 计算指数移动平均线 (EMA)
    ///
    /// # 参数
    /// - `prices`: 价格序列
    /// - `period`: 计算周期
    ///
    /// # 返回
    /// 指数移动平均值序列
    pub fn ema(prices: &[f64], period: usize) -> Vec<f64> {
        if prices.is_empty() {
            return Vec::new();
        }

        let mut ema_values = Vec::with_capacity(prices.len());
        let multiplier = 2.0 / (period as f64 + 1.0);

        // 第一个EMA值使用SMA
        if prices.len() >= period {
            let first_sma: f64 = prices.iter().take(period).sum::<f64>() / period as f64;
            ema_values.push(first_sma);

            // 计算后续EMA值
            for i in period..prices.len() {
                let ema = (prices[i] - ema_values[i - period]) * multiplier + ema_values[i - period];
                ema_values.push(ema);
            }
        }

        ema_values
    }

    /// 计算布林带
    ///
    /// # 参数
    /// - `prices`: 价格序列
    /// - `period`: 计算周期
    /// - `std_dev`: 标准差倍数
    ///
    /// # 返回
    /// (中轨, 上轨, 下轨)
    pub fn bollinger_bands(prices: &[f64], period: usize, std_dev: f64) -> Option<(f64, f64, f64)> {
        if prices.len() < period {
            return None;
        }

        let recent_prices: Vec<f64> = prices.iter().rev().take(period).cloned().collect();
        let sma = recent_prices.iter().sum::<f64>() / period as f64;

        // 计算标准差
        let variance = recent_prices.iter()
            .map(|price| (price - sma).powi(2))
            .sum::<f64>() / period as f64;
        let std = variance.sqrt();

        let upper_band = sma + (std_dev * std);
        let lower_band = sma - (std_dev * std);

        Some((sma, upper_band, lower_band))
    }

    /// 计算RSI (相对强弱指数)
    ///
    /// # 参数
    /// - `prices`: 价格序列
    /// - `period`: 计算周期
    ///
    /// # 返回
    /// RSI值 (0-100)
    pub fn rsi(prices: &[f64], period: usize) -> Option<f64> {
        if prices.len() < period + 1 {
            return None;
        }

        let mut gains = Vec::new();
        let mut losses = Vec::new();

        // 计算价格变化
        for i in 1..prices.len() {
            let change = prices[i] - prices[i - 1];
            if change > 0.0 {
                gains.push(change);
                losses.push(0.0);
            } else {
                gains.push(0.0);
                losses.push(-change);
            }
        }

        if gains.len() < period {
            return None;
        }

        // 计算平均收益和平均损失
        let avg_gain: f64 = gains.iter().rev().take(period).sum::<f64>() / period as f64;
        let avg_loss: f64 = losses.iter().rev().take(period).sum::<f64>() / period as f64;

        if avg_loss == 0.0 {
            return Some(100.0);
        }

        let rs = avg_gain / avg_loss;
        let rsi = 100.0 - (100.0 / (1.0 + rs));

        Some(rsi)
    }
}

/// 波动率计算器
pub struct VolatilityCalculator {
    price_history: VecDeque<f64>,
    window_size: usize,
}

impl VolatilityCalculator {
    /// 创建新的波动率计算器
    ///
    /// # 参数
    /// - `window_size`: 计算窗口大小
    pub fn new(window_size: usize) -> Self {
        Self {
            price_history: VecDeque::with_capacity(window_size),
            window_size,
        }
    }

    /// 添加新的价格数据
    ///
    /// # 参数
    /// - `price`: 新的价格
    pub fn add_price(&mut self, price: f64) {
        self.price_history.push_back(price);

        // 保持窗口大小
        if self.price_history.len() > self.window_size {
            self.price_history.pop_front();
        }
    }

    /// 计算当前波动率
    ///
    /// # 返回
    /// 波动率百分比，如果数据不足返回None
    pub fn calculate_volatility(&self) -> Option<f64> {
        if self.price_history.len() < 2 {
            return None;
        }

        let prices: Vec<f64> = self.price_history.iter().cloned().collect();
        let mean = prices.iter().sum::<f64>() / prices.len() as f64;

        let variance = prices.iter()
            .map(|price| (price - mean).powi(2))
            .sum::<f64>() / (prices.len() - 1) as f64;

        let volatility = (variance.sqrt() / mean) * 100.0;
        Some(volatility)
    }

    /// 计算价格变化率
    ///
    /// # 返回
    /// 价格变化率百分比
    pub fn calculate_price_change_rate(&self) -> Option<f64> {
        if self.price_history.len() < 2 {
            return None;
        }

        let latest = *self.price_history.back().unwrap();
        let previous = *self.price_history.front().unwrap();

        if previous == 0.0 {
            return None;
        }

        let change_rate = ((latest - previous) / previous) * 100.0;
        Some(change_rate)
    }

    /// 获取价格历史
    pub fn get_price_history(&self) -> Vec<f64> {
        self.price_history.iter().cloned().collect()
    }

    /// 清空历史数据
    pub fn clear(&mut self) {
        self.price_history.clear();
    }

    /// 检查数据是否充足
    pub fn has_sufficient_data(&self) -> bool {
        self.price_history.len() >= self.window_size
    }
}

impl Default for VolatilityCalculator {
    fn default() -> Self {
        Self::new(24) // 默认24小时窗口
    }
}

/// 从K线数据中提取价格序列
pub fn extract_prices_from_candles(candles: &[Candle], price_type: PriceType) -> Vec<f64> {
    candles.iter().map(|candle| {
        match price_type {
            PriceType::Open => candle.open.to_f64().unwrap_or(0.0),
            PriceType::High => candle.high.to_f64().unwrap_or(0.0),
            PriceType::Low => candle.low.to_f64().unwrap_or(0.0),
            PriceType::Close => candle.close.to_f64().unwrap_or(0.0),
            PriceType::Typical => {
                let high = candle.high.to_f64().unwrap_or(0.0);
                let low = candle.low.to_f64().unwrap_or(0.0);
                let close = candle.close.to_f64().unwrap_or(0.0);
                (high + low + close) / 3.0
            }
        }
    }).collect()
}

/// 价格类型枚举
#[derive(Debug, Clone, Copy)]
pub enum PriceType {
    Open,
    High,
    Low,
    Close,
    Typical, // (High + Low + Close) / 3
}
