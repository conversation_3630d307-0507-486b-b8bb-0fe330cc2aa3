#!/usr/bin/env python3
"""
性能分析器 - 分析性能数据
提供性能指标分析、趋势分析、异常检测等功能
"""

import statistics
from typing import Dict, List, Any, Optional
import logging

logger = logging.getLogger(__name__)

class PerformanceAnalyzer:
    """性能分析器"""
    
    def __init__(self, performance_thresholds: Dict[str, Any]):
        self.performance_thresholds = performance_thresholds
        
    def analyze_response_times(self, response_times: Dict[str, float]) -> Dict[str, Any]:
        """分析响应时间"""
        analysis = {}
        
        for endpoint, time_ms in response_times.items():
            # 获取阈值
            thresholds = self.performance_thresholds.get("performance_thresholds", {}).get("response_time", {})
            endpoint_threshold = thresholds.get(endpoint, {})
            
            if endpoint_threshold:
                if time_ms <= endpoint_threshold.get("target", 1000):
                    status = "excellent"
                elif time_ms <= endpoint_threshold.get("warning", 2000):
                    status = "good"
                elif time_ms <= endpoint_threshold.get("critical", 5000):
                    status = "warning"
                else:
                    status = "critical"
            else:
                status = "unknown"
            
            analysis[endpoint] = {
                "response_time": time_ms,
                "status": status,
                "threshold": endpoint_threshold
            }
        
        return analysis
    
    def calculate_statistics(self, values: List[float]) -> Dict[str, float]:
        """计算统计指标"""
        if not values:
            return {}
        
        return {
            "mean": statistics.mean(values),
            "median": statistics.median(values),
            "min": min(values),
            "max": max(values),
            "std_dev": statistics.stdev(values) if len(values) > 1 else 0,
            "p95": statistics.quantiles(values, n=20)[18] if len(values) >= 20 else max(values),
            "p99": statistics.quantiles(values, n=100)[98] if len(values) >= 100 else max(values)
        }
    
    def detect_anomalies(self, values: List[float], threshold_factor: float = 2.0) -> List[int]:
        """检测异常值"""
        if len(values) < 3:
            return []
        
        mean = statistics.mean(values)
        std_dev = statistics.stdev(values)
        threshold = threshold_factor * std_dev
        
        anomalies = []
        for i, value in enumerate(values):
            if abs(value - mean) > threshold:
                anomalies.append(i)
        
        return anomalies
