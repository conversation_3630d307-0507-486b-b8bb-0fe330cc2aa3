//! 策略类型定义
//!
//! 提供类型安全的策略类型枚举，替代裸字符串匹配

use serde::{Deserialize, Serialize};
use std::fmt;
use std::str::FromStr;
use sigmax_core::SigmaXError;

/// 策略类型枚举
///
/// ## 设计原则
/// - **类型安全**：编译时检查策略类型
/// - **可扩展**：新策略类型只需添加枚举值
/// - **序列化友好**：支持JSON序列化/反序列化
/// - **字符串转换**：提供与现有API的兼容性
///
/// ## 当前支持的策略
/// - `AsymmetricVolatilityGrid`: 非对称波动率网格策略（已实现）
///
/// ## 未来计划的策略
/// - DCA（定投策略）
/// - Momentum（动量策略）
/// - MeanReversion（均值回归策略）
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum StrategyType {
    /// 非对称波动率网格策略
    AsymmetricVolatilityGrid,
    // 🔥 未来添加新策略时，在这里添加新的枚举值
    // /// 定投策略（DCA）
    // DollarCostAveraging,
    // /// 动量策略
    // Momentum,
    // /// 均值回归策略
    // MeanReversion,
}

impl StrategyType {
    /// 获取策略类型的字符串标识
    ///
    /// # 返回
    /// 策略类型的字符串表示，用于API兼容性
    pub fn as_str(&self) -> &'static str {
        match self {
            Self::AsymmetricVolatilityGrid => "asymmetric_volatility_grid",
            // 🔥 未来添加新策略时，在这里添加对应的字符串标识
        }
    }

    /// 获取策略类型的显示名称
    ///
    /// # 返回
    /// 用户友好的策略名称
    pub fn display_name(&self) -> &'static str {
        match self {
            Self::AsymmetricVolatilityGrid => "非对称波动率网格策略",
            // 🔥 未来添加新策略时，在这里添加对应的显示名称
        }
    }

    /// 获取策略类型的描述
    ///
    /// # 返回
    /// 策略的详细描述
    pub fn description(&self) -> &'static str {
        match self {
            Self::AsymmetricVolatilityGrid => {
                "基于波动率的非对称网格策略，下跌区间密集吸筹，上涨区间稀疏止盈"
            }
            // 🔥 未来添加新策略时，在这里添加对应的描述
        }
    }

    /// 获取所有支持的策略类型
    ///
    /// # 返回
    /// 所有策略类型的向量
    pub fn all() -> Vec<Self> {
        vec![
            Self::AsymmetricVolatilityGrid,
            // 🔥 未来添加新策略时，在这里添加新的枚举值
        ]
    }

    /// 获取所有策略类型的字符串标识
    ///
    /// # 返回
    /// 所有策略类型字符串标识的向量
    pub fn all_str() -> Vec<&'static str> {
        Self::all().iter().map(|t| t.as_str()).collect()
    }

    /// 检查策略类型是否受支持
    ///
    /// # 参数
    /// - `strategy_type`: 策略类型字符串
    ///
    /// # 返回
    /// - `true`: 支持该策略类型
    /// - `false`: 不支持该策略类型
    pub fn is_supported(strategy_type: &str) -> bool {
        Self::all_str().contains(&strategy_type)
    }
}

impl fmt::Display for StrategyType {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.as_str())
    }
}

impl FromStr for StrategyType {
    type Err = SigmaXError;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s {
            // 下划线格式（标准格式）
            "asymmetric_volatility_grid" => Ok(Self::AsymmetricVolatilityGrid),

            // 驼峰格式（向后兼容）
            "AsymmetricVolatilityGrid" => Ok(Self::AsymmetricVolatilityGrid),

            // 🔥 未来添加新策略时，在这里添加对应的字符串匹配
            // "dollar_cost_averaging" => Ok(Self::DollarCostAveraging),
            // "DollarCostAveraging" => Ok(Self::DollarCostAveraging),

            _ => Err(SigmaXError::Config(format!(
                "Unknown strategy type: {}. Supported types: {:?}",
                s,
                Self::all_str()
            ))),
        }
    }
}

/// 策略类型转换trait
///
/// 提供从字符串到策略类型的安全转换
pub trait StrategyTypeConversion {
    /// 从字符串安全转换为策略类型
    fn try_from_str(s: &str) -> Result<StrategyType, SigmaXError>;
}

impl StrategyTypeConversion for StrategyType {
    fn try_from_str(s: &str) -> Result<StrategyType, SigmaXError> {
        s.parse()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_strategy_type_as_str() {
        assert_eq!(StrategyType::AsymmetricVolatilityGrid.as_str(), "asymmetric_volatility_grid");
        // 🔥 未来添加新策略时，在这里添加对应的测试
    }

    #[test]
    fn test_strategy_type_from_str() {
        assert_eq!(
            "asymmetric_volatility_grid".parse::<StrategyType>().unwrap(),
            StrategyType::AsymmetricVolatilityGrid
        );
        assert!("invalid_strategy".parse::<StrategyType>().is_err());
    }

    #[test]
    fn test_strategy_type_serialization() {
        let strategy_type = StrategyType::AsymmetricVolatilityGrid;
        let json = serde_json::to_string(&strategy_type).unwrap();
        assert_eq!(json, "\"asymmetric_volatility_grid\"");

        let deserialized: StrategyType = serde_json::from_str(&json).unwrap();
        assert_eq!(deserialized, strategy_type);
    }

    #[test]
    fn test_is_supported() {
        assert!(StrategyType::is_supported("asymmetric_volatility_grid"));
        assert!(!StrategyType::is_supported("invalid_strategy"));
    }
}
