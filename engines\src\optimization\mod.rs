//! 性能优化模块
//! 
//! 设计原则：
//! - 极致性能：针对高频交易场景的极致优化
//! - 智能缓存：多层次缓存策略和预测性缓存
//! - 内存优化：零拷贝、对象池、内存复用
//! - 延迟优化：减少异步开销、批量操作、预计算

pub mod cache_optimization;
pub mod memory_optimization;
pub mod latency_optimization;
pub mod batch_optimization;
pub mod performance_tuning;

use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tracing::{info, debug, warn};

/// 性能优化配置
#[derive(Debug, Clone)]
pub struct OptimizationConfig {
    /// 启用高级缓存
    pub enable_advanced_cache: bool,
    /// 启用内存优化
    pub enable_memory_optimization: bool,
    /// 启用延迟优化
    pub enable_latency_optimization: bool,
    /// 启用批量优化
    pub enable_batch_optimization: bool,
    /// 性能监控间隔
    pub monitoring_interval_ms: u64,
    /// 自动调优
    pub enable_auto_tuning: bool,
}

impl Default for OptimizationConfig {
    fn default() -> Self {
        Self {
            enable_advanced_cache: true,
            enable_memory_optimization: true,
            enable_latency_optimization: true,
            enable_batch_optimization: true,
            monitoring_interval_ms: 1000, // 1秒
            enable_auto_tuning: true,
        }
    }
}

/// 性能优化器
pub struct PerformanceOptimizer {
    config: OptimizationConfig,
    cache_optimizer: Arc<cache_optimization::CacheOptimizer>,
    memory_optimizer: Arc<memory_optimization::MemoryOptimizer>,
    latency_optimizer: Arc<latency_optimization::LatencyOptimizer>,
    batch_optimizer: Arc<batch_optimization::BatchOptimizer>,
    performance_monitor: Arc<RwLock<performance_tuning::PerformanceMonitor>>,
    is_running: std::sync::atomic::AtomicBool,
}

impl PerformanceOptimizer {
    /// 创建性能优化器
    pub async fn new(config: OptimizationConfig) -> core::errors::SigmaXResult<Self> {
        info!("Creating PerformanceOptimizer with config: {:?}", config);
        
        let cache_optimizer = Arc::new(cache_optimization::CacheOptimizer::new().await?);
        let memory_optimizer = Arc::new(memory_optimization::MemoryOptimizer::new().await?);
        let latency_optimizer = Arc::new(latency_optimization::LatencyOptimizer::new().await?);
        let batch_optimizer = Arc::new(batch_optimization::BatchOptimizer::new().await?);
        let performance_monitor = Arc::new(RwLock::new(performance_tuning::PerformanceMonitor::new()));
        
        Ok(Self {
            config,
            cache_optimizer,
            memory_optimizer,
            latency_optimizer,
            batch_optimizer,
            performance_monitor,
            is_running: std::sync::atomic::AtomicBool::new(false),
        })
    }
    
    /// 启动性能优化
    pub async fn start(&self) -> core::errors::SigmaXResult<()> {
        info!("Starting performance optimization");
        
        self.is_running.store(true, std::sync::atomic::Ordering::SeqCst);
        
        // 启动各个优化器
        if self.config.enable_advanced_cache {
            self.cache_optimizer.start().await?;
        }
        
        if self.config.enable_memory_optimization {
            self.memory_optimizer.start().await?;
        }
        
        if self.config.enable_latency_optimization {
            self.latency_optimizer.start().await?;
        }
        
        if self.config.enable_batch_optimization {
            self.batch_optimizer.start().await?;
        }
        
        // 启动性能监控
        self.start_performance_monitoring().await?;
        
        // 启动自动调优
        if self.config.enable_auto_tuning {
            self.start_auto_tuning().await?;
        }
        
        info!("Performance optimization started successfully");
        Ok(())
    }
    
    /// 停止性能优化
    pub async fn stop(&self) -> core::errors::SigmaXResult<()> {
        info!("Stopping performance optimization");
        
        self.is_running.store(false, std::sync::atomic::Ordering::SeqCst);
        
        // 停止各个优化器
        self.cache_optimizer.stop().await?;
        self.memory_optimizer.stop().await?;
        self.latency_optimizer.stop().await?;
        self.batch_optimizer.stop().await?;
        
        info!("Performance optimization stopped");
        Ok(())
    }
    
    /// 启动性能监控
    async fn start_performance_monitoring(&self) -> core::errors::SigmaXResult<()> {
        let monitor = self.performance_monitor.clone();
        let interval_ms = self.config.monitoring_interval_ms;
        let is_running = &self.is_running;
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_millis(interval_ms));
            
            while is_running.load(std::sync::atomic::Ordering::SeqCst) {
                interval.tick().await;
                
                let mut monitor_guard = monitor.write().await;
                if let Err(e) = monitor_guard.collect_metrics().await {
                    warn!("Failed to collect performance metrics: {}", e);
                }
            }
        });
        
        Ok(())
    }
    
    /// 启动自动调优
    async fn start_auto_tuning(&self) -> core::errors::SigmaXResult<()> {
        let cache_optimizer = self.cache_optimizer.clone();
        let memory_optimizer = self.memory_optimizer.clone();
        let latency_optimizer = self.latency_optimizer.clone();
        let batch_optimizer = self.batch_optimizer.clone();
        let performance_monitor = self.performance_monitor.clone();
        let is_running = &self.is_running;
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(30)); // 30秒调优一次
            
            while is_running.load(std::sync::atomic::Ordering::SeqCst) {
                interval.tick().await;
                
                let monitor_guard = performance_monitor.read().await;
                let metrics = monitor_guard.get_current_metrics();
                drop(monitor_guard);
                
                // 基于性能指标自动调优
                if let Err(e) = Self::auto_tune_optimizers(
                    &cache_optimizer,
                    &memory_optimizer,
                    &latency_optimizer,
                    &batch_optimizer,
                    &metrics,
                ).await {
                    warn!("Auto-tuning failed: {}", e);
                }
            }
        });
        
        Ok(())
    }
    
    /// 自动调优优化器
    async fn auto_tune_optimizers(
        cache_optimizer: &cache_optimization::CacheOptimizer,
        memory_optimizer: &memory_optimization::MemoryOptimizer,
        latency_optimizer: &latency_optimization::LatencyOptimizer,
        batch_optimizer: &batch_optimization::BatchOptimizer,
        metrics: &performance_tuning::PerformanceMetrics,
    ) -> core::errors::SigmaXResult<()> {
        debug!("Starting auto-tuning based on metrics: {:?}", metrics);
        
        // 缓存调优
        if metrics.cache_hit_rate < 0.9 {
            cache_optimizer.increase_cache_size().await?;
            debug!("Increased cache size due to low hit rate: {:.2}%", metrics.cache_hit_rate * 100.0);
        }
        
        // 内存调优
        if metrics.memory_usage_ratio > 0.8 {
            memory_optimizer.trigger_cleanup().await?;
            debug!("Triggered memory cleanup due to high usage: {:.2}%", metrics.memory_usage_ratio * 100.0);
        }
        
        // 延迟调优
        if metrics.avg_latency_ms > 20.0 {
            latency_optimizer.optimize_hot_paths().await?;
            debug!("Optimized hot paths due to high latency: {:.2}ms", metrics.avg_latency_ms);
        }
        
        // 批量调优
        if metrics.throughput_ops < 10000.0 {
            batch_optimizer.increase_batch_size().await?;
            debug!("Increased batch size due to low throughput: {:.0} ops/s", metrics.throughput_ops);
        }
        
        Ok(())
    }
    
    /// 获取优化统计
    pub async fn get_optimization_stats(&self) -> OptimizationStats {
        let cache_stats = self.cache_optimizer.get_stats().await;
        let memory_stats = self.memory_optimizer.get_stats().await;
        let latency_stats = self.latency_optimizer.get_stats().await;
        let batch_stats = self.batch_optimizer.get_stats().await;
        
        let monitor_guard = self.performance_monitor.read().await;
        let performance_metrics = monitor_guard.get_current_metrics();
        
        OptimizationStats {
            cache_stats,
            memory_stats,
            latency_stats,
            batch_stats,
            performance_metrics,
            is_running: self.is_running.load(std::sync::atomic::Ordering::SeqCst),
        }
    }
    
    /// 手动触发优化
    pub async fn trigger_optimization(&self) -> core::errors::SigmaXResult<()> {
        info!("Manually triggering optimization");
        
        // 触发各个优化器
        self.cache_optimizer.optimize().await?;
        self.memory_optimizer.optimize().await?;
        self.latency_optimizer.optimize().await?;
        self.batch_optimizer.optimize().await?;
        
        info!("Manual optimization completed");
        Ok(())
    }
}

/// 优化统计信息
#[derive(Debug, Clone)]
pub struct OptimizationStats {
    pub cache_stats: cache_optimization::CacheStats,
    pub memory_stats: memory_optimization::MemoryStats,
    pub latency_stats: latency_optimization::LatencyStats,
    pub batch_stats: batch_optimization::BatchStats,
    pub performance_metrics: performance_tuning::PerformanceMetrics,
    pub is_running: bool,
}

impl OptimizationStats {
    /// 打印优化统计
    pub fn print(&self) {
        println!("🚀 性能优化统计");
        println!("================");
        
        println!("📊 整体性能:");
        println!("   - 平均延迟: {:.2}ms", self.performance_metrics.avg_latency_ms);
        println!("   - 吞吐量: {:.0} ops/s", self.performance_metrics.throughput_ops);
        println!("   - 缓存命中率: {:.1}%", self.performance_metrics.cache_hit_rate * 100.0);
        println!("   - 内存使用率: {:.1}%", self.performance_metrics.memory_usage_ratio * 100.0);
        
        println!("🔥 缓存优化:");
        println!("   - L1缓存命中率: {:.1}%", self.cache_stats.l1_hit_rate * 100.0);
        println!("   - L2缓存命中率: {:.1}%", self.cache_stats.l2_hit_rate * 100.0);
        println!("   - 预测命中率: {:.1}%", self.cache_stats.prediction_hit_rate * 100.0);
        
        println!("💾 内存优化:");
        println!("   - 对象池命中率: {:.1}%", self.memory_stats.pool_hit_rate * 100.0);
        println!("   - 零拷贝操作: {}", self.memory_stats.zero_copy_operations);
        println!("   - GC压力: {:.2}", self.memory_stats.gc_pressure);
        
        println!("⚡ 延迟优化:");
        println!("   - 热路径优化: {}", self.latency_stats.hot_path_optimizations);
        println!("   - 异步开销: {:.2}ms", self.latency_stats.async_overhead_ms);
        println!("   - 预计算命中: {}", self.latency_stats.precompute_hits);
        
        println!("📦 批量优化:");
        println!("   - 当前批量大小: {}", self.batch_stats.current_batch_size);
        println!("   - 批量效率: {:.1}%", self.batch_stats.batch_efficiency * 100.0);
        println!("   - 并行度: {}", self.batch_stats.parallelism_level);
        
        println!("🔧 优化器状态: {}", if self.is_running { "运行中" } else { "已停止" });
    }
    
    /// 生成优化建议
    pub fn generate_recommendations(&self) -> Vec<String> {
        let mut recommendations = Vec::new();
        
        if self.performance_metrics.cache_hit_rate < 0.9 {
            recommendations.push("考虑增加缓存大小或优化缓存策略".to_string());
        }
        
        if self.performance_metrics.avg_latency_ms > 15.0 {
            recommendations.push("考虑启用更多延迟优化技术".to_string());
        }
        
        if self.performance_metrics.throughput_ops < 12000.0 {
            recommendations.push("考虑增加批量大小或并行度".to_string());
        }
        
        if self.performance_metrics.memory_usage_ratio > 0.8 {
            recommendations.push("考虑启用更积极的内存清理策略".to_string());
        }
        
        if self.cache_stats.l1_hit_rate < 0.95 {
            recommendations.push("考虑调整L1缓存大小或替换策略".to_string());
        }
        
        if self.memory_stats.gc_pressure > 0.5 {
            recommendations.push("考虑使用更多对象池或减少内存分配".to_string());
        }
        
        if self.latency_stats.async_overhead_ms > 2.0 {
            recommendations.push("考虑减少异步操作或使用同步优化".to_string());
        }
        
        if self.batch_stats.batch_efficiency < 0.8 {
            recommendations.push("考虑优化批量处理算法或调整批量大小".to_string());
        }
        
        if recommendations.is_empty() {
            recommendations.push("性能表现优秀，无需额外优化".to_string());
        }
        
        recommendations
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_performance_optimizer_creation() {
        let config = OptimizationConfig::default();
        let optimizer = PerformanceOptimizer::new(config).await;
        
        // 注意：这个测试需要完整的实现才能运行
        // 目前只测试配置
        assert!(optimizer.is_err()); // 因为依赖的模块还未实现
    }
    
    #[test]
    fn test_optimization_config_default() {
        let config = OptimizationConfig::default();
        assert!(config.enable_advanced_cache);
        assert!(config.enable_memory_optimization);
        assert!(config.enable_latency_optimization);
        assert!(config.enable_batch_optimization);
        assert!(config.enable_auto_tuning);
        assert_eq!(config.monitoring_interval_ms, 1000);
    }
}
