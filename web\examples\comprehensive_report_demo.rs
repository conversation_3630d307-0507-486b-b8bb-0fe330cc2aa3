//! 完整回测报告生成演示
//!
//! 展示专业级回测报告生成功能

use sigmax_core::{SigmaXResult, Trade, TradingPair, OrderSide, ExchangeId};
use rust_decimal::{Decimal, prelude::ToPrimitive};
use rust_decimal_macros::dec;
use chrono::{Utc, TimeZone};
use uuid::Uuid;
use std::fs;
use serde_json;

// 由于reporting模块还在开发中，我们先创建一个简化版本的演示

/// 简化的报告数据结构
#[derive(Debug, serde::Serialize)]
struct SimpleBacktestReport {
    metadata: ReportMetadata,
    performance_summary: PerformanceSummary,
    risk_metrics: RiskMetrics,
    trading_statistics: TradingStatistics,
    charts: ChartData,
    recommendations: Vec<String>,
}

#[derive(Debug, serde::Serialize)]
struct ReportMetadata {
    strategy_name: String,
    backtest_period: String,
    initial_capital: String,
    final_capital: String,
    duration_days: u32,
    generated_at: String,
}

#[derive(Debug, serde::Serialize)]
struct PerformanceSummary {
    total_return: String,
    annualized_return: String,
    monthly_returns: Vec<f64>,
    best_month: String,
    worst_month: String,
    positive_months: u32,
    total_months: u32,
}

#[derive(Debug, serde::Serialize)]
struct RiskMetrics {
    max_drawdown: String,
    volatility: String,
    sharpe_ratio: f64,
    sortino_ratio: f64,
    calmar_ratio: f64,
    var_95: String,
    cvar_95: String,
    beta: f64,
    alpha: String,
}

#[derive(Debug, serde::Serialize)]
struct TradingStatistics {
    total_trades: u64,
    winning_trades: u64,
    losing_trades: u64,
    win_rate: String,
    profit_factor: f64,
    average_win: String,
    average_loss: String,
    largest_win: String,
    largest_loss: String,
    average_holding_period: f64,
    expectancy: String,
}

#[derive(Debug, serde::Serialize)]
struct ChartData {
    equity_curve: Vec<EquityPoint>,
    drawdown_series: Vec<DrawdownPoint>,
    monthly_returns_heatmap: Vec<MonthlyReturnPoint>,
    return_distribution: Vec<DistributionPoint>,
}

#[derive(Debug, Clone, serde::Serialize)]
struct EquityPoint {
    date: String,
    value: f64,
    cumulative_return: f64,
}

#[derive(Debug, serde::Serialize)]
struct DrawdownPoint {
    date: String,
    drawdown: f64,
    underwater_days: u32,
}

#[derive(Debug, serde::Serialize)]
struct MonthlyReturnPoint {
    year: i32,
    month: u32,
    return_rate: f64,
}

#[derive(Debug, serde::Serialize)]
struct DistributionPoint {
    return_range: String,
    frequency: u32,
}

#[tokio::main]
async fn main() -> SigmaXResult<()> {
    println!("📊 SigmaX完整回测报告生成演示");
    println!("{}", "=".repeat(60));

    // 1. 生成模拟的回测数据
    let (trades, equity_curve) = generate_mock_backtest_data().await?;

    // 2. 生成完整报告
    let report = generate_comprehensive_report(&trades, &equity_curve).await?;

    // 3. 生成HTML报告
    generate_html_report(&report).await?;

    // 4. 生成JSON报告
    generate_json_report(&report).await?;

    // 5. 生成PDF报告 (模拟)
    generate_pdf_report_simulation().await?;

    // 6. 显示报告摘要
    display_report_summary(&report);

    println!("\n🎉 完整回测报告生成演示完成！");
    println!("📄 查看 comprehensive_backtest_report.html 获取详细报告");
    println!("📊 查看 backtest_report.json 获取JSON数据");
    println!("📋 查看 backtest_report_summary.pdf (模拟)");

    Ok(())
}

/// 生成模拟回测数据
async fn generate_mock_backtest_data() -> SigmaXResult<(Vec<Trade>, Vec<EquityPoint>)> {
    println!("📈 生成模拟回测数据...");

    let mut trades = Vec::new();
    let mut equity_curve = Vec::new();

    let start_date = Utc.with_ymd_and_hms(2024, 1, 1, 0, 0, 0).unwrap();
    let mut current_capital = dec!(100000); // 10万初始资金
    let trading_pair = TradingPair::new("BTC".to_string(), "USDT".to_string());

    // 生成一年的交易数据
    for i in 0..365 {
        let current_date = start_date + chrono::Duration::days(i);

        // 每5天生成一笔交易
        if i % 5 == 0 && i > 0 {
            let is_buy = i % 10 == 0;
            let price = dec!(40000) + Decimal::from(i * 50); // 模拟价格变化
            let quantity = dec!(0.1);

            // 模拟交易盈亏
            let pnl_factor = if i % 7 == 0 { dec!(-0.02) } else { dec!(0.03) };
            let trade_pnl = current_capital * pnl_factor;
            current_capital += trade_pnl;

            let trade = Trade::new(
                Uuid::new_v4(), // order_id
                ExchangeId::from("binance"),
                trading_pair.clone(),
                if is_buy { OrderSide::Buy } else { OrderSide::Sell },
                quantity,
                price,
                price * quantity * dec!(0.001), // 0.1% 手续费
                Some("USDT".to_string()),
            );

            trades.push(trade);
        }

        // 每日资金曲线
        let daily_return = if i % 15 == 0 { -0.02 } else if i % 10 == 0 { 0.04 } else { 0.001 };
        current_capital = current_capital * (dec!(1) + Decimal::from_f64_retain(daily_return).unwrap_or(dec!(0)));

        let cumulative_return = ((current_capital / dec!(100000)) - dec!(1)).to_f64().unwrap_or(0.0);

        equity_curve.push(EquityPoint {
            date: current_date.format("%Y-%m-%d").to_string(),
            value: current_capital.to_f64().unwrap_or(100000.0),
            cumulative_return,
        });

        if i % 50 == 0 {
            println!("   第 {} 天: 资金 ${:.0}, 累计收益 {:.2}%",
                i + 1, current_capital, cumulative_return * 100.0);
        }
    }

    println!("✅ 生成了 {} 笔交易和 {} 个资金数据点", trades.len(), equity_curve.len());

    Ok((trades, equity_curve))
}

/// 生成完整报告
async fn generate_comprehensive_report(
    trades: &[Trade],
    equity_curve: &[EquityPoint]
) -> SigmaXResult<SimpleBacktestReport> {
    println!("📊 生成完整回测报告...");

    // 计算基础指标
    let initial_capital = 100000.0;
    let final_capital = equity_curve.last().unwrap().value;
    let total_return = (final_capital / initial_capital - 1.0) * 100.0;
    let duration_days = equity_curve.len() as u32;
    let annualized_return = ((final_capital / initial_capital).powf(365.25 / duration_days as f64) - 1.0) * 100.0;

    // 计算月度收益
    let monthly_returns = calculate_monthly_returns(equity_curve);
    let positive_months = monthly_returns.iter().filter(|&&r| r > 0.0).count() as u32;
    let best_month = monthly_returns.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));
    let worst_month = monthly_returns.iter().fold(f64::INFINITY, |a, &b| a.min(b));

    // 计算风险指标
    let max_drawdown = calculate_max_drawdown(equity_curve);
    let volatility = calculate_volatility(equity_curve);
    let sharpe_ratio = calculate_sharpe_ratio(&monthly_returns, volatility);
    let sortino_ratio = calculate_sortino_ratio(&monthly_returns);
    let calmar_ratio = if max_drawdown > 0.0 { annualized_return / (max_drawdown * 100.0) } else { 0.0 };

    // 计算交易统计
    let total_trades = trades.len() as u64;
    let winning_trades = simulate_winning_trades(total_trades);
    let losing_trades = total_trades - winning_trades;
    let win_rate = (winning_trades as f64 / total_trades as f64) * 100.0;
    let profit_factor = 1.5 + (win_rate - 50.0) / 100.0; // 模拟盈亏比

    // 生成图表数据
    let charts = generate_chart_data(equity_curve, &monthly_returns);

    // 生成建议
    let recommendations = generate_recommendations(total_return, sharpe_ratio, max_drawdown, win_rate);

    let report = SimpleBacktestReport {
        metadata: ReportMetadata {
            strategy_name: "BTC量化网格策略".to_string(),
            backtest_period: format!("2024-01-01 至 2024-12-31 ({} 天)", duration_days),
            initial_capital: format!("${:.2}", initial_capital),
            final_capital: format!("${:.2}", final_capital),
            duration_days,
            generated_at: Utc::now().format("%Y-%m-%d %H:%M:%S UTC").to_string(),
        },
        performance_summary: PerformanceSummary {
            total_return: format!("{:.2}%", total_return),
            annualized_return: format!("{:.2}%", annualized_return),
            monthly_returns: monthly_returns.clone(),
            best_month: format!("{:.2}%", best_month),
            worst_month: format!("{:.2}%", worst_month),
            positive_months,
            total_months: monthly_returns.len() as u32,
        },
        risk_metrics: RiskMetrics {
            max_drawdown: format!("{:.2}%", max_drawdown * 100.0),
            volatility: format!("{:.2}%", volatility * 100.0),
            sharpe_ratio,
            sortino_ratio,
            calmar_ratio,
            var_95: format!("{:.2}%", volatility * 1.645 * 100.0), // 简化VaR计算
            cvar_95: format!("{:.2}%", volatility * 2.0 * 100.0), // 简化CVaR计算
            beta: 0.75,
            alpha: format!("{:.2}%", annualized_return - 10.0), // 假设市场收益10%
        },
        trading_statistics: TradingStatistics {
            total_trades,
            winning_trades,
            losing_trades,
            win_rate: format!("{:.1}%", win_rate),
            profit_factor,
            average_win: format!("${:.2}", final_capital * 0.02 / winning_trades as f64),
            average_loss: format!("${:.2}", final_capital * 0.01 / losing_trades.max(1) as f64),
            largest_win: format!("${:.2}", final_capital * 0.05),
            largest_loss: format!("${:.2}", final_capital * 0.03),
            average_holding_period: 5.2,
            expectancy: format!("${:.2}", (final_capital - initial_capital) / total_trades as f64),
        },
        charts,
        recommendations,
    };

    println!("✅ 报告生成完成");
    println!("   总收益率: {:.2}%", total_return);
    println!("   年化收益率: {:.2}%", annualized_return);
    println!("   最大回撤: {:.2}%", max_drawdown * 100.0);
    println!("   夏普比率: {:.3}", sharpe_ratio);
    println!("   胜率: {:.1}%", win_rate);

    Ok(report)
}

/// 计算月度收益
fn calculate_monthly_returns(equity_curve: &[EquityPoint]) -> Vec<f64> {
    let mut monthly_returns = Vec::new();
    let mut current_month = 1;
    let mut month_start_value = equity_curve[0].value;

    for point in equity_curve {
        let date_parts: Vec<&str> = point.date.split('-').collect();
        let month: u32 = date_parts[1].parse().unwrap_or(1);

        if month != current_month {
            // 计算上个月的收益率
            let month_end_value = equity_curve.iter()
                .filter(|p| {
                    let parts: Vec<&str> = p.date.split('-').collect();
                    parts[1].parse::<u32>().unwrap_or(1) == current_month
                })
                .last()
                .map(|p| p.value)
                .unwrap_or(month_start_value);

            let monthly_return = (month_end_value / month_start_value - 1.0) * 100.0;
            monthly_returns.push(monthly_return);

            current_month = month;
            month_start_value = month_end_value;
        }
    }

    // 添加最后一个月
    if let Some(last_point) = equity_curve.last() {
        let monthly_return = (last_point.value / month_start_value - 1.0) * 100.0;
        monthly_returns.push(monthly_return);
    }

    monthly_returns
}

/// 计算最大回撤
fn calculate_max_drawdown(equity_curve: &[EquityPoint]) -> f64 {
    let mut max_drawdown = 0.0;
    let mut peak = equity_curve[0].value;

    for point in equity_curve {
        if point.value > peak {
            peak = point.value;
        } else {
            let drawdown = (peak - point.value) / peak;
            if drawdown > max_drawdown {
                max_drawdown = drawdown;
            }
        }
    }

    max_drawdown
}

/// 计算波动率
fn calculate_volatility(equity_curve: &[EquityPoint]) -> f64 {
    if equity_curve.len() < 2 {
        return 0.0;
    }

    let returns: Vec<f64> = equity_curve
        .windows(2)
        .map(|w| (w[1].value / w[0].value - 1.0))
        .collect();

    let mean_return = returns.iter().sum::<f64>() / returns.len() as f64;
    let variance = returns
        .iter()
        .map(|r| (r - mean_return).powi(2))
        .sum::<f64>() / (returns.len() - 1) as f64;

    variance.sqrt() * (252.0_f64).sqrt() // 年化波动率
}

/// 计算夏普比率
fn calculate_sharpe_ratio(monthly_returns: &[f64], volatility: f64) -> f64 {
    if monthly_returns.is_empty() || volatility == 0.0 {
        return 0.0;
    }

    let mean_monthly_return = monthly_returns.iter().sum::<f64>() / monthly_returns.len() as f64;
    let annualized_return = mean_monthly_return * 12.0;
    let risk_free_rate = 2.0; // 假设无风险利率2%

    (annualized_return - risk_free_rate) / (volatility * 100.0)
}

/// 计算Sortino比率
fn calculate_sortino_ratio(monthly_returns: &[f64]) -> f64 {
    if monthly_returns.is_empty() {
        return 0.0;
    }

    let mean_return = monthly_returns.iter().sum::<f64>() / monthly_returns.len() as f64;
    let negative_returns: Vec<f64> = monthly_returns.iter().filter(|&&r| r < 0.0).cloned().collect();

    if negative_returns.is_empty() {
        return mean_return; // 如果没有负收益，返回平均收益
    }

    let downside_deviation = {
        let mean_negative = negative_returns.iter().sum::<f64>() / negative_returns.len() as f64;
        let variance = negative_returns
            .iter()
            .map(|r| (r - mean_negative).powi(2))
            .sum::<f64>() / negative_returns.len() as f64;
        variance.sqrt()
    };

    if downside_deviation > 0.0 {
        (mean_return * 12.0 - 2.0) / (downside_deviation * (12.0_f64).sqrt()) // 年化
    } else {
        0.0
    }
}

/// 模拟获胜交易数
fn simulate_winning_trades(total_trades: u64) -> u64 {
    // 模拟65%的胜率
    (total_trades as f64 * 0.65) as u64
}

/// 生成图表数据
fn generate_chart_data(equity_curve: &[EquityPoint], monthly_returns: &[f64]) -> ChartData {
    // 生成回撤序列
    let mut drawdown_series = Vec::new();
    let mut peak = equity_curve[0].value;
    let mut underwater_days = 0;

    for point in equity_curve {
        if point.value > peak {
            peak = point.value;
            underwater_days = 0;
        } else {
            underwater_days += 1;
        }

        let drawdown = (peak - point.value) / peak;
        drawdown_series.push(DrawdownPoint {
            date: point.date.clone(),
            drawdown,
            underwater_days,
        });
    }

    // 生成月度收益热力图数据
    let mut monthly_heatmap = Vec::new();
    for (i, &return_rate) in monthly_returns.iter().enumerate() {
        monthly_heatmap.push(MonthlyReturnPoint {
            year: 2024,
            month: (i % 12 + 1) as u32,
            return_rate,
        });
    }

    // 生成收益分布数据
    let return_distribution = vec![
        DistributionPoint { return_range: "< -5%".to_string(), frequency: 2 },
        DistributionPoint { return_range: "-5% to -2%".to_string(), frequency: 5 },
        DistributionPoint { return_range: "-2% to 0%".to_string(), frequency: 8 },
        DistributionPoint { return_range: "0% to 2%".to_string(), frequency: 15 },
        DistributionPoint { return_range: "2% to 5%".to_string(), frequency: 12 },
        DistributionPoint { return_range: "> 5%".to_string(), frequency: 3 },
    ];

    ChartData {
        equity_curve: equity_curve.to_vec(),
        drawdown_series,
        monthly_returns_heatmap: monthly_heatmap,
        return_distribution,
    }
}

/// 生成建议
fn generate_recommendations(total_return: f64, sharpe_ratio: f64, max_drawdown: f64, win_rate: f64) -> Vec<String> {
    let mut recommendations = Vec::new();

    if total_return > 20.0 {
        recommendations.push("✅ 策略收益表现优秀，建议继续使用当前参数".to_string());
    } else if total_return > 10.0 {
        recommendations.push("📊 策略收益表现良好，可考虑适度优化参数".to_string());
    } else {
        recommendations.push("⚠️ 策略收益偏低，建议重新评估策略逻辑".to_string());
    }

    if sharpe_ratio > 1.5 {
        recommendations.push("✅ 风险调整后收益优秀，策略风险控制良好".to_string());
    } else if sharpe_ratio > 1.0 {
        recommendations.push("📊 风险调整后收益良好，可进一步优化风险管理".to_string());
    } else {
        recommendations.push("⚠️ 风险调整后收益偏低，需要加强风险控制".to_string());
    }

    if max_drawdown < 0.1 {
        recommendations.push("✅ 回撤控制优秀，策略稳定性很好".to_string());
    } else if max_drawdown < 0.2 {
        recommendations.push("📊 回撤控制良好，建议设置更严格的止损".to_string());
    } else {
        recommendations.push("⚠️ 回撤过大，必须改进风险管理机制".to_string());
    }

    if win_rate > 60.0 {
        recommendations.push("✅ 胜率表现优秀，策略选股/择时能力强".to_string());
    } else if win_rate > 50.0 {
        recommendations.push("📊 胜率表现一般，可优化入场时机".to_string());
    } else {
        recommendations.push("⚠️ 胜率偏低，需要改进策略信号质量".to_string());
    }

    // 添加通用建议
    recommendations.push("💡 建议定期回测验证策略有效性".to_string());
    recommendations.push("💡 建议在不同市场环境下测试策略鲁棒性".to_string());
    recommendations.push("💡 建议结合基准对比分析策略超额收益".to_string());

    recommendations
}

/// 生成HTML报告
async fn generate_html_report(report: &SimpleBacktestReport) -> SigmaXResult<()> {
    println!("📄 生成HTML报告...");

    let html_content = format!(r#"
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SigmaX完整回测报告 - {}</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: #f8f9fa; }}
        .container {{ max-width: 1400px; margin: 0 auto; background: white; padding: 40px; border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.1); }}
        .header {{ text-align: center; margin-bottom: 40px; padding-bottom: 20px; border-bottom: 3px solid #007bff; }}
        .header h1 {{ color: #2c3e50; margin: 0; font-size: 2.5em; }}
        .header .subtitle {{ color: #6c757d; margin-top: 10px; font-size: 1.1em; }}

        .section {{ margin: 30px 0; }}
        .section h2 {{ color: #343a40; border-left: 4px solid #007bff; padding-left: 15px; margin-bottom: 20px; }}

        .metrics-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 20px; margin: 20px 0; }}
        .metric-card {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 12px; text-align: center; }}
        .metric-card.positive {{ background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); }}
        .metric-card.negative {{ background: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%); }}
        .metric-card.neutral {{ background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); color: #333; }}

        .metric-value {{ font-size: 2.2em; font-weight: bold; margin-bottom: 8px; }}
        .metric-label {{ font-size: 0.95em; opacity: 0.9; }}

        .chart-container {{ margin: 30px 0; height: 400px; background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .chart-grid {{ display: grid; grid-template-columns: 1fr 1fr; gap: 30px; }}

        .summary-box {{ background: #e3f2fd; padding: 25px; border-radius: 10px; margin: 20px 0; border-left: 5px solid #2196f3; }}
        .recommendations {{ background: #f3e5f5; padding: 25px; border-radius: 10px; margin: 20px 0; border-left: 5px solid #9c27b0; }}
        .recommendations ul {{ list-style: none; padding: 0; }}
        .recommendations li {{ margin: 10px 0; padding: 10px; background: white; border-radius: 6px; }}

        .performance-grade {{ display: inline-block; padding: 10px 20px; border-radius: 25px; font-weight: bold; margin: 10px 0; }}
        .grade-excellent {{ background: #4caf50; color: white; }}
        .grade-good {{ background: #ff9800; color: white; }}
        .grade-average {{ background: #2196f3; color: white; }}
        .grade-poor {{ background: #f44336; color: white; }}

        .data-table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
        .data-table th, .data-table td {{ padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }}
        .data-table th {{ background: #f8f9fa; font-weight: 600; }}
        .data-table tr:hover {{ background: #f5f5f5; }}

        @media (max-width: 768px) {{
            .metrics-grid {{ grid-template-columns: 1fr; }}
            .chart-grid {{ grid-template-columns: 1fr; }}
            .container {{ padding: 20px; }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 SigmaX完整回测报告</h1>
            <div class="subtitle">{} | 生成时间: {}</div>
        </div>

        <!-- 执行摘要 -->
        <div class="section">
            <h2>📈 执行摘要</h2>
            <div class="summary-box">
                <p><strong>策略名称:</strong> {}</p>
                <p><strong>回测期间:</strong> {}</p>
                <p><strong>初始资金:</strong> {} → <strong>最终资金:</strong> {}</p>
                <div class="performance-grade grade-excellent">🏆 策略表现: 优秀</div>
            </div>
        </div>

        <!-- 核心指标 -->
        <div class="section">
            <h2>🎯 核心表现指标</h2>
            <div class="metrics-grid">
                <div class="metric-card positive">
                    <div class="metric-value">{}</div>
                    <div class="metric-label">总收益率</div>
                </div>
                <div class="metric-card positive">
                    <div class="metric-value">{}</div>
                    <div class="metric-label">年化收益率</div>
                </div>
                <div class="metric-card neutral">
                    <div class="metric-value">{:.3}</div>
                    <div class="metric-label">夏普比率</div>
                </div>
                <div class="metric-card negative">
                    <div class="metric-value">{}</div>
                    <div class="metric-label">最大回撤</div>
                </div>
                <div class="metric-card positive">
                    <div class="metric-value">{}</div>
                    <div class="metric-label">胜率</div>
                </div>
                <div class="metric-card neutral">
                    <div class="metric-value">{:.2}</div>
                    <div class="metric-label">盈亏比</div>
                </div>
            </div>
        </div>

        <!-- 风险指标 -->
        <div class="section">
            <h2>⚠️ 风险分析</h2>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value">{}</div>
                    <div class="metric-label">波动率</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{:.3}</div>
                    <div class="metric-label">Sortino比率</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{:.3}</div>
                    <div class="metric-label">Calmar比率</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{}</div>
                    <div class="metric-label">VaR (95%)</div>
                </div>
            </div>
        </div>

        <!-- 交易统计 -->
        <div class="section">
            <h2>📊 交易统计</h2>
            <table class="data-table">
                <tr><th>指标</th><th>数值</th><th>说明</th></tr>
                <tr><td>总交易次数</td><td>{}</td><td>回测期间执行的总交易数</td></tr>
                <tr><td>获胜交易</td><td>{}</td><td>盈利的交易次数</td></tr>
                <tr><td>失败交易</td><td>{}</td><td>亏损的交易次数</td></tr>
                <tr><td>平均获利</td><td>{}</td><td>单次获胜交易的平均盈利</td></tr>
                <tr><td>平均亏损</td><td>{}</td><td>单次失败交易的平均亏损</td></tr>
                <tr><td>最大单笔盈利</td><td>{}</td><td>单次交易的最大盈利</td></tr>
                <tr><td>最大单笔亏损</td><td>{}</td><td>单次交易的最大亏损</td></tr>
                <tr><td>平均持仓时间</td><td>{:.1} 天</td><td>每笔交易的平均持有时间</td></tr>
            </table>
        </div>

        <!-- 图表展示 -->
        <div class="section">
            <h2>📈 图表分析</h2>
            <div class="chart-grid">
                <div class="chart-container">
                    <canvas id="equityChart"></canvas>
                </div>
                <div class="chart-container">
                    <canvas id="drawdownChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 建议 -->
        <div class="section">
            <h2>💡 策略建议</h2>
            <div class="recommendations">
                <ul>
                    {}
                </ul>
            </div>
        </div>

        <!-- 免责声明 -->
        <div class="section">
            <div style="background: #fff3cd; padding: 20px; border-radius: 8px; border-left: 4px solid #ffc107;">
                <h3>⚠️ 免责声明</h3>
                <p>本报告仅供参考，不构成投资建议。历史表现不代表未来收益，投资有风险，入市需谨慎。</p>
            </div>
        </div>
    </div>

    <script>
        // 资金曲线图
        const equityCtx = document.getElementById('equityChart').getContext('2d');
        const equityDates = {};
        const equityValues = {};

        new Chart(equityCtx, {{
            type: 'line',
            data: {{
                labels: equityDates,
                datasets: [{{
                    label: '资金曲线',
                    data: equityValues,
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4,
                    fill: true
                }}]
            }},
            options: {{
                responsive: true,
                maintainAspectRatio: false,
                plugins: {{
                    title: {{
                        display: true,
                        text: '资金曲线'
                    }}
                }},
                scales: {{
                    y: {{
                        title: {{
                            display: true,
                            text: '资金 ($)'
                        }}
                    }}
                }}
            }}
        }});

        // 回撤图
        const drawdownCtx = document.getElementById('drawdownChart').getContext('2d');
        const drawdownDates = {};
        const drawdownValues = {};

        new Chart(drawdownCtx, {{
            type: 'line',
            data: {{
                labels: drawdownDates,
                datasets: [{{
                    label: '回撤',
                    data: drawdownValues,
                    borderColor: '#dc3545',
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                    tension: 0.4,
                    fill: true
                }}]
            }},
            options: {{
                responsive: true,
                maintainAspectRatio: false,
                plugins: {{
                    title: {{
                        display: true,
                        text: '回撤分析'
                    }}
                }},
                scales: {{
                    y: {{
                        title: {{
                            display: true,
                            text: '回撤 (%)'
                        }},
                        min: 0
                    }}
                }}
            }}
        }});
    </script>
</body>
</html>
"#,
        report.metadata.strategy_name,
        report.metadata.strategy_name,
        report.metadata.generated_at,
        report.metadata.strategy_name,
        report.metadata.backtest_period,
        report.metadata.initial_capital,
        report.metadata.final_capital,
        report.performance_summary.total_return,
        report.performance_summary.annualized_return,
        report.risk_metrics.sharpe_ratio,
        report.risk_metrics.max_drawdown,
        report.trading_statistics.win_rate,
        report.trading_statistics.profit_factor,
        report.risk_metrics.volatility,
        report.risk_metrics.sortino_ratio,
        report.risk_metrics.calmar_ratio,
        report.risk_metrics.var_95,
        report.trading_statistics.total_trades,
        report.trading_statistics.winning_trades,
        report.trading_statistics.losing_trades,
        report.trading_statistics.average_win,
        report.trading_statistics.average_loss,
        report.trading_statistics.largest_win,
        report.trading_statistics.largest_loss,
        report.trading_statistics.average_holding_period,
        report.recommendations.iter()
            .map(|r| format!("<li>{}</li>", r))
            .collect::<Vec<_>>()
            .join(""),
        serde_json::to_string(&report.charts.equity_curve.iter().map(|p| &p.date).collect::<Vec<_>>()).unwrap(),
        serde_json::to_string(&report.charts.equity_curve.iter().map(|p| p.value).collect::<Vec<_>>()).unwrap(),
        serde_json::to_string(&report.charts.drawdown_series.iter().map(|p| &p.date).collect::<Vec<_>>()).unwrap(),
        serde_json::to_string(&report.charts.drawdown_series.iter().map(|p| p.drawdown * 100.0).collect::<Vec<_>>()).unwrap()
    );

    fs::write("comprehensive_backtest_report.html", html_content)
        .map_err(|e| sigmax_core::SigmaXError::InvalidOperation(format!("写入HTML文件失败: {}", e)))?;

    println!("✅ HTML报告已生成: comprehensive_backtest_report.html");
    Ok(())
}

/// 生成JSON报告
async fn generate_json_report(report: &SimpleBacktestReport) -> SigmaXResult<()> {
    println!("📊 生成JSON报告...");

    let json_content = serde_json::to_string_pretty(report)
        .map_err(|e| sigmax_core::SigmaXError::InvalidOperation(format!("JSON序列化失败: {}", e)))?;

    fs::write("backtest_report.json", json_content)
        .map_err(|e| sigmax_core::SigmaXError::InvalidOperation(format!("写入JSON文件失败: {}", e)))?;

    println!("✅ JSON报告已生成: backtest_report.json");
    Ok(())
}

/// 生成PDF报告 (模拟)
async fn generate_pdf_report_simulation() -> SigmaXResult<()> {
    println!("📋 生成PDF报告 (模拟)...");

    // 模拟PDF生成过程
    let pdf_content = r#"
%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
/Resources <<
/Font <<
/F1 5 0 R
>>
>>
>>
endobj

4 0 obj
<<
/Length 200
>>
stream
BT
/F1 24 Tf
100 700 Td
(SigmaX Backtest Report) Tj
0 -50 Td
/F1 12 Tf
(This is a simulated PDF report.) Tj
0 -20 Td
(In a real implementation, this would contain) Tj
0 -20 Td
(comprehensive charts and analysis.) Tj
ET
endstream
endobj

5 0 obj
<<
/Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj

xref
0 6
0000000000 65535 f
0000000009 00000 n
0000000058 00000 n
0000000115 00000 n
0000000274 00000 n
0000000526 00000 n
trailer
<<
/Size 6
/Root 1 0 R
>>
startxref
623
%%EOF
"#;

    fs::write("backtest_report_summary.pdf", pdf_content)
        .map_err(|e| sigmax_core::SigmaXError::InvalidOperation(format!("写入PDF文件失败: {}", e)))?;

    println!("✅ PDF报告已生成: backtest_report_summary.pdf (模拟)");
    println!("💡 实际应用中可使用 wkhtmltopdf 或 puppeteer 生成真实PDF");
    Ok(())
}

/// 显示报告摘要
fn display_report_summary(report: &SimpleBacktestReport) {
    println!("\n📋 回测报告摘要");
    println!("{}", "=".repeat(60));

    println!("📊 策略信息:");
    println!("   策略名称: {}", report.metadata.strategy_name);
    println!("   回测期间: {}", report.metadata.backtest_period);
    println!("   初始资金: {}", report.metadata.initial_capital);
    println!("   最终资金: {}", report.metadata.final_capital);

    println!("\n📈 核心指标:");
    println!("   总收益率: {}", report.performance_summary.total_return);
    println!("   年化收益率: {}", report.performance_summary.annualized_return);
    println!("   最大回撤: {}", report.risk_metrics.max_drawdown);
    println!("   夏普比率: {:.3}", report.risk_metrics.sharpe_ratio);
    println!("   胜率: {}", report.trading_statistics.win_rate);

    println!("\n⚠️ 风险指标:");
    println!("   波动率: {}", report.risk_metrics.volatility);
    println!("   Sortino比率: {:.3}", report.risk_metrics.sortino_ratio);
    println!("   Calmar比率: {:.3}", report.risk_metrics.calmar_ratio);
    println!("   VaR (95%): {}", report.risk_metrics.var_95);

    println!("\n📊 交易统计:");
    println!("   总交易数: {}", report.trading_statistics.total_trades);
    println!("   获胜交易: {}", report.trading_statistics.winning_trades);
    println!("   失败交易: {}", report.trading_statistics.losing_trades);
    println!("   盈亏比: {:.2}", report.trading_statistics.profit_factor);

    println!("\n💡 关键建议:");
    for (i, recommendation) in report.recommendations.iter().take(3).enumerate() {
        println!("   {}. {}", i + 1, recommendation);
    }

    // 性能评级
    let grade = determine_overall_grade(report);
    println!("\n🏆 综合评级: {}", grade);

    println!("\n📄 报告文件:");
    println!("   HTML报告: comprehensive_backtest_report.html");
    println!("   JSON数据: backtest_report.json");
    println!("   PDF摘要: backtest_report_summary.pdf");
}

/// 确定综合评级
fn determine_overall_grade(report: &SimpleBacktestReport) -> String {
    let total_return: f64 = report.performance_summary.total_return
        .trim_end_matches('%')
        .parse()
        .unwrap_or(0.0);

    let max_drawdown: f64 = report.risk_metrics.max_drawdown
        .trim_end_matches('%')
        .parse()
        .unwrap_or(100.0);

    let sharpe_ratio = report.risk_metrics.sharpe_ratio;

    let win_rate: f64 = report.trading_statistics.win_rate
        .trim_end_matches('%')
        .parse()
        .unwrap_or(0.0);

    // 综合评分算法
    let return_score = (total_return / 20.0).min(5.0).max(0.0);
    let drawdown_score = (5.0 - max_drawdown / 4.0).min(5.0).max(0.0);
    let sharpe_score = (sharpe_ratio * 2.5).min(5.0).max(0.0);
    let winrate_score = (win_rate / 20.0).min(5.0).max(0.0);

    let total_score = (return_score + drawdown_score + sharpe_score + winrate_score) / 4.0;

    match total_score {
        s if s >= 4.5 => "🏆 A+ (优秀)".to_string(),
        s if s >= 4.0 => "🥇 A (良好)".to_string(),
        s if s >= 3.5 => "🥈 B+ (中上)".to_string(),
        s if s >= 3.0 => "🥉 B (中等)".to_string(),
        s if s >= 2.5 => "📊 C+ (中下)".to_string(),
        s if s >= 2.0 => "📉 C (较差)".to_string(),
        _ => "❌ D (很差)".to_string(),
    }
}