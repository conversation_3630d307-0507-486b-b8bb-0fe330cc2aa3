//! 统一配置验证器
//!
//! 集中所有引擎配置的验证逻辑，提高一致性和可维护性

use sigmax_core::{EngineType, EngineConfig, SigmaXResult, SigmaXError, Amount};
use std::collections::HashMap;

/// 配置验证器trait
pub trait ConfigValidator: Send + Sync {
    /// 验证引擎配置
    fn validate(&self, config: &EngineConfig) -> SigmaXResult<()>;
    
    /// 获取支持的引擎类型
    fn supported_engine_type(&self) -> EngineType;
    
    /// 获取验证器描述
    fn description(&self) -> &str;
}

/// 回测引擎配置验证器
pub struct BacktestConfigValidator;

impl ConfigValidator for BacktestConfigValidator {
    fn validate(&self, config: &EngineConfig) -> SigmaXResult<()> {
        // 回测引擎需要历史数据配置
        if config.exchange_config.is_none() {
            return Err(SigmaXError::Config(
                "Backtest engine requires exchange config for data source".to_string()
            ));
        }
        
        // 验证交易对
        if config.trading_pairs.is_empty() {
            return Err(SigmaXError::Config(
                "Backtest engine requires at least one trading pair".to_string()
            ));
        }
        
        Ok(())
    }
    
    fn supported_engine_type(&self) -> EngineType {
        EngineType::Backtest
    }
    
    fn description(&self) -> &str {
        "回测引擎配置验证器"
    }
}

/// 实盘引擎配置验证器
pub struct LiveConfigValidator;

impl ConfigValidator for LiveConfigValidator {
    fn validate(&self, config: &EngineConfig) -> SigmaXResult<()> {
        // 实盘引擎需要交易所配置
        if config.exchange_config.is_none() {
            return Err(SigmaXError::Config(
                "Live engine requires exchange config".to_string()
            ));
        }
        
        // 验证风险控制参数
        if config.max_position_size.is_some() && config.max_position_size.unwrap() <= Amount::ZERO {
            return Err(SigmaXError::Config(
                "Live engine max position size must be positive".to_string()
            ));
        }
        
        Ok(())
    }
    
    fn supported_engine_type(&self) -> EngineType {
        EngineType::Live
    }
    
    fn description(&self) -> &str {
        "实盘引擎配置验证器"
    }
}

/// 纸上交易引擎配置验证器
pub struct PaperConfigValidator;

impl ConfigValidator for PaperConfigValidator {
    fn validate(&self, config: &EngineConfig) -> SigmaXResult<()> {
        // 纸上交易引擎相对宽松，主要验证基本参数
        if config.trading_pairs.is_empty() {
            return Err(SigmaXError::Config(
                "Paper engine requires at least one trading pair".to_string()
            ));
        }
        
        Ok(())
    }
    
    fn supported_engine_type(&self) -> EngineType {
        EngineType::Paper
    }
    
    fn description(&self) -> &str {
        "纸上交易引擎配置验证器"
    }
}

/// 模拟引擎配置验证器
pub struct SimulationConfigValidator;

impl ConfigValidator for SimulationConfigValidator {
    fn validate(&self, config: &EngineConfig) -> SigmaXResult<()> {
        // 模拟引擎使用回测验证逻辑
        BacktestConfigValidator.validate(config)
    }
    
    fn supported_engine_type(&self) -> EngineType {
        EngineType::Simulation
    }
    
    fn description(&self) -> &str {
        "模拟引擎配置验证器"
    }
}

/// 统一配置管理器
pub struct ConfigurationManager {
    validators: HashMap<EngineType, Box<dyn ConfigValidator>>,
}

impl ConfigurationManager {
    /// 创建新的配置管理器
    pub fn new() -> Self {
        let mut validators: HashMap<EngineType, Box<dyn ConfigValidator>> = HashMap::new();
        
        validators.insert(EngineType::Backtest, Box::new(BacktestConfigValidator));
        validators.insert(EngineType::Live, Box::new(LiveConfigValidator));
        validators.insert(EngineType::Paper, Box::new(PaperConfigValidator));
        validators.insert(EngineType::Simulation, Box::new(SimulationConfigValidator));
        
        Self { validators }
    }
    
    /// 验证并丰富配置
    pub fn validate_and_enrich(&self, engine_type: &EngineType, config: &mut EngineConfig) -> SigmaXResult<()> {
        // 基础配置检查
        self.validate_basic_config(config)?;
        
        // 类型特定验证
        if let Some(validator) = self.validators.get(engine_type) {
            validator.validate(config)?;
        } else {
            return Err(SigmaXError::Config(
                format!("Unsupported engine type: {:?}", engine_type)
            ));
        }
        
        // 丰富默认值
        self.enrich_config(engine_type, config)?;
        
        Ok(())
    }
    
    /// 基础配置验证
    fn validate_basic_config(&self, config: &EngineConfig) -> SigmaXResult<()> {
        // 验证初始资金
        if config.initial_capital <= Amount::ZERO {
            return Err(SigmaXError::Config(
                "Engine requires positive initial capital".to_string()
            ));
        }
        
        // 验证交易对
        if config.trading_pairs.is_empty() {
            return Err(SigmaXError::Config(
                "Engine requires at least one trading pair".to_string()
            ));
        }
        
        // 验证交易对格式
        for pair in &config.trading_pairs {
            if pair.base.is_empty() || pair.quote.is_empty() {
                return Err(SigmaXError::Config(
                    "Trading pair base and quote cannot be empty".to_string()
                ));
            }
        }
        
        Ok(())
    }
    
    /// 丰富配置默认值
    fn enrich_config(&self, engine_type: &EngineType, config: &mut EngineConfig) -> SigmaXResult<()> {
        // 设置引擎类型
        config.engine_type = *engine_type;
        
        // 根据引擎类型设置默认值
        match engine_type {
            EngineType::Live => {
                // 实盘引擎默认设置
                if config.max_orders_per_second.is_none() {
                    config.max_orders_per_second = Some(10); // 限制每秒订单数
                }
                if config.risk_check_timeout_ms.is_none() {
                    config.risk_check_timeout_ms = Some(100); // 100ms风控超时
                }
            }
            EngineType::Backtest => {
                // 回测引擎默认设置
                if config.batch_size.is_none() {
                    config.batch_size = Some(1000); // 批量处理大小
                }
            }
            EngineType::Paper => {
                // 纸上交易默认设置
                if config.simulate_latency_ms.is_none() {
                    config.simulate_latency_ms = Some(50); // 模拟50ms延迟
                }
            }
            EngineType::Simulation => {
                // 模拟引擎默认设置
                if config.batch_size.is_none() {
                    config.batch_size = Some(500); // 较小的批量处理
                }
            }
        }
        
        Ok(())
    }
    
    /// 获取支持的引擎类型
    pub fn get_supported_engine_types(&self) -> Vec<EngineType> {
        self.validators.keys().cloned().collect()
    }
    
    /// 获取引擎类型描述
    pub fn get_engine_description(&self, engine_type: &EngineType) -> Option<&str> {
        self.validators.get(engine_type).map(|v| v.description())
    }
}

impl Default for ConfigurationManager {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use sigmax_core::TradingPair;

    fn create_basic_config() -> EngineConfig {
        EngineConfig::new(
            EngineType::Paper,
            vec![TradingPair::new("BTC".to_string(), "USDT".to_string())],
            Amount::from(10000)
        )
    }

    #[test]
    fn test_configuration_manager_validation() {
        let manager = ConfigurationManager::new();
        let mut config = create_basic_config();
        
        // 测试有效配置
        assert!(manager.validate_and_enrich(&EngineType::Paper, &mut config).is_ok());
        
        // 测试无效配置 - 零初始资金
        let mut invalid_config = config.clone();
        invalid_config.initial_capital = Amount::ZERO;
        assert!(manager.validate_and_enrich(&EngineType::Paper, &mut invalid_config).is_err());
    }

    #[test]
    fn test_backtest_validator() {
        let validator = BacktestConfigValidator;
        let mut config = create_basic_config();
        
        // 缺少exchange_config应该失败
        assert!(validator.validate(&config).is_err());
        
        // 添加exchange_config后应该通过
        config.exchange_config = Some(sigmax_core::ExchangeConfig {
            name: "test".to_string(),
            api_key: None,
            secret_key: None,
            testnet: true,
            rate_limit: None,
        });
        assert!(validator.validate(&config).is_ok());
    }

    #[test]
    fn test_live_validator() {
        let validator = LiveConfigValidator;
        let mut config = create_basic_config();
        
        // 缺少exchange_config应该失败
        assert!(validator.validate(&config).is_err());
        
        // 添加exchange_config后应该通过
        config.exchange_config = Some(sigmax_core::ExchangeConfig {
            name: "test".to_string(),
            api_key: Some("key".to_string()),
            secret_key: Some("secret".to_string()),
            testnet: false,
            rate_limit: Some(10),
        });
        assert!(validator.validate(&config).is_ok());
    }
}