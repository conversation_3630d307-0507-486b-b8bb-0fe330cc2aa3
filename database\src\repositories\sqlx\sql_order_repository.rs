//! SQL 订单仓储实现

use std::sync::Arc;
use sqlx::Row;
use sigmax_core::{
    Order, OrderId, OrderStatus, StrategyId, SigmaXResult, SigmaXError,
    TradingPair, OrderSide, OrderType, ExchangeId,
};
use crate::DatabaseManager;
use crate::repositories::traits::OrderRepository;

/// SQL订单仓库实现
pub struct SqlOrderRepository {
    db: Arc<DatabaseManager>,
}

impl SqlOrderRepository {
    pub fn new(db: Arc<DatabaseManager>) -> Self {
        Self { db }
    }
}

impl OrderRepository for SqlOrderRepository {
    async fn save_order(&self, order: &Order) -> SigmaXResult<()> {
        let pool = self.db.pool();

        sqlx::query(
            r#"
            INSERT INTO orders (
                id, trading_pair_id, order_type, side, quantity, price, status,
                filled_quantity, average_price, exchange_id, exchange_order_id,
                strategy_id, created_at, updated_at
            ) VALUES (
                $1,
                (SELECT id FROM trading_pairs WHERE symbol = $2),
                $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14
            )
            ON CONFLICT (id) DO UPDATE SET
                status = EXCLUDED.status,
                filled_quantity = EXCLUDED.filled_quantity,
                average_price = EXCLUDED.average_price,
                updated_at = EXCLUDED.updated_at
            "#
        )
        .bind(&order.id)
        .bind(&order.trading_pair.symbol())
        .bind(&format!("{:?}", order.order_type))
        .bind(&format!("{:?}", order.side))
        .bind(&order.quantity)
        .bind(&order.price)
        .bind(&format!("{:?}", order.status))
        .bind(&order.filled_quantity)
        .bind(&order.average_price)
        .bind(&order.exchange_id.to_string())
        .bind(None::<String>) // exchange_order_id
        .bind(&order.strategy_id)
        .bind(&order.created_at)
        .bind(&order.updated_at)
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("Failed to save order: {}", e)))?;

        Ok(())
    }

    async fn get_order(&self, order_id: OrderId) -> SigmaXResult<Option<Order>> {
        let pool = self.db.pool();

        let row = sqlx::query(
            r#"
            SELECT o.*, tp.symbol as trading_pair_symbol
            FROM orders o
            JOIN trading_pairs tp ON o.trading_pair_id = tp.id
            WHERE o.id = $1
            "#
        )
        .bind(&order_id)
        .fetch_optional(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("Failed to get order: {}", e)))?;

        if let Some(row) = row {
            Ok(Some(self.row_to_order(row)?))
        } else {
            Ok(None)
        }
    }

    async fn get_all_orders(&self) -> SigmaXResult<Vec<Order>> {
        let pool = self.db.pool();

        let rows = sqlx::query(
            r#"
            SELECT o.*, tp.symbol as trading_pair_symbol
            FROM orders o
            JOIN trading_pairs tp ON o.trading_pair_id = tp.id
            ORDER BY o.created_at DESC
            "#
        )
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("Failed to get all orders: {}", e)))?;

        let mut orders = Vec::new();
        for row in rows {
            orders.push(self.row_to_order(row)?);
        }

        Ok(orders)
    }

    async fn get_orders_by_status(&self, status: OrderStatus) -> SigmaXResult<Vec<Order>> {
        let pool = self.db.pool();

        let rows = sqlx::query(
            r#"
            SELECT o.*, tp.symbol as trading_pair_symbol
            FROM orders o
            JOIN trading_pairs tp ON o.trading_pair_id = tp.id
            WHERE o.status = $1
            ORDER BY o.created_at DESC
            "#
        )
        .bind(&format!("{:?}", status))
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("Failed to get orders by status: {}", e)))?;

        let mut orders = Vec::new();
        for row in rows {
            orders.push(self.row_to_order(row)?);
        }

        Ok(orders)
    }

    async fn get_orders_by_strategy(&self, strategy_id: StrategyId) -> SigmaXResult<Vec<Order>> {
        let pool = self.db.pool();

        let rows = sqlx::query(
            r#"
            SELECT o.*, tp.symbol as trading_pair_symbol
            FROM orders o
            JOIN trading_pairs tp ON o.trading_pair_id = tp.id
            WHERE o.strategy_id = $1
            ORDER BY o.created_at DESC
            "#
        )
        .bind(&strategy_id)
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("Failed to get orders by strategy: {}", e)))?;

        let mut orders = Vec::new();
        for row in rows {
            orders.push(self.row_to_order(row)?);
        }

        Ok(orders)
    }
}

impl SqlOrderRepository {
    fn row_to_order(&self, row: sqlx::postgres::PgRow) -> SigmaXResult<Order> {
        let trading_pair_symbol: String = row.get("trading_pair_symbol");
        let trading_pair = TradingPair::from_symbol(&trading_pair_symbol)
            .map_err(|e| SigmaXError::Database(format!("Invalid trading pair: {}", e)))?;

        let order_type_str: String = row.get("order_type");
        let order_type = match order_type_str.as_str() {
            "Market" => OrderType::Market,
            "Limit" => OrderType::Limit,
            "StopLoss" => OrderType::StopLoss,
            "StopLimit" => OrderType::StopLimit,
            _ => return Err(SigmaXError::Database(format!("Unknown order type: {}", order_type_str))),
        };

        let side_str: String = row.get("side");
        let side = match side_str.as_str() {
            "Buy" => OrderSide::Buy,
            "Sell" => OrderSide::Sell,
            _ => return Err(SigmaXError::Database(format!("Unknown order side: {}", side_str))),
        };

        let status_str: String = row.get("status");
        let status = match status_str.as_str() {
            "Pending" => OrderStatus::Pending,
            "PartiallyFilled" => OrderStatus::PartiallyFilled,
            "Filled" => OrderStatus::Filled,
            "Cancelled" => OrderStatus::Cancelled,
            "Rejected" => OrderStatus::Rejected,
            "Expired" => OrderStatus::Expired,
            _ => return Err(SigmaXError::Database(format!("Unknown order status: {}", status_str))),
        };

        let exchange_id_str: String = row.get("exchange_id");
        let exchange_id = ExchangeId::from(exchange_id_str);

        Ok(Order {
            id: row.get("id"),
            strategy_id: row.get("strategy_id"),
            exchange_id,
            trading_pair,
            side,
            order_type,
            quantity: row.get("quantity"),
            price: row.get("price"),
            stop_price: None, // TODO: 添加到数据库表
            status,
            filled_quantity: row.get("filled_quantity"),
            average_price: row.get("average_price"),
            created_at: row.get("created_at"),
            updated_at: row.get("updated_at"),
        })
    }
}
