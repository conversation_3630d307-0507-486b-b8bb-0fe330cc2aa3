/**
 * 策略验证控制器模块
 * 协调策略验证的服务层和视图层
 */

class StrategyValidationController {
    constructor() {
        this.service = new StrategyValidationService();
        this.view = new StrategyValidationView();
        this.strategies = [];
        this.tradingPairs = [];
        this.currentValidationResult = null;
    }

    /**
     * 初始化控制器
     * @param {HTMLElement} container - 容器元素
     */
    async init(container) {
        try {
            // 渲染视图
            this.view.render(container);

            // 设置视图回调
            this.view.setCallbacks({
                onStrategyTypeChange: this.handleStrategyTypeChange.bind(this),
                onValidateStrategy: this.handleValidateStrategy.bind(this),
                onApplyTemplate: this.handleApplyTemplate.bind(this)
            });

            // 加载初始数据
            await this.loadInitialData();

            console.log('策略验证控制器初始化完成');
        } catch (error) {
            console.error('策略验证控制器初始化失败:', error);
            this.showError('初始化失败: ' + error.message);
        }
    }

    /**
     * 加载初始数据
     */
    async loadInitialData() {
        try {
            // 获取支持的策略类型 - 使用异步方法
            this.strategies = await this.service.getSupportedStrategyTypes();
            this.view.populateStrategyTypes(this.strategies);

            // 获取交易对列表
            this.tradingPairs = this.service.getTradingPairs();
            this.view.populateTradingPairs(this.tradingPairs);

            console.log('初始数据加载完成:', {
                strategies: this.strategies.length,
                tradingPairs: this.tradingPairs.length
            });
        } catch (error) {
            console.error('加载初始数据失败:', error);
            // 显示用户友好的错误信息
            this.showError('加载策略数据失败，请检查网络连接后重试');
            throw error;
        }
    }

    /**
     * 处理策略类型变化
     * @param {string} strategyType - 策略类型
     */
    handleStrategyTypeChange(strategyType) {
        try {
            if (!strategyType) {
                this.view.hideStrategyConfig();
                this.view.hideStrategyInfo();
                this.view.hideValidationResult();
                return;
            }

            // 查找策略信息
            const strategy = this.strategies.find(s => s.id === strategyType);
            if (!strategy) {
                console.error('未找到策略类型:', strategyType);
                return;
            }

            // 显示策略配置
            this.view.showStrategyConfig(strategy);

            // 清除之前的验证结果
            this.view.hideValidationResult();
            this.currentValidationResult = null;

            console.log('策略类型已切换:', strategy.name);
        } catch (error) {
            console.error('处理策略类型变化失败:', error);
            this.showError('切换策略类型失败: ' + error.message);
        }
    }

    /**
     * 处理策略验证
     */
    async handleValidateStrategy() {
        try {
            // 获取表单数据
            const formData = this.view.getFormData();

            // 验证表单数据
            const validationErrors = this.validateFormData(formData);
            if (validationErrors.length > 0) {
                this.showValidationErrors(validationErrors);
                return;
            }

            // 显示加载状态
            this.view.showLoading();

            // 构建验证请求
            const validationRequest = this.service.buildValidationRequest(formData);

            console.log('开始验证策略:', validationRequest);

            // 调用验证API
            const result = await this.service.validateStrategy(validationRequest);

            // 格式化验证结果
            this.currentValidationResult = this.service.formatValidationResult(result);

            // 显示验证结果
            this.view.showValidationResult(this.currentValidationResult);

            console.log('策略验证完成:', this.currentValidationResult);

        } catch (error) {
            console.error('策略验证失败:', error);
            this.showError('策略验证失败: ' + error.message);
        } finally {
            this.view.hideLoading();
        }
    }

    /**
     * 处理应用配置模板
     * @param {string} template - 模板类型
     */
    handleApplyTemplate(template) {
        try {
            const formData = this.view.getFormData();

            if (!formData.strategyType) {
                this.showError('请先选择策略类型');
                return;
            }

            // 获取当前策略信息
            const strategy = this.strategies.find(s => s.id === formData.strategyType);
            if (!strategy) {
                this.showError('未找到策略信息');
                return;
            }

            // 生成模板配置
            const templateConfig = this.generateTemplateConfig(strategy, template);

            // 应用模板配置
            this.view.setFormData({
                ...formData,
                ...templateConfig
            });

            console.log('已应用配置模板:', template, templateConfig);

        } catch (error) {
            console.error('应用配置模板失败:', error);
            this.showError('应用配置模板失败: ' + error.message);
        }
    }

    /**
     * 验证表单数据
     * @param {Object} formData - 表单数据
     * @returns {Array} 验证错误列表
     */
    validateFormData(formData) {
        const errors = [];

        // 验证策略类型
        if (!formData.strategyType) {
            errors.push('请选择策略类型');
        }

        // 验证交易对
        if (!formData.tradingPair) {
            errors.push('请选择交易对');
        }

        // 验证初始资金
        const capitalValidation = this.service.validateInitialCapital(parseFloat(formData.initialCapital));
        if (!capitalValidation.valid) {
            errors.push(capitalValidation.message);
        }

        // 验证策略配置参数
        if (formData.strategyType && Object.keys(formData.config).length === 0) {
            errors.push('请配置策略参数');
        }

        return errors;
    }

    /**
     * 生成模板配置
     * @param {Object} strategy - 策略信息
     * @param {string} template - 模板类型
     * @returns {Object} 模板配置
     */
    generateTemplateConfig(strategy, template) {
        const baseConfig = { ...strategy.defaultConfig };

        // 根据模板类型调整配置
        switch (template) {
            case 'conservative':
                return this.generateConservativeConfig(baseConfig, strategy);
            case 'balanced':
                return this.generateBalancedConfig(baseConfig, strategy);
            case 'aggressive':
                return this.generateAggressiveConfig(baseConfig, strategy);
            default:
                return { config: baseConfig, initialCapital: 10000 };
        }
    }

    /**
     * 生成保守型配置
     * @param {Object} baseConfig - 基础配置
     * @param {Object} strategy - 策略信息
     * @returns {Object} 保守型配置
     */
    generateConservativeConfig(baseConfig, strategy) {
        const config = { ...baseConfig };

        if (strategy.id === 'asymmetric_volatility_grid_strategy') {
            config.grid_spacing = Math.max(config.grid_spacing * 1.5, 0.015); // 增大网格间距
            config.volatility_threshold = Math.max(config.volatility_threshold * 0.8, 0.015); // 降低波动率阈值
            config.grid_multiplier = Math.min(config.grid_multiplier * 0.8, 1.2); // 降低网格倍数
            config.stop_loss_percentage = Math.min(config.stop_loss_percentage * 0.6, 3.0); // 降低止损
        }

        return {
            config,
            initialCapital: 5000,
            tradingPair: 'BTCUSDT' // 选择相对稳定的交易对
        };
    }

    /**
     * 生成平衡型配置
     * @param {Object} baseConfig - 基础配置
     * @param {Object} strategy - 策略信息
     * @returns {Object} 平衡型配置
     */
    generateBalancedConfig(baseConfig, strategy) {
        return {
            config: baseConfig, // 使用默认配置
            initialCapital: 10000,
            tradingPair: 'ETHUSDT'
        };
    }

    /**
     * 生成激进型配置
     * @param {Object} baseConfig - 基础配置
     * @param {Object} strategy - 策略信息
     * @returns {Object} 激进型配置
     */
    generateAggressiveConfig(baseConfig, strategy) {
        const config = { ...baseConfig };

        if (strategy.id === 'asymmetric_volatility_grid_strategy') {
            config.grid_spacing = Math.min(config.grid_spacing * 0.7, 0.007); // 减小网格间距
            config.volatility_threshold = Math.min(config.volatility_threshold * 1.3, 0.035); // 提高波动率阈值
            config.grid_multiplier = Math.min(config.grid_multiplier * 1.5, 2.5); // 提高网格倍数
            config.stop_loss_percentage = Math.min(config.stop_loss_percentage * 1.5, 8.0); // 提高止损
        }

        return {
            config,
            initialCapital: 20000,
            tradingPair: 'BNBUSDT' // 选择波动性较大的交易对
        };
    }

    /**
     * 显示验证错误
     * @param {Array} errors - 错误列表
     */
    showValidationErrors(errors) {
        const result = {
            valid: false,
            errors: errors,
            warnings: [],
            suggestions: []
        };

        this.view.showValidationResult(result);
    }

    /**
     * 显示错误信息
     * @param {string} message - 错误信息
     */
    showError(message) {
        // 可以使用通知组件或者简单的alert
        console.error(message);
        alert(message); // 临时使用alert，后续可以替换为更好的通知组件
    }

    /**
     * 获取当前验证结果
     * @returns {Object|null} 当前验证结果
     */
    getCurrentValidationResult() {
        return this.currentValidationResult;
    }

    /**
     * 重置控制器状态
     */
    reset() {
        this.currentValidationResult = null;
        this.view.resetForm();
    }

    /**
     * 销毁控制器
     */
    destroy() {
        // 清理资源
        this.currentValidationResult = null;
        this.strategies = [];
        this.tradingPairs = [];
    }
}

// 导出控制器类
window.StrategyValidationController = StrategyValidationController;
