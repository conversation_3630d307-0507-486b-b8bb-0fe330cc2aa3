//! 风控策略层
//!
//! 实现各种风控策略和规则

use sigmax_core::Order;
use sigmax_interfaces::risk::{RiskPolicy, PolicyResult, RiskContext, Portfolio};
use serde::{Deserialize, Serialize};
use rust_decimal::Decimal;

// ============================================================================
// 基础规则策略
// ============================================================================

/// 基础规则策略
pub struct BasicRules {
    config: BasicRuleConfig,
}

impl BasicRules {
    /// 创建新的基础规则策略
    pub fn new(config: BasicRuleConfig) -> Self {
        Self { config }
    }

    /// 使用默认配置创建基础规则策略
    pub fn with_default_config() -> Self {
        Self::new(BasicRuleConfig::default())
    }

    /// 验证订单基础规则
    pub fn validate_order(&self, order: &Order, _context: &RiskContext) -> PolicyResult {
        if !self.config.enabled {
            return PolicyResult::Approved;
        }

        // 1. 检查订单金额
        if let Some(price) = order.price {
            let order_value = order.quantity * price;
            if order_value > self.config.max_order_value {
                return PolicyResult::Rejected(format!(
                    "Order value {} exceeds maximum allowed {}",
                    order_value, self.config.max_order_value
                ));
            }
            if order_value < self.config.min_order_value {
                return PolicyResult::Rejected(format!(
                    "Order value {} below minimum required {}",
                    order_value, self.config.min_order_value
                ));
            }
        }

        // 2. 检查订单数量
        if order.quantity > self.config.max_order_quantity {
            return PolicyResult::Rejected(format!(
                "Order quantity {} exceeds maximum allowed {}",
                order.quantity, self.config.max_order_quantity
            ));
        }

        // 3. 检查交易对是否在黑名单中
        let trading_pair_str = format!("{}_{}", order.trading_pair.base, order.trading_pair.quote);
        if self.config.blacklisted_pairs.contains(&trading_pair_str) {
            return PolicyResult::Rejected(format!(
                "Trading pair {} is blacklisted",
                trading_pair_str
            ));
        }

        PolicyResult::Approved
    }
}

impl RiskPolicy for BasicRules {
    fn evaluate(&self, context: &RiskContext) -> PolicyResult {
        if let Some(order) = &context.order {
            self.validate_order(order, context)
        } else {
            PolicyResult::Approved
        }
    }

    fn name(&self) -> &str {
        "BasicRules"
    }

    fn description(&self) -> &str {
        "Basic trading rules including order size limits and blacklist checks"
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BasicRuleConfig {
    /// 是否启用基础规则
    pub enabled: bool,
    /// 最大订单价值
    pub max_order_value: Decimal,
    /// 最小订单价值
    pub min_order_value: Decimal,
    /// 最大订单数量
    pub max_order_quantity: Decimal,
    /// 黑名单交易对
    pub blacklisted_pairs: Vec<String>,
}

impl Default for BasicRuleConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            max_order_value: Decimal::from(1_000_000),
            min_order_value: Decimal::from(1),
            max_order_quantity: Decimal::from(100_000),
            blacklisted_pairs: vec![],
        }
    }
}

// ============================================================================
// 持仓限制策略
// ============================================================================

/// 持仓限制策略
pub struct PositionLimits {
    config: PositionLimitConfig,
}

impl PositionLimits {
    /// 创建新的持仓限制策略
    pub fn new(config: PositionLimitConfig) -> Self {
        Self { config }
    }

    /// 使用默认配置创建持仓限制策略
    pub fn with_default_config() -> Self {
        Self::new(PositionLimitConfig::default())
    }

    /// 检查持仓限制
    pub fn check_position_limits(&self, portfolio: &Portfolio, new_order: Option<&Order>) -> PolicyResult {
        if !self.config.enabled {
            return PolicyResult::Approved;
        }

        let total_value = portfolio.total_value;
        if total_value <= Decimal::ZERO {
            return PolicyResult::Approved;
        }

        // 1. 检查单一资产持仓比例
        for balance in &portfolio.balances {
            let asset_value = balance.total * balance.price.unwrap_or(Decimal::ONE);
            let asset_ratio = asset_value / total_value;

            if asset_ratio > self.config.max_single_asset_ratio {
                return PolicyResult::Rejected(format!(
                    "Asset {} position ratio {:.2}% exceeds maximum allowed {:.2}%",
                    balance.asset,
                    asset_ratio * Decimal::from(100),
                    self.config.max_single_asset_ratio * Decimal::from(100)
                ));
            }
        }

        // 2. 检查总持仓价值
        if total_value > self.config.max_total_position_value {
            return PolicyResult::Rejected(format!(
                "Total position value {} exceeds maximum allowed {}",
                total_value, self.config.max_total_position_value
            ));
        }

        // 3. 如果有新订单，检查新订单后的持仓限制
        if let Some(order) = new_order {
            if let Some(result) = self.check_new_order_impact(portfolio, order) {
                return result;
            }
        }

        PolicyResult::Approved
    }

    /// 检查新订单对持仓的影响
    fn check_new_order_impact(&self, portfolio: &Portfolio, order: &Order) -> Option<PolicyResult> {
        let order_value = if let Some(price) = order.price {
            order.quantity * price
        } else {
            // 对于市价单，使用当前市价估算
            order.quantity * Decimal::from(50000) // 假设价格
        };

        let new_total_value = portfolio.total_value + order_value;

        // 检查新的总持仓价值
        if new_total_value > self.config.max_total_position_value {
            return Some(PolicyResult::Rejected(format!(
                "Order would cause total position value to exceed limit: {} > {}",
                new_total_value, self.config.max_total_position_value
            )));
        }

        // 检查新的资产持仓比例
        let target_asset = match order.side {
            sigmax_core::OrderSide::Buy => &order.trading_pair.base,
            sigmax_core::OrderSide::Sell => &order.trading_pair.quote,
        };

        let current_asset_value = portfolio.balances
            .iter()
            .find(|b| b.asset == *target_asset)
            .map(|b| b.total * b.price.unwrap_or(Decimal::ONE))
            .unwrap_or_default();

        let new_asset_value = match order.side {
            sigmax_core::OrderSide::Buy => current_asset_value + order_value,
            sigmax_core::OrderSide::Sell => current_asset_value - order_value,
        };

        let new_asset_ratio = new_asset_value / new_total_value;

        if new_asset_ratio > self.config.max_single_asset_ratio {
            return Some(PolicyResult::Rejected(format!(
                "Order would cause {} position ratio to exceed limit: {:.2}% > {:.2}%",
                target_asset,
                new_asset_ratio * Decimal::from(100),
                self.config.max_single_asset_ratio * Decimal::from(100)
            )));
        }

        None
    }
}

impl RiskPolicy for PositionLimits {
    fn evaluate(&self, context: &RiskContext) -> PolicyResult {
        if let Some(portfolio) = &context.portfolio {
            self.check_position_limits(portfolio, context.order.as_ref())
        } else {
            PolicyResult::RequiresApproval("Portfolio data unavailable".to_string())
        }
    }

    fn name(&self) -> &str {
        "PositionLimits"
    }

    fn description(&self) -> &str {
        "Position size and concentration limits"
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PositionLimitConfig {
    /// 是否启用持仓限制
    pub enabled: bool,
    /// 最大单一资产持仓比例
    pub max_single_asset_ratio: Decimal,
    /// 最大总持仓价值
    pub max_total_position_value: Decimal,
    /// 最大杠杆倍数
    pub max_leverage: Decimal,
}

impl Default for PositionLimitConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            max_single_asset_ratio: Decimal::from_str_exact("0.3").unwrap(), // 30%
            max_total_position_value: Decimal::from(10_000_000),
            max_leverage: Decimal::from(5),
        }
    }
}

// ============================================================================
// 波动率控制策略
// ============================================================================

/// 波动率控制策略
pub struct VolatilityControl {
    config: VolatilityConfig,
}

impl VolatilityControl {
    /// 创建新的波动率控制策略
    pub fn new(config: VolatilityConfig) -> Self {
        Self { config }
    }

    /// 使用默认配置创建波动率控制策略
    pub fn with_default_config() -> Self {
        Self::new(VolatilityConfig::default())
    }

    /// 检查波动率限制
    pub fn check_volatility(&self, context: &RiskContext) -> PolicyResult {
        if !self.config.enabled {
            return PolicyResult::Approved;
        }

        // 检查市场波动率
        if let Some(market_data) = &context.market_data {
            let volatility = self.calculate_volatility(market_data);

            if volatility > self.config.max_volatility {
                return PolicyResult::Rejected(format!(
                    "Market volatility {:.2}% exceeds maximum allowed {:.2}%",
                    volatility * Decimal::from(100),
                    self.config.max_volatility * Decimal::from(100)
                ));
            }

            // 检查价格偏离
            if let Some(order) = &context.order {
                if let Some(order_price) = order.price {
                    let price_deviation = (order_price - market_data.price).abs() / market_data.price;

                    if price_deviation > self.config.max_price_deviation {
                        return PolicyResult::Rejected(format!(
                            "Order price deviation {:.2}% exceeds maximum allowed {:.2}%",
                            price_deviation * Decimal::from(100),
                            self.config.max_price_deviation * Decimal::from(100)
                        ));
                    }
                }
            }
        } else {
            // 没有市场数据时，根据配置决定是否允许交易
            if self.config.require_market_data {
                return PolicyResult::RequiresApproval("Market data unavailable for volatility check".to_string());
            }
        }

        PolicyResult::Approved
    }

    /// 计算市场波动率
    fn calculate_volatility(&self, market_data: &sigmax_interfaces::risk::MarketData) -> Decimal {
        // 使用24小时高低价计算简单波动率
        if let (Some(high), Some(low)) = (market_data.high_24h, market_data.low_24h) {
            let price = market_data.price;
            if price > Decimal::ZERO {
                return (high - low) / price;
            }
        }

        // 如果没有高低价数据，使用默认波动率
        self.config.default_volatility
    }
}

impl RiskPolicy for VolatilityControl {
    fn evaluate(&self, context: &RiskContext) -> PolicyResult {
        self.check_volatility(context)
    }

    fn name(&self) -> &str {
        "VolatilityControl"
    }

    fn description(&self) -> &str {
        "Market volatility and price deviation controls"
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VolatilityConfig {
    /// 是否启用波动率控制
    pub enabled: bool,
    /// 最大允许波动率
    pub max_volatility: Decimal,
    /// 最大价格偏离
    pub max_price_deviation: Decimal,
    /// 是否要求市场数据
    pub require_market_data: bool,
    /// 默认波动率（当无法计算时使用）
    pub default_volatility: Decimal,
}

impl Default for VolatilityConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            max_volatility: Decimal::from_str_exact("0.5").unwrap(), // 50%
            max_price_deviation: Decimal::from_str_exact("0.05").unwrap(), // 5%
            require_market_data: true,
            default_volatility: Decimal::from_str_exact("0.2").unwrap(), // 20%
        }
    }
}

// ============================================================================
// 策略组合器
// ============================================================================

/// 策略组合器 - 用于组合多个策略
pub struct PolicyCombinator {
    policies: Vec<Box<dyn RiskPolicy>>,
    require_all_pass: bool,
}

impl PolicyCombinator {
    /// 创建新的策略组合器
    pub fn new(require_all_pass: bool) -> Self {
        Self {
            policies: Vec::new(),
            require_all_pass,
        }
    }

    /// 添加策略
    pub fn add_policy(&mut self, policy: Box<dyn RiskPolicy>) {
        self.policies.push(policy);
    }

    /// 评估所有策略
    pub fn evaluate_all(&self, context: &RiskContext) -> PolicyResult {
        let mut results = Vec::new();
        let mut has_rejection = false;
        let mut approval_messages = Vec::new();
        let mut rejection_messages = Vec::new();

        for policy in &self.policies {
            let result = policy.evaluate(context);

            match &result {
                PolicyResult::Approved => {
                    approval_messages.push(format!("{}: Approved", policy.name()));
                },
                PolicyResult::Rejected(msg) => {
                    has_rejection = true;
                    rejection_messages.push(format!("{}: {}", policy.name(), msg));
                },
                PolicyResult::RequiresApproval(msg) => {
                    approval_messages.push(format!("{}: {}", policy.name(), msg));
                },
                PolicyResult::Warning(msg) => {
                    // 警告不阻止执行，但记录
                    tracing::warn!("Policy '{}' warning: {}", policy.name(), msg);
                    approval_messages.push(format!("{}: Warning - {}", policy.name(), msg));
                },
                PolicyResult::Error(msg) => {
                    has_rejection = true;
                    rejection_messages.push(format!("{}: Error - {}", policy.name(), msg));
                },
            }

            results.push(result);
        }

        if self.require_all_pass {
            // 所有策略都必须通过
            if has_rejection {
                PolicyResult::Rejected(rejection_messages.join("; "))
            } else if approval_messages.iter().any(|msg| msg.contains("RequiresApproval")) {
                PolicyResult::RequiresApproval(approval_messages.join("; "))
            } else {
                PolicyResult::Approved
            }
        } else {
            // 只要有一个策略通过即可
            if results.iter().any(|r| matches!(r, PolicyResult::Approved)) {
                PolicyResult::Approved
            } else if has_rejection {
                PolicyResult::Rejected(rejection_messages.join("; "))
            } else {
                PolicyResult::RequiresApproval(approval_messages.join("; "))
            }
        }
    }
}

impl RiskPolicy for PolicyCombinator {
    fn evaluate(&self, context: &RiskContext) -> PolicyResult {
        self.evaluate_all(context)
    }

    fn name(&self) -> &str {
        "PolicyCombinator"
    }

    fn description(&self) -> &str {
        "Combines multiple risk policies"
    }
}