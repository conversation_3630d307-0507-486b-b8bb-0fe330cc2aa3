#!/usr/bin/env python3
"""
检查数据库表结构
"""

import psycopg2
import sys

# Neon数据库连接参数
NEON_DATABASE_URL = "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"

def check_table_structure(cursor, table_name):
    """检查表结构"""
    print(f"\n📋 {table_name} 表结构:")
    print("-" * 50)
    
    cursor.execute(f"""
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = '{table_name}' 
        AND table_schema = 'public'
        ORDER BY ordinal_position;
    """)
    
    columns = cursor.fetchall()
    if columns:
        for col in columns:
            nullable = "NULL" if col[2] == "YES" else "NOT NULL"
            default = f" DEFAULT {col[3]}" if col[3] else ""
            print(f"  {col[0]:<25} {col[1]:<20} {nullable}{default}")
    else:
        print(f"  ❌ 表 {table_name} 不存在")

def main():
    """主函数"""
    print("🔍 SigmaX 数据库表结构检查")
    print("=" * 50)
    
    try:
        print("📡 连接到Neon数据库...")
        conn = psycopg2.connect(NEON_DATABASE_URL)
        cursor = conn.cursor()
        
        print("✅ 数据库连接成功")
        
        # 检查策略相关表
        tables_to_check = [
            'strategies',
            'strategy_performance', 
            'strategy_trades',
            'strategy_positions',
            'risk_rules',
            'risk_checks',
            'risk_violations',
            'risk_statistics'
        ]
        
        for table in tables_to_check:
            check_table_structure(cursor, table)
        
        print("\n" + "=" * 50)
        print("✅ 表结构检查完成!")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
