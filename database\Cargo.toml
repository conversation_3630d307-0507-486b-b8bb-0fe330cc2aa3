[package]
name = "sigmax-database"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
repository.workspace = true

[features]
default = []
diesel = ["dep:diesel", "dep:diesel_migrations"]

[dependencies]
sigmax-core.workspace = true
tokio.workspace = true
serde.workspace = true
serde_json.workspace = true
chrono.workspace = true
uuid.workspace = true
rust_decimal.workspace = true
anyhow.workspace = true
thiserror.workspace = true
tracing.workspace = true
sqlx.workspace = true
async-trait.workspace = true

# Diesel ORM 依赖 (可选)
diesel = { workspace = true, optional = true }
diesel_migrations = { workspace = true, optional = true }
