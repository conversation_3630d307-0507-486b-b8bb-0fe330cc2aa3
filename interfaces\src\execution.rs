//! 执行相关接口定义
//!
//! 定义执行引擎、引擎管理等接口契约

use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use sigmax_core::{Order, EngineType, SigmaXResult};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use std::collections::HashMap;

// ============================================================================
// 核心执行接口
// ============================================================================

/// 执行引擎接口 - 专注执行逻辑
#[async_trait]
pub trait ExecutionEngine: Send + Sync {
    /// 执行订单
    async fn execute_order(&self, order: &Order) -> ExecutionResult;
    
    /// 取消订单
    async fn cancel_order(&self, order_id: &Uuid) -> ExecutionResult;
    
    /// 获取引擎状态
    async fn get_status(&self) -> EngineStatus;
    
    /// 获取引擎类型
    fn engine_type(&self) -> EngineType;
    
    /// 启动引擎
    async fn start(&mut self) -> SigmaXResult<()>;
    
    /// 停止引擎
    async fn stop(&mut self) -> SigmaXResult<()>;
}

/// 引擎管理器接口
#[async_trait]
pub trait EngineManager: Send + Sync {
    /// 创建引擎
    async fn create_engine(&self, config: &EngineConfig) -> SigmaXResult<Box<dyn ExecutionEngine>>;
    
    /// 获取引擎
    async fn get_engine(&self, engine_id: &Uuid) -> Option<&dyn ExecutionEngine>;
    
    /// 移除引擎
    async fn remove_engine(&self, engine_id: &Uuid) -> SigmaXResult<()>;
    
    /// 列出所有引擎
    async fn list_engines(&self) -> Vec<EngineInfo>;
    
    /// 健康检查
    async fn health_check(&self) -> HealthCheckResult;
}

// ============================================================================
// 数据模型
// ============================================================================

/// 执行结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutionResult {
    pub order_id: Uuid,
    pub execution_id: Uuid,
    pub status: ExecutionStatus,
    pub message: String,
    pub executed_at: DateTime<Utc>,
    pub metadata: HashMap<String, serde_json::Value>,
}

/// 执行状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ExecutionStatus {
    /// 已提交
    Submitted,
    /// 执行中
    Executing,
    /// 已完成
    Completed,
    /// 已取消
    Cancelled,
    /// 失败
    Failed(String),
    /// 部分执行
    PartiallyExecuted,
}

/// 引擎状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EngineStatus {
    pub engine_id: Uuid,
    pub engine_type: EngineType,
    pub status: EngineRunStatus,
    pub uptime: std::time::Duration,
    pub last_heartbeat: DateTime<Utc>,
    pub performance_metrics: PerformanceMetrics,
}

/// 引擎运行状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum EngineRunStatus {
    /// 启动中
    Starting,
    /// 运行中
    Running,
    /// 停止中
    Stopping,
    /// 已停止
    Stopped,
    /// 错误
    Error(String),
    /// 维护中
    Maintenance,
}

/// 引擎配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EngineConfig {
    pub engine_type: EngineType,
    pub name: String,
    pub settings: HashMap<String, serde_json::Value>,
    pub resources: ResourceConfig,
}

/// 资源配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceConfig {
    pub max_concurrent_orders: usize,
    pub timeout_ms: u64,
    pub retry_attempts: u32,
    pub memory_limit_mb: Option<usize>,
}

/// 引擎信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EngineInfo {
    pub engine_id: Uuid,
    pub engine_type: EngineType,
    pub name: String,
    pub status: EngineRunStatus,
    pub created_at: DateTime<Utc>,
    pub last_active: DateTime<Utc>,
}

/// 性能指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    pub orders_executed: u64,
    pub average_latency_ms: f64,
    pub success_rate: f64,
    pub error_count: u64,
    pub last_updated: DateTime<Utc>,
}

/// 健康检查结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthCheckResult {
    pub is_healthy: bool,
    pub engine_count: usize,
    pub active_engines: usize,
    pub failed_engines: usize,
    pub last_check: DateTime<Utc>,
    pub details: HashMap<String, serde_json::Value>,
}

// ============================================================================
// 错误类型
// ============================================================================

/// 执行错误
#[derive(Debug, thiserror::Error)]
pub enum ExecutionError {
    #[error("Order execution failed: {reason}")]
    ExecutionFailed { reason: String },
    
    #[error("Order not found: {order_id}")]
    OrderNotFound { order_id: Uuid },
    
    #[error("Engine not available: {engine_type}")]
    EngineNotAvailable { engine_type: String },
    
    #[error("Execution timeout: operation timed out after {timeout_ms}ms")]
    Timeout { timeout_ms: u64 },
    
    #[error("Invalid order state: {current_state}")]
    InvalidOrderState { current_state: String },
    
    #[error("Resource limit exceeded: {resource}")]
    ResourceLimitExceeded { resource: String },
    
    #[error("Configuration error: {message}")]
    Configuration { message: String },
    
    #[error("Network error: {message}")]
    Network { message: String },
    
    #[error("Exchange error: {exchange} - {message}")]
    Exchange { exchange: String, message: String },
}