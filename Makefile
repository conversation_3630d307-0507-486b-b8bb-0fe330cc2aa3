# SigmaX Makefile

.PHONY: help build check test clean run fmt clippy doc

# 默认目标
help:
	@echo "SigmaX 开发工具"
	@echo ""
	@echo "可用命令:"
	@echo "  build    - 构建项目"
	@echo "  check    - 检查代码"
	@echo "  test     - 运行测试"
	@echo "  clean    - 清理构建文件"
	@echo "  run      - 运行主程序"
	@echo "  fmt      - 格式化代码"
	@echo "  clippy   - 运行Clippy检查"
	@echo "  doc      - 生成文档"
	@echo "  setup    - 初始化开发环境"

# 构建项目
build:
	cargo build

# 构建发布版本
build-release:
	cargo build --release

# 检查代码
check:
	cargo check

# 运行测试
test:
	cargo test

# 运行特定模块测试
test-core:
	cargo test --package sigmax-core

test-strategies:
	cargo test --package sigmax-strategies

test-engines:
	cargo test --package sigmax-engines

# 清理构建文件
clean:
	cargo clean

# 运行主程序
run:
	cargo run --bin sigmax

# 格式化代码
fmt:
	cargo fmt

# 运行Clippy检查
clippy:
	cargo clippy -- -D warnings

# 生成文档
doc:
	cargo doc --no-deps --open

# 初始化开发环境
setup:
	@echo "初始化SigmaX开发环境..."
	@if [ ! -f config.toml ]; then \
		cp config.example.toml config.toml; \
		echo "已创建配置文件 config.toml"; \
	fi
	@echo "安装开发依赖..."
	cargo install cargo-watch
	cargo install cargo-audit
	@echo "开发环境初始化完成!"

# 监控文件变化并自动重新构建
watch:
	cargo watch -x check -x test

# 安全审计
audit:
	cargo audit

# 运行所有检查
ci: fmt clippy test
	@echo "所有检查通过!"

# 创建新的策略模板
new-strategy:
	@read -p "输入策略名称: " name; \
	mkdir -p strategies/src/$$name; \
	echo "// $$name 策略实现" > strategies/src/$$name/mod.rs; \
	echo "已创建策略模板: strategies/src/$$name/"

# 创建新的交易所模板
new-exchange:
	@read -p "输入交易所名称: " name; \
	echo "// $$name 交易所实现" > exchange/src/$$name.rs; \
	echo "已创建交易所模板: exchange/src/$$name.rs"
