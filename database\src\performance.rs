//! 数据库性能监控模块
//!
//! 提供查询性能监控、慢查询日志、连接池状态监控等功能

use std::time::Duration;
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc};
use sigmax_core::{SigmaXResult, SigmaXError};
use sqlx::{PgPool, Row};

/// 查询性能指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QueryMetrics {
    /// 查询SQL（脱敏后）
    pub query_hash: String,
    /// 查询类型（SELECT, INSERT, UPDATE, DELETE）
    pub query_type: QueryType,
    /// 执行次数
    pub execution_count: u64,
    /// 总执行时间（毫秒）
    pub total_duration_ms: u64,
    /// 平均执行时间（毫秒）
    pub avg_duration_ms: f64,
    /// 最小执行时间（毫秒）
    pub min_duration_ms: u64,
    /// 最大执行时间（毫秒）
    pub max_duration_ms: u64,
    /// 最后执行时间
    pub last_executed_at: DateTime<Utc>,
    /// 错误次数
    pub error_count: u64,
}

/// 查询类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum QueryType {
    Select,
    Insert,
    Update,
    Delete,
    Transaction,
    Other,
}

/// 慢查询记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SlowQuery {
    /// 查询ID
    pub id: String,
    /// 查询SQL（脱敏后）
    pub query_hash: String,
    /// 原始查询（仅在开发模式下记录）
    pub raw_query: Option<String>,
    /// 执行时间（毫秒）
    pub duration_ms: u64,
    /// 执行时间
    pub executed_at: DateTime<Utc>,
    /// 查询参数数量
    pub param_count: usize,
    /// 错误信息（如果有）
    pub error: Option<String>,
    /// 调用栈信息
    pub stack_trace: Option<String>,
}

/// 连接池状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConnectionPoolStatus {
    /// 总连接数
    pub total_connections: u32,
    /// 活跃连接数
    pub active_connections: u32,
    /// 空闲连接数
    pub idle_connections: u32,
    /// 等待连接的请求数
    pub waiting_requests: u32,
    /// 连接池大小限制
    pub max_connections: u32,
    /// 连接超时时间（秒）
    pub connection_timeout_seconds: u64,
    /// 空闲超时时间（秒）
    pub idle_timeout_seconds: u64,
    /// 最后更新时间
    pub updated_at: DateTime<Utc>,
}

/// 性能监控配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceConfig {
    /// 是否启用性能监控
    pub enabled: bool,
    /// 慢查询阈值（毫秒）
    pub slow_query_threshold_ms: u64,
    /// 是否记录所有查询
    pub log_all_queries: bool,
    /// 是否在开发模式下记录原始SQL
    pub log_raw_sql_in_dev: bool,
    /// 性能指标保留天数
    pub metrics_retention_days: u32,
    /// 慢查询日志保留天数
    pub slow_query_retention_days: u32,
    /// 监控数据刷新间隔（秒）
    pub refresh_interval_seconds: u64,
}

impl Default for PerformanceConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            slow_query_threshold_ms: 1000, // 1秒
            log_all_queries: false,
            log_raw_sql_in_dev: cfg!(debug_assertions),
            metrics_retention_days: 30,
            slow_query_retention_days: 7,
            refresh_interval_seconds: 60,
        }
    }
}

/// 性能监控器
#[derive(Debug)]
pub struct PerformanceMonitor {
    config: PerformanceConfig,
    metrics: Arc<Mutex<HashMap<String, QueryMetrics>>>,
    slow_queries: Arc<Mutex<Vec<SlowQuery>>>,
    pool: Arc<PgPool>,
}

impl PerformanceMonitor {
    /// 创建新的性能监控器
    pub fn new(pool: Arc<PgPool>, config: PerformanceConfig) -> Self {
        Self {
            config,
            metrics: Arc::new(Mutex::new(HashMap::new())),
            slow_queries: Arc::new(Mutex::new(Vec::new())),
            pool,
        }
    }

    /// 记录查询执行
    pub fn record_query_execution(
        &self,
        query: &str,
        duration: Duration,
        error: Option<&str>,
        param_count: usize,
    ) -> SigmaXResult<()> {
        if !self.config.enabled {
            return Ok(());
        }

        let duration_ms = duration.as_millis() as u64;
        let query_hash = self.hash_query(query);
        let query_type = self.detect_query_type(query);
        let now = Utc::now();

        // 更新性能指标
        {
            let mut metrics = self.metrics.lock().unwrap();
            let metric = metrics.entry(query_hash.clone()).or_insert_with(|| QueryMetrics {
                query_hash: query_hash.clone(),
                query_type,
                execution_count: 0,
                total_duration_ms: 0,
                avg_duration_ms: 0.0,
                min_duration_ms: u64::MAX,
                max_duration_ms: 0,
                last_executed_at: now,
                error_count: 0,
            });

            metric.execution_count += 1;
            metric.total_duration_ms += duration_ms;
            metric.avg_duration_ms = metric.total_duration_ms as f64 / metric.execution_count as f64;
            metric.min_duration_ms = metric.min_duration_ms.min(duration_ms);
            metric.max_duration_ms = metric.max_duration_ms.max(duration_ms);
            metric.last_executed_at = now;

            if error.is_some() {
                metric.error_count += 1;
            }
        }

        // 记录慢查询
        if duration_ms >= self.config.slow_query_threshold_ms {
            let slow_query = SlowQuery {
                id: uuid::Uuid::new_v4().to_string(),
                query_hash: query_hash.clone(),
                raw_query: if self.config.log_raw_sql_in_dev {
                    Some(query.to_string())
                } else {
                    None
                },
                duration_ms,
                executed_at: now,
                param_count,
                error: error.map(|e| e.to_string()),
                stack_trace: if cfg!(debug_assertions) {
                    Some(std::backtrace::Backtrace::capture().to_string())
                } else {
                    None
                },
            };

            let mut slow_queries = self.slow_queries.lock().unwrap();
            slow_queries.push(slow_query);

            // 限制慢查询记录数量
            if slow_queries.len() > 1000 {
                slow_queries.drain(0..100); // 删除最旧的100条记录
            }
        }

        Ok(())
    }

    /// 获取查询性能指标
    pub fn get_query_metrics(&self) -> Vec<QueryMetrics> {
        let metrics = self.metrics.lock().unwrap();
        metrics.values().cloned().collect()
    }

    /// 获取慢查询记录
    pub fn get_slow_queries(&self, limit: Option<usize>) -> Vec<SlowQuery> {
        let slow_queries = self.slow_queries.lock().unwrap();
        let mut queries: Vec<_> = slow_queries.iter().cloned().collect();
        queries.sort_by(|a, b| b.executed_at.cmp(&a.executed_at));
        
        if let Some(limit) = limit {
            queries.truncate(limit);
        }
        
        queries
    }

    /// 获取连接池状态
    pub async fn get_connection_pool_status(&self) -> SigmaXResult<ConnectionPoolStatus> {
        let pool = &self.pool;
        
        // 获取连接池基本信息
        let total_connections = pool.size();
        let idle_connections = pool.num_idle();
        
        // 从数据库获取活跃连接信息
        let row = sqlx::query(
            r#"
            SELECT 
                count(*) as total_active,
                count(CASE WHEN state = 'active' THEN 1 END) as active,
                count(CASE WHEN state = 'idle' THEN 1 END) as idle,
                count(CASE WHEN state = 'idle in transaction' THEN 1 END) as idle_in_transaction
            FROM pg_stat_activity 
            WHERE datname = current_database()
            "#
        )
        .fetch_one(pool.as_ref())
        .await
        .map_err(|e| SigmaXError::Database(format!("获取连接池状态失败: {}", e)))?;

        let active_connections = row.get::<i64, _>("active") as u32;
        
        Ok(ConnectionPoolStatus {
            total_connections,
            active_connections,
            idle_connections: idle_connections as u32,
            waiting_requests: 0, // SQLx 不直接提供此信息
            max_connections: pool.options().get_max_connections(),
            connection_timeout_seconds: pool.options().get_acquire_timeout().as_secs(),
            idle_timeout_seconds: pool.options().get_idle_timeout().map(|d| d.as_secs()).unwrap_or(0),
            updated_at: Utc::now(),
        })
    }

    /// 清理过期数据
    pub async fn cleanup_expired_data(&self) -> SigmaXResult<()> {
        let now = Utc::now();
        let metrics_cutoff = now - chrono::Duration::days(self.config.metrics_retention_days as i64);
        let slow_query_cutoff = now - chrono::Duration::days(self.config.slow_query_retention_days as i64);

        // 清理过期的慢查询记录
        {
            let mut slow_queries = self.slow_queries.lock().unwrap();
            slow_queries.retain(|q| q.executed_at > slow_query_cutoff);
        }

        // 清理过期的性能指标
        {
            let mut metrics = self.metrics.lock().unwrap();
            metrics.retain(|_, m| m.last_executed_at > metrics_cutoff);
        }

        Ok(())
    }

    /// 生成查询哈希（脱敏）
    fn hash_query(&self, query: &str) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        // 简单的查询规范化：移除参数值，保留结构
        let normalized = query
            .to_lowercase()
            .replace(char::is_numeric, "?")
            .replace("'", "")
            .replace("\"", "");

        let mut hasher = DefaultHasher::new();
        normalized.hash(&mut hasher);
        format!("query_{:x}", hasher.finish())
    }

    /// 检测查询类型
    fn detect_query_type(&self, query: &str) -> QueryType {
        let query_lower = query.trim().to_lowercase();
        
        if query_lower.starts_with("select") {
            QueryType::Select
        } else if query_lower.starts_with("insert") {
            QueryType::Insert
        } else if query_lower.starts_with("update") {
            QueryType::Update
        } else if query_lower.starts_with("delete") {
            QueryType::Delete
        } else if query_lower.starts_with("begin") || query_lower.starts_with("commit") || query_lower.starts_with("rollback") {
            QueryType::Transaction
        } else {
            QueryType::Other
        }
    }
}

/// 性能监控统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceStats {
    /// 总查询数
    pub total_queries: u64,
    /// 慢查询数
    pub slow_queries_count: u64,
    /// 错误查询数
    pub error_queries_count: u64,
    /// 平均查询时间（毫秒）
    pub avg_query_duration_ms: f64,
    /// 查询类型分布
    pub query_type_distribution: HashMap<QueryType, u64>,
    /// 连接池状态
    pub connection_pool_status: ConnectionPoolStatus,
    /// 统计时间
    pub generated_at: DateTime<Utc>,
}

impl PerformanceMonitor {
    /// 生成性能统计报告
    pub async fn generate_stats(&self) -> SigmaXResult<PerformanceStats> {
        let metrics = self.get_query_metrics();
        let slow_queries = self.get_slow_queries(None);
        let pool_status = self.get_connection_pool_status().await?;

        let total_queries = metrics.iter().map(|m| m.execution_count).sum();
        let error_queries_count = metrics.iter().map(|m| m.error_count).sum();
        let total_duration: u64 = metrics.iter().map(|m| m.total_duration_ms).sum();
        let avg_query_duration_ms = if total_queries > 0 {
            total_duration as f64 / total_queries as f64
        } else {
            0.0
        };

        let mut query_type_distribution = HashMap::new();
        for metric in &metrics {
            *query_type_distribution.entry(metric.query_type).or_insert(0) += metric.execution_count;
        }

        Ok(PerformanceStats {
            total_queries,
            slow_queries_count: slow_queries.len() as u64,
            error_queries_count,
            avg_query_duration_ms,
            query_type_distribution,
            connection_pool_status: pool_status,
            generated_at: Utc::now(),
        })
    }
}
