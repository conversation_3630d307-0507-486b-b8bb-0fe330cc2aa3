//! 风险管理工具模块
//!
//! 提供可复用的风险计算逻辑，包括：
//! - 追踪止损计算
//! - ATR止损计算
//! - 其他风险指标计算
//!
//! ## 设计原则
//! - 纯函数设计，无状态依赖
//! - 可被所有策略复用
//! - 职责单一，专注计算

use sigmax_core::{SigmaXResult, SigmaXError};

/// 止损计算器
///
/// 提供各种止损价格的计算方法
pub struct StopLossCalculator;

impl StopLossCalculator {
    /// 计算追踪止损价格
    ///
    /// # 参数
    /// - `high_water_mark`: 开仓以来的最高价（多头）或最低价（空头）
    /// - `trail_percent`: 追踪止损百分比 (0.0-1.0)
    /// - `is_long`: 是否为多头仓位
    ///
    /// # 返回
    /// 追踪止损的触发价格
    ///
    /// # 示例
    /// ```
    /// use sigmax_strategies::utils::risk::StopLossCalculator;
    ///
    /// // 多头仓位：最高价 $110，追踪5%
    /// let stop_price = StopLossCalculator::trailing_stop_price(110.0, 0.05, true);
    /// assert_eq!(stop_price, 104.5); // $110 * (1 - 0.05)
    ///
    /// // 空头仓位：最低价 $90，追踪5%
    /// let stop_price = StopLossCalculator::trailing_stop_price(90.0, 0.05, false);
    /// assert_eq!(stop_price, 94.5); // $90 * (1 + 0.05)
    /// ```
    pub fn trailing_stop_price(high_water_mark: f64, trail_percent: f64, is_long: bool) -> f64 {
        if is_long {
            // 多头：止损价 = 最高价 * (1 - 追踪百分比)
            high_water_mark * (1.0 - trail_percent)
        } else {
            // 空头：止损价 = 最低价 * (1 + 追踪百分比)
            high_water_mark * (1.0 + trail_percent)
        }
    }

    /// 计算固定百分比止损价格
    ///
    /// # 参数
    /// - `entry_price`: 入场价格
    /// - `stop_percent`: 止损百分比 (0.0-1.0)
    /// - `is_long`: 是否为多头仓位
    ///
    /// # 返回
    /// 固定止损的触发价格
    pub fn fixed_stop_price(entry_price: f64, stop_percent: f64, is_long: bool) -> f64 {
        if is_long {
            // 多头：止损价 = 入场价 * (1 - 止损百分比)
            entry_price * (1.0 - stop_percent)
        } else {
            // 空头：止损价 = 入场价 * (1 + 止损百分比)
            entry_price * (1.0 + stop_percent)
        }
    }

    /// 计算ATR止损价格
    ///
    /// # 参数
    /// - `entry_price`: 入场价格
    /// - `atr_value`: ATR指标值
    /// - `multiplier`: ATR乘数（通常为1.5-3.0）
    /// - `is_long`: 是否为多头仓位
    ///
    /// # 返回
    /// ATR止损的触发价格
    ///
    /// # 说明
    /// ATR止损根据市场波动性动态调整：
    /// - 波动大时，止损距离较远
    /// - 波动小时，止损距离较近
    pub fn atr_stop_price(entry_price: f64, atr_value: f64, multiplier: f64, is_long: bool) -> f64 {
        let stop_distance = atr_value * multiplier;

        if is_long {
            // 多头：止损价 = 入场价 - (ATR * 乘数)
            entry_price - stop_distance
        } else {
            // 空头：止损价 = 入场价 + (ATR * 乘数)
            entry_price + stop_distance
        }
    }

    /// 验证止损配置的有效性
    ///
    /// # 参数
    /// - `trail_percent`: 追踪止损百分比
    /// - `atr_multiplier`: ATR乘数
    ///
    /// # 返回
    /// 验证结果
    pub fn validate_stop_loss_config(trail_percent: Option<f64>, atr_multiplier: Option<f64>) -> SigmaXResult<()> {
        if let Some(percent) = trail_percent {
            if percent <= 0.0 || percent >= 1.0 {
                return Err(SigmaXError::Config(
                    "追踪止损百分比必须在 0.0-1.0 范围内".to_string()
                ));
            }
        }

        if let Some(multiplier) = atr_multiplier {
            if multiplier <= 0.0 || multiplier > 10.0 {
                return Err(SigmaXError::Config(
                    "ATR乘数必须在 0.0-10.0 范围内".to_string()
                ));
            }
        }

        Ok(())
    }
}

/// ATR（平均真实波幅）计算器
///
/// 用于计算市场波动性指标
pub struct ATRCalculator;

impl ATRCalculator {
    /// 计算真实波幅（True Range）
    ///
    /// # 参数
    /// - `high`: 当前周期最高价
    /// - `low`: 当前周期最低价
    /// - `prev_close`: 前一周期收盘价
    ///
    /// # 返回
    /// 真实波幅值
    pub fn true_range(high: f64, low: f64, prev_close: f64) -> f64 {
        let tr1 = high - low;
        let tr2 = (high - prev_close).abs();
        let tr3 = (low - prev_close).abs();

        tr1.max(tr2).max(tr3)
    }

    /// 计算ATR（简化版本，使用简单移动平均）
    ///
    /// # 参数
    /// - `true_ranges`: 真实波幅历史数据
    /// - `period`: ATR周期（通常为14）
    ///
    /// # 返回
    /// ATR值
    pub fn calculate_atr(true_ranges: &[f64], period: usize) -> Option<f64> {
        if true_ranges.len() < period {
            return None;
        }

        let recent_trs = &true_ranges[true_ranges.len() - period..];
        let sum: f64 = recent_trs.iter().sum();
        Some(sum / period as f64)
    }

    /// 更新ATR（使用指数移动平均，更平滑）
    ///
    /// # 参数
    /// - `prev_atr`: 前一期ATR值
    /// - `current_tr`: 当前真实波幅
    /// - `period`: ATR周期
    ///
    /// # 返回
    /// 新的ATR值
    pub fn update_atr_ema(prev_atr: f64, current_tr: f64, period: usize) -> f64 {
        let alpha = 1.0 / period as f64;
        prev_atr * (1.0 - alpha) + current_tr * alpha
    }
}

/// 风险度量工具
///
/// 提供各种风险指标的计算
pub struct RiskMetrics;

impl RiskMetrics {
    /// 计算仓位风险度
    ///
    /// # 参数
    /// - `position_value`: 仓位价值
    /// - `account_value`: 账户总价值
    ///
    /// # 返回
    /// 风险度百分比 (0.0-1.0)
    pub fn position_risk_ratio(position_value: f64, account_value: f64) -> f64 {
        if account_value <= 0.0 {
            return 0.0;
        }
        (position_value / account_value).abs()
    }

    /// 计算最大可接受仓位大小
    ///
    /// # 参数
    /// - `account_value`: 账户总价值
    /// - `max_risk_percent`: 最大风险百分比 (0.0-1.0)
    /// - `entry_price`: 入场价格
    /// - `stop_price`: 止损价格
    ///
    /// # 返回
    /// 最大可接受的仓位数量
    pub fn max_position_size(
        account_value: f64,
        max_risk_percent: f64,
        entry_price: f64,
        stop_price: f64,
    ) -> f64 {
        let risk_per_unit = (entry_price - stop_price).abs();
        if risk_per_unit <= 0.0 {
            return 0.0;
        }

        let max_risk_amount = account_value * max_risk_percent;
        max_risk_amount / risk_per_unit
    }

    /// 计算盈亏比
    ///
    /// # 参数
    /// - `entry_price`: 入场价格
    /// - `target_price`: 目标价格
    /// - `stop_price`: 止损价格
    ///
    /// # 返回
    /// 盈亏比（潜在盈利/潜在亏损）
    pub fn risk_reward_ratio(entry_price: f64, target_price: f64, stop_price: f64) -> f64 {
        let potential_profit = (target_price - entry_price).abs();
        let potential_loss = (entry_price - stop_price).abs();

        if potential_loss <= 0.0 {
            return f64::INFINITY;
        }

        potential_profit / potential_loss
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_trailing_stop_price() {
        // 多头仓位测试
        let stop_price = StopLossCalculator::trailing_stop_price(110.0, 0.05, true);
        assert_eq!(stop_price, 104.5);

        // 空头仓位测试
        let stop_price = StopLossCalculator::trailing_stop_price(90.0, 0.05, false);
        assert_eq!(stop_price, 94.5);
    }

    #[test]
    fn test_fixed_stop_price() {
        // 多头仓位测试
        let stop_price = StopLossCalculator::fixed_stop_price(100.0, 0.1, true);
        assert!((stop_price - 90.0).abs() < 1e-10);

        // 空头仓位测试
        let stop_price = StopLossCalculator::fixed_stop_price(100.0, 0.1, false);
        assert!((stop_price - 110.0).abs() < 1e-10);
    }

    #[test]
    fn test_atr_stop_price() {
        // 多头仓位测试
        let stop_price = StopLossCalculator::atr_stop_price(100.0, 2.0, 2.0, true);
        assert_eq!(stop_price, 96.0); // 100 - (2.0 * 2.0)

        // 空头仓位测试
        let stop_price = StopLossCalculator::atr_stop_price(100.0, 2.0, 2.0, false);
        assert_eq!(stop_price, 104.0); // 100 + (2.0 * 2.0)
    }

    #[test]
    fn test_true_range() {
        let tr = ATRCalculator::true_range(105.0, 95.0, 98.0);
        assert_eq!(tr, 10.0); // max(105-95, |105-98|, |95-98|) = max(10, 7, 3) = 10
    }

    #[test]
    fn test_calculate_atr() {
        let true_ranges = vec![2.0, 3.0, 1.5, 2.5, 4.0];
        let atr = ATRCalculator::calculate_atr(&true_ranges, 3);
        // (1.5 + 2.5 + 4.0) / 3 = 8.0 / 3 = 2.6666...
        assert!((atr.unwrap() - 8.0/3.0).abs() < 1e-10);
    }

    #[test]
    fn test_position_risk_ratio() {
        let ratio = RiskMetrics::position_risk_ratio(1000.0, 10000.0);
        assert_eq!(ratio, 0.1);
    }

    #[test]
    fn test_max_position_size() {
        let size = RiskMetrics::max_position_size(10000.0, 0.02, 100.0, 95.0);
        assert_eq!(size, 40.0); // (10000 * 0.02) / (100 - 95) = 200 / 5 = 40
    }

    #[test]
    fn test_risk_reward_ratio() {
        let ratio = RiskMetrics::risk_reward_ratio(100.0, 110.0, 95.0);
        assert_eq!(ratio, 2.0); // (110 - 100) / (100 - 95) = 10 / 5 = 2.0
    }
}
