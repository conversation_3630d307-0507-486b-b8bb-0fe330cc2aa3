//! 风控控制子模块
//!
//! 专注实时风控业务逻辑，包括：
//! - 订单验证服务
//! - 持仓监控服务
//! - 风险计算服务
//! - 预警管理服务
//! - 风控策略
//! - 风控模型

// ============================================================================
// 服务层 - 核心业务逻辑
// ============================================================================

/// 风控服务层
pub mod services;

// ============================================================================
// 策略层 - 风控策略实现
// ============================================================================

/// 风控策略层
pub mod policies;

// ============================================================================
// 模型层 - 数据模型定义
// ============================================================================

/// 风控模型层
pub mod models;

// ============================================================================
// 公共导出
// ============================================================================

// 服务层导出
pub use services::{
    OrderValidator, OrderValidationResult, OrderValidationError, OrderValidatorConfig,
    PositionMonitor, PositionStatus, PositionRiskLevel, PositionMonitorError, PositionMonitorConfig, PositionSummary,
    RiskCalculator, RiskCalculationResult, RiskCalculationError, RiskCalculatorConfig,
    AlertManager, AlertResult, AlertManagerError, AlertManagerConfig, AlertStatistics,
};

// 策略层导出
pub use policies::{
    BasicRules, BasicRuleConfig,
    PositionLimits, PositionLimitConfig,
    VolatilityControl, VolatilityConfig,
};

// 模型层导出
pub use models::{
    RiskContextBuilder,
    RiskMetrics, RiskMetricsBuilder,
};

// ============================================================================
// 风控控制器实现
// ============================================================================

use sigmax_interfaces::{
    RiskController, RiskResult,
    RiskContext as InterfaceRiskContext,
    RiskMetrics as InterfaceRiskMetrics,
    risk::RiskAlert
};
use sigmax_core::{Order, SigmaXResult};
use async_trait::async_trait;
use models::RiskContext;

/// 风控控制器实现
pub struct RiskControllerImpl {
    order_validator: OrderValidator,
    position_monitor: PositionMonitor,
    risk_calculator: RiskCalculator,
    alert_manager: AlertManager,
}

impl RiskControllerImpl {
    /// 创建新的风控控制器
    pub fn new(
        order_validator: OrderValidator,
        position_monitor: PositionMonitor,
        risk_calculator: RiskCalculator,
        alert_manager: AlertManager,
    ) -> Self {
        Self {
            order_validator,
            position_monitor,
            risk_calculator,
            alert_manager,
        }
    }
    
    /// 验证订单
    pub async fn validate_order(&self, order: &Order) -> SigmaXResult<OrderValidationResult> {
        // 创建风控上下文
        let context = RiskContext::new().with_order(order.clone());

        // 执行订单验证
        let interface_context = self.convert_to_interface_context(&context);
        let result = self.order_validator.validate_order(order, &interface_context).await;
        Ok(result)
    }

    /// 检查持仓状态
    pub async fn check_position_status(&self, portfolio: &sigmax_interfaces::risk::Portfolio) -> SigmaXResult<PositionStatus> {
        let result = self.position_monitor.check_position_status(portfolio).await;
        Ok(result)
    }

    /// 计算风险指标
    pub async fn calculate_risk(&self, context: &RiskContext) -> SigmaXResult<RiskCalculationResult> {
        let interface_context = self.convert_to_interface_context(context);
        let result = self.risk_calculator.calculate_basic_metrics(&interface_context).await;
        Ok(result)
    }

    /// 检查并生成预警
    pub async fn check_alerts(&mut self, context: &RiskContext) -> SigmaXResult<AlertResult> {
        let interface_context = self.convert_to_interface_context(context);
        let result = self.alert_manager.check_and_alert(&interface_context).await;
        Ok(result)
    }
}

#[async_trait]
impl RiskController for RiskControllerImpl {
    async fn validate_order(&self, order: &Order) -> RiskResult {
        let context = RiskContext::new().with_order(order.clone());

        let interface_context = self.convert_to_interface_context(&context);
        match self.order_validator.validate_order(order, &interface_context).await {
            result if result.is_valid => RiskResult::Approved,
            result => RiskResult::Rejected(result.message),
        }
    }

    async fn check_position_limits(&self, portfolio: &sigmax_interfaces::risk::Portfolio) -> RiskResult {
        match self.position_monitor.check_position_status(portfolio).await {
            status if matches!(status.level, PositionRiskLevel::Safe | PositionRiskLevel::Warning) => {
                RiskResult::Approved
            },
            status if matches!(status.level, PositionRiskLevel::Dangerous) => {
                RiskResult::RequiresApproval(status.message)
            },
            status => RiskResult::Rejected(status.message),
        }
    }

    async fn calculate_risk_metrics(&self, context: &InterfaceRiskContext) -> InterfaceRiskMetrics {
        // 转换接口上下文到内部上下文
        let internal_context = self.convert_interface_context(context);

        // 计算风险指标
        let interface_context = self.convert_to_interface_context(&internal_context);
        let calculation_result = self.risk_calculator.calculate_basic_metrics(&interface_context).await;

        // 转换结果到接口格式
        self.convert_to_interface_metrics(&calculation_result)
    }

    async fn monitor_risk(&self, context: &InterfaceRiskContext) -> Vec<RiskAlert> {
        let internal_context = self.convert_interface_context(context);

        // 这里需要一个可变引用，但接口不允许，所以我们创建一个临时的 AlertManager
        let mut temp_alert_manager = AlertManager::with_default_config();
        let interface_context = self.convert_to_interface_context(&internal_context);
        let alert_result = temp_alert_manager.check_and_alert(&interface_context).await;

        alert_result.alerts
    }
}

impl RiskControllerImpl {
    /// 转换接口上下文到内部上下文
    fn convert_interface_context(&self, context: &InterfaceRiskContext) -> RiskContext {
        RiskContext {
            order: context.order.clone(),
            portfolio: context.portfolio.clone(),
            balances: context.balances.clone(),
            market_data: context.market_data.clone(),
            timestamp: context.timestamp,
            metadata: context.metadata.clone(),
        }
    }
    
    /// 转换内部上下文到接口上下文
    fn convert_to_interface_context(&self, context: &RiskContext) -> InterfaceRiskContext {
        InterfaceRiskContext {
            order: context.order.clone(),
            portfolio: context.portfolio.clone(),
            balances: context.balances.clone(),
            market_data: context.market_data.clone(),
            timestamp: context.timestamp,
            metadata: context.metadata.clone(),
        }
    }

    /// 转换计算结果到接口格式
    fn convert_to_interface_metrics(&self, result: &RiskCalculationResult) -> InterfaceRiskMetrics {
        // 从计算结果中提取基础指标
        let _volatility = result.metrics.get("volatility")
            .and_then(|v| serde_json::from_value::<rust_decimal::Decimal>(v.clone()).ok())
            .unwrap_or_default();

        let leverage = result.metrics.get("leverage")
            .and_then(|v| serde_json::from_value::<rust_decimal::Decimal>(v.clone()).ok())
            .unwrap_or_default();

        let _concentration = result.metrics.get("concentration")
            .and_then(|v| serde_json::from_value::<rust_decimal::Decimal>(v.clone()).ok())
            .unwrap_or_default();

        sigmax_interfaces::risk::RiskMetrics {
            exposure: result.metrics.get("exposure")
                .and_then(|v| serde_json::from_value::<rust_decimal::Decimal>(v.clone()).ok())
                .unwrap_or_default(),
            leverage,
            var: result.metrics.get("var")
                .and_then(|v| serde_json::from_value::<rust_decimal::Decimal>(v.clone()).ok()),
            max_drawdown: result.metrics.get("max_drawdown")
                .and_then(|v| serde_json::from_value::<rust_decimal::Decimal>(v.clone()).ok()),
            sharpe_ratio: result.metrics.get("sharpe_ratio")
                .and_then(|v| serde_json::from_value::<rust_decimal::Decimal>(v.clone()).ok()),
            calculated_at: result.calculated_at,
        }
    }
}