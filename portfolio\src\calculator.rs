//! PnL计算器

use sigmax_core::{Amount, Position, SigmaXResult};

/// PnL计算器
pub struct PnLCalculator;

impl PnLCalculator {
    /// 计算总的已实现盈亏
    pub fn calculate_total_realized_pnl(positions: &[Position]) -> Amount {
        positions.iter()
            .map(|pos| pos.realized_pnl)
            .sum()
    }

    /// 计算总的未实现盈亏
    pub fn calculate_total_unrealized_pnl(positions: &[Position]) -> Amount {
        positions.iter()
            .map(|pos| pos.unrealized_pnl)
            .sum()
    }

    /// 计算总盈亏
    pub fn calculate_total_pnl(positions: &[Position]) -> Amount {
        Self::calculate_total_realized_pnl(positions) + Self::calculate_total_unrealized_pnl(positions)
    }

    /// 计算盈亏百分比
    pub fn calculate_pnl_percentage(total_pnl: Amount, initial_capital: Amount) -> SigmaXResult<rust_decimal::Decimal> {
        if initial_capital == Amount::ZERO {
            return Ok(rust_decimal::Decimal::ZERO);
        }
        Ok((total_pnl / initial_capital) * rust_decimal::Decimal::from(100))
    }

    /// 计算夏普比率
    pub fn calculate_sharpe_ratio(returns: &[rust_decimal::Decimal], risk_free_rate: rust_decimal::Decimal) -> SigmaXResult<rust_decimal::Decimal> {
        if returns.is_empty() {
            return Ok(rust_decimal::Decimal::ZERO);
        }

        let mean_return = returns.iter().sum::<rust_decimal::Decimal>() / rust_decimal::Decimal::from(returns.len());
        let excess_return = mean_return - risk_free_rate;

        // 计算标准差
        let variance = returns.iter()
            .map(|r| (*r - mean_return) * (*r - mean_return))
            .sum::<rust_decimal::Decimal>() / rust_decimal::Decimal::from(returns.len());

        // 简化的标准差计算（实际应用中可能需要更精确的方法）
        let std_dev = if variance > rust_decimal::Decimal::ZERO {
            // 使用f64进行开方运算，然后转回Decimal
            let variance_f64: f64 = variance.to_string().parse().unwrap_or(0.0);
            rust_decimal::Decimal::from_f64_retain(variance_f64.sqrt()).unwrap_or(rust_decimal::Decimal::ZERO)
        } else {
            rust_decimal::Decimal::ZERO
        };

        if std_dev == rust_decimal::Decimal::ZERO {
            return Ok(rust_decimal::Decimal::ZERO);
        }

        Ok(excess_return / std_dev)
    }

    /// 计算最大回撤
    pub fn calculate_max_drawdown(equity_curve: &[Amount]) -> SigmaXResult<rust_decimal::Decimal> {
        if equity_curve.is_empty() {
            return Ok(rust_decimal::Decimal::ZERO);
        }

        let mut max_drawdown = rust_decimal::Decimal::ZERO;
        let mut peak = equity_curve[0];

        for &value in equity_curve.iter().skip(1) {
            if value > peak {
                peak = value;
            } else {
                let drawdown = (peak - value) / peak;
                if drawdown > max_drawdown {
                    max_drawdown = drawdown;
                }
            }
        }

        Ok(max_drawdown)
    }
}
