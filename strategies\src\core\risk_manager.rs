//! 策略切换风险管理模块
//!
//! 提供策略切换的风险评估和安全控制机制
//! 当前实现为基础架构，智能切换功能暂时注释

use serde::{Deserialize, Serialize};
use std::collections::VecDeque;
use chrono::{DateTime, Utc, Duration};
use sigmax_core::{SigmaXResult, RiskManagementConfig};
// 注意：RiskConfigMapper 和 StrategyRiskConfig 已被移除，使用简化的配置管理
use tracing::{info, debug};

use super::strategy_type::StrategyType;
use super::market_signal::{MarketSignal, MarketState};
use super::performance_tracker::PerformanceMetrics;

/// 风险因子类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RiskFactor {
    /// 切换过于频繁
    FrequentSwitching { 
        recent_switches: u32, 
        time_window: Duration 
    },
    /// 当前持有大量仓位
    LargePosition { 
        position_size: f64, 
        max_safe_size: f64 
    },
    /// 市场波动过大
    HighVolatility { 
        current_vol: f64, 
        safe_threshold: f64 
    },
    /// 流动性不足
    LowLiquidity { 
        bid_ask_spread: f64, 
        volume: f64 
    },
    /// 未实现损失过大
    UnrealizedLoss { 
        current_loss: f64, 
        max_acceptable: f64 
    },
    /// 策略性能不佳
    PoorPerformance { 
        current_sharpe: f64, 
        min_acceptable: f64 
    },
}

impl RiskFactor {
    /// 检查风险因子是否阻止切换
    pub fn is_blocking(&self) -> bool {
        match self {
            RiskFactor::FrequentSwitching { recent_switches, .. } => *recent_switches > 5,
            RiskFactor::LargePosition { position_size, max_safe_size } => position_size > max_safe_size,
            RiskFactor::HighVolatility { current_vol, safe_threshold } => current_vol > safe_threshold,
            RiskFactor::LowLiquidity { volume, .. } => *volume < 1000.0, // 简化阈值
            RiskFactor::UnrealizedLoss { current_loss, max_acceptable } => current_loss > max_acceptable,
            RiskFactor::PoorPerformance { current_sharpe, min_acceptable } => current_sharpe < min_acceptable,
        }
    }
    
    /// 获取风险等级 (0.0 - 1.0)
    pub fn risk_level(&self) -> f64 {
        match self {
            RiskFactor::FrequentSwitching { recent_switches, .. } => (*recent_switches as f64 / 10.0).min(1.0),
            RiskFactor::LargePosition { position_size, max_safe_size } => (position_size / max_safe_size).min(1.0),
            RiskFactor::HighVolatility { current_vol, safe_threshold } => (current_vol / safe_threshold).min(1.0),
            RiskFactor::LowLiquidity { bid_ask_spread, .. } => (*bid_ask_spread * 100.0).min(1.0),
            RiskFactor::UnrealizedLoss { current_loss, max_acceptable } => (current_loss / max_acceptable).min(1.0),
            RiskFactor::PoorPerformance { current_sharpe, min_acceptable } => {
                if *min_acceptable > 0.0 {
                    (1.0 - current_sharpe / min_acceptable).max(0.0).min(1.0)
                } else {
                    0.5
                }
            }
        }
    }
}

/// 策略切换风险评估结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SwitchRiskAssessment {
    pub is_safe_to_switch: bool,
    pub overall_risk_score: f64,  // 0.0 - 1.0
    pub risk_factors: Vec<RiskFactor>,
    pub recommended_delay: Option<Duration>,
    pub position_adjustment_needed: bool,
    pub emergency_stop_required: bool,
    pub assessment_time: DateTime<Utc>,
}

/// 风险管理配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskManagerConfig {
    /// 最大风险评分阈值
    pub max_risk_score: f64,
    /// 紧急停止风险阈值
    pub emergency_risk_threshold: f64,
    /// 最大仓位大小（相对于总资金）
    pub max_position_ratio: f64,
    /// 最大波动率阈值
    pub max_volatility_threshold: f64,
    /// 最小夏普比率
    pub min_sharpe_ratio: f64,
    /// 最大未实现损失比例
    pub max_unrealized_loss_ratio: f64,
    /// 切换冷却期
    pub switch_cooldown_period: Duration,
    /// 最大每小时切换次数
    pub max_switches_per_hour: u32,
}

impl Default for RiskManagerConfig {
    fn default() -> Self {
        Self {
            max_risk_score: 0.7,
            emergency_risk_threshold: 0.9,
            max_position_ratio: 0.8,
            max_volatility_threshold: 0.3,
            min_sharpe_ratio: 0.5,
            max_unrealized_loss_ratio: 0.15,
            switch_cooldown_period: Duration::minutes(30),
            max_switches_per_hour: 4,
        }
    }
}

/// 策略切换历史记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SwitchHistoryRecord {
    pub timestamp: DateTime<Utc>,
    pub from_strategy: Option<StrategyType>,
    pub to_strategy: Option<StrategyType>,
    pub risk_score: f64,
    pub market_state: MarketState,
    pub success: bool,
    pub reason: String,
}

/// 策略切换风险管理器
/// 
/// 注意：当前实现为基础架构，智能风险管理功能暂时简化
pub struct StrategySwitchRiskManager {
    /// 配置
    config: RiskManagerConfig,
    /// 切换历史
    switch_history: VecDeque<SwitchHistoryRecord>,
    /// 当前市场条件缓存
    current_market_conditions: Option<MarketSignal>,
    /// 最大历史记录长度
    max_history_length: usize,
}

impl StrategySwitchRiskManager {
    /// 创建新的风险管理器
    pub fn new(config: RiskManagerConfig) -> Self {
        Self {
            config,
            switch_history: VecDeque::new(),
            current_market_conditions: None,
            max_history_length: 100,
        }
    }
    
    /// 使用默认配置创建
    pub fn with_default_config() -> Self {
        Self::new(RiskManagerConfig::default())
    }

    /// 从核心风控配置创建策略风控管理器（简化版本）
    pub fn from_core_config(_core_config: &RiskManagementConfig) -> Self {
        // 简化：直接使用默认配置，不再依赖复杂的配置映射
        Self::new(RiskManagerConfig::default())
    }

    // 复杂的配置转换已移除，使用简化的配置管理

    // 复杂的策略配置导出已移除，不再提供外部配置接口

    /// 更新配置
    pub fn update_config(&mut self, new_config: RiskManagerConfig) {
        info!("更新策略风控配置");
        self.config = new_config;
    }

    /// 从核心配置更新（简化版本）
    pub fn update_from_core_config(&mut self, _core_config: &RiskManagementConfig) {
        // 简化：暂不支持从核心配置更新，使用默认配置
        info!("策略风控配置更新已简化，保持默认配置");
    }
    
    /// 更新市场条件
    pub async fn update_market_conditions(&mut self, market_signal: MarketSignal) -> SigmaXResult<()> {
        self.current_market_conditions = Some(market_signal);
        Ok(())
    }
    
    /// 评估策略切换风险
    /// 
    /// 注意：当前为简化实现，未来可以扩展为更复杂的风险模型
    pub async fn assess_switch_risk(
        &self,
        from_strategy: Option<StrategyType>,
        to_strategy: StrategyType,
        current_performance: Option<&PerformanceMetrics>,
        market_state: &MarketState,
    ) -> SigmaXResult<SwitchRiskAssessment> {
        
        debug!("评估策略切换风险: {:?} -> {:?}", from_strategy, to_strategy);
        
        let mut risk_factors = Vec::new();
        
        // 1. 检查切换频率
        if let Some(freq_risk) = self.check_switching_frequency()? {
            risk_factors.push(freq_risk);
        }
        
        // 2. 检查市场条件
        if let Some(market_risk) = self.check_market_conditions(market_state)? {
            risk_factors.push(market_risk);
        }
        
        // 3. 检查策略性能
        if let Some(performance) = current_performance {
            if let Some(perf_risk) = self.check_performance_risk(performance)? {
                risk_factors.push(perf_risk);
            }
        }
        
        // 4. 计算总体风险评分
        let overall_risk_score = self.calculate_overall_risk_score(&risk_factors);
        
        // 5. 判断是否安全切换
        let is_safe = overall_risk_score < self.config.max_risk_score;
        let emergency_stop = overall_risk_score > self.config.emergency_risk_threshold;
        
        // 6. 计算推荐延迟
        let recommended_delay = if !is_safe {
            Some(self.calculate_recommended_delay(&risk_factors))
        } else {
            None
        };
        
        let assessment = SwitchRiskAssessment {
            is_safe_to_switch: is_safe,
            overall_risk_score,
            risk_factors,
            recommended_delay,
            position_adjustment_needed: false, // 简化实现
            emergency_stop_required: emergency_stop,
            assessment_time: Utc::now(),
        };
        
        debug!("风险评估结果: 安全={}, 风险评分={:.2}", assessment.is_safe_to_switch, assessment.overall_risk_score);
        
        Ok(assessment)
    }
    
    /// 记录切换历史
    pub async fn record_switch(
        &mut self,
        from_strategy: Option<StrategyType>,
        to_strategy: Option<StrategyType>,
        risk_score: f64,
        market_state: &MarketState,
        success: bool,
        reason: String,
    ) -> SigmaXResult<()> {
        
        let record = SwitchHistoryRecord {
            timestamp: Utc::now(),
            from_strategy,
            to_strategy,
            risk_score,
            market_state: market_state.clone(),
            success,
            reason: reason.clone(),
        };

        self.switch_history.push_back(record.clone());

        // 限制历史长度
        while self.switch_history.len() > self.max_history_length {
            self.switch_history.pop_front();
        }

        info!("记录策略切换: {:?} -> {:?}, 成功: {}", record.from_strategy, record.to_strategy, success);
        Ok(())
    }
    
    /// 检查切换频率
    fn check_switching_frequency(&self) -> SigmaXResult<Option<RiskFactor>> {
        let now = Utc::now();
        let one_hour_ago = now - Duration::hours(1);
        
        let recent_switches = self.switch_history.iter()
            .filter(|record| record.timestamp > one_hour_ago)
            .count() as u32;
        
        if recent_switches > self.config.max_switches_per_hour {
            Ok(Some(RiskFactor::FrequentSwitching {
                recent_switches,
                time_window: Duration::hours(1),
            }))
        } else {
            Ok(None)
        }
    }
    
    /// 检查市场条件风险
    fn check_market_conditions(&self, market_state: &MarketState) -> SigmaXResult<Option<RiskFactor>> {
        match market_state {
            MarketState::Chaos { volatility_spike } => {
                if *volatility_spike > self.config.max_volatility_threshold {
                    Ok(Some(RiskFactor::HighVolatility {
                        current_vol: *volatility_spike,
                        safe_threshold: self.config.max_volatility_threshold,
                    }))
                } else {
                    Ok(None)
                }
            }
            MarketState::IlliquidMarket { spread_ratio } => {
                Ok(Some(RiskFactor::LowLiquidity {
                    bid_ask_spread: *spread_ratio,
                    volume: 100.0, // 简化实现
                }))
            }
            _ => Ok(None),
        }
    }
    
    /// 检查策略性能风险
    fn check_performance_risk(&self, performance: &PerformanceMetrics) -> SigmaXResult<Option<RiskFactor>> {
        if performance.sharpe_ratio < self.config.min_sharpe_ratio {
            Ok(Some(RiskFactor::PoorPerformance {
                current_sharpe: performance.sharpe_ratio,
                min_acceptable: self.config.min_sharpe_ratio,
            }))
        } else if performance.max_drawdown > self.config.max_unrealized_loss_ratio {
            Ok(Some(RiskFactor::UnrealizedLoss {
                current_loss: performance.max_drawdown,
                max_acceptable: self.config.max_unrealized_loss_ratio,
            }))
        } else {
            Ok(None)
        }
    }
    
    /// 计算总体风险评分
    fn calculate_overall_risk_score(&self, risk_factors: &[RiskFactor]) -> f64 {
        if risk_factors.is_empty() {
            return 0.0;
        }
        
        // 使用加权平均计算总体风险
        let total_risk: f64 = risk_factors.iter().map(|rf| rf.risk_level()).sum();
        let max_risk = risk_factors.len() as f64;
        
        (total_risk / max_risk).min(1.0)
    }
    
    /// 计算推荐延迟时间
    fn calculate_recommended_delay(&self, risk_factors: &[RiskFactor]) -> Duration {
        let base_delay = self.config.switch_cooldown_period;
        
        // 根据风险因子调整延迟时间
        let risk_multiplier = risk_factors.iter()
            .map(|rf| rf.risk_level())
            .fold(1.0, f64::max);
        
        let delay_minutes = base_delay.num_minutes() as f64 * risk_multiplier;
        Duration::minutes(delay_minutes as i64)
    }
    
    /// 获取切换历史
    pub fn get_switch_history(&self) -> &VecDeque<SwitchHistoryRecord> {
        &self.switch_history
    }
    
    /// 获取最近的风险评分
    pub fn get_recent_risk_scores(&self, count: usize) -> Vec<f64> {
        self.switch_history.iter()
            .rev()
            .take(count)
            .map(|record| record.risk_score)
            .collect()
    }
}
