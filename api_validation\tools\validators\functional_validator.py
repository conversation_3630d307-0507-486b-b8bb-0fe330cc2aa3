#!/usr/bin/env python3
"""
功能验证器 - 验证完整业务流程
包括完整回测流程、数据一致性、业务规则验证等
"""

import asyncio
import time
import requests
import json
from typing import Dict, List, Any, Optional
from urllib3.util import Retry
from requests.adapters import HTTPAdapter
import logging

logger = logging.getLogger(__name__)

class FunctionalValidator:
    """功能验证器"""

    def __init__(self, environment: Dict[str, Any], test_scenarios: Dict[str, Any]):
        self.environment = environment
        self.test_scenarios = test_scenarios
        self.session = self._create_session()
        self.results = []
        self.test_data = {}

    def _create_session(self) -> requests.Session:
        """创建配置了重试机制的会话"""
        session = requests.Session()

        retry_strategy = Retry(
            total=self.environment.get("retry_count", 3),
            backoff_factor=0.1,
            status_forcelist=[502, 503, 504, 429]
        )

        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)

        return session

    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """发送HTTP请求"""
        url = f"{self.environment['api_base_url']}{endpoint}"
        start_time = time.time()

        try:
            response = self.session.request(
                method, url,
                timeout=self.environment.get("timeout", 30),
                **kwargs
            )

            response_time = (time.time() - start_time) * 1000

            result = {
                "success": True,
                "status_code": response.status_code,
                "response_time": response_time,
                "url": url,
                "method": method
            }

            try:
                if response.content:
                    result["data"] = response.json()
                else:
                    result["data"] = {}
            except json.JSONDecodeError:
                result["data"] = {"text": response.text}

            return result

        except requests.exceptions.RequestException as e:
            response_time = (time.time() - start_time) * 1000
            return {
                "success": False,
                "error": str(e),
                "response_time": response_time,
                "url": url,
                "method": method
            }

    async def test_complete_backtest_flow(self) -> Dict[str, Any]:
        """测试完整回测流程"""
        logger.info("测试完整回测流程")

        flow_results = []
        engine_id = None

        try:
            # 1. 创建回测引擎
            logger.info("步骤1: 创建回测引擎")
            create_payload = {
                "engine_type": "Backtest",
                "trading_pairs": [{"base": "BNB", "quote": "USDT"}],
                "initial_capital": "700.00"
            }

            create_result = self._make_request("POST", "/api/engines", json=create_payload)
            flow_results.append({"step": "create_engine", "result": create_result})

            if not create_result["success"] or create_result["status_code"] not in [200, 201]:
                raise Exception("引擎创建失败")

            engine_id = create_result["data"].get("id")
            if not engine_id:
                raise Exception("未获取到引擎ID")

            self.test_data["engine_id"] = engine_id

            # 2. 配置回测参数
            logger.info("步骤2: 配置回测参数")
            config_payload = {
                "data_file": "BNB_USDT_1d.json",
                "start_time": "2023-01-01T00:00:00Z",
                "end_time": "2023-01-31T23:59:59Z",
                "initial_capital": "700.00",
                "trading_pairs": ["BNB_USDT"],
                "timeframe": "1d",
                "strategy_config": {
                    "type": "grid",
                    "config": {
                        "grid_levels": 10,
                        "upper_price": "320.00",
                        "lower_price": "280.00",
                        "base_order_size": "0.1"
                    }
                }
            }

            config_result = self._make_request(
                "POST", f"/api/engines/{engine_id}/backtest/config",
                json=config_payload
            )
            flow_results.append({"step": "configure_backtest", "result": config_result})

            if not config_result["success"] or config_result["status_code"] != 200:
                raise Exception("回测配置失败")

            # 3. 启动回测
            logger.info("步骤3: 启动回测")
            start_result = self._make_request("POST", f"/api/engines/{engine_id}/start")
            flow_results.append({"step": "start_backtest", "result": start_result})

            if not start_result["success"] or start_result["status_code"] != 200:
                raise Exception("回测启动失败")

            # 4. 监控回测进度
            logger.info("步骤4: 监控回测进度")
            progress_results = []
            max_checks = 10

            for i in range(max_checks):
                progress_result = self._make_request("GET", f"/api/engines/{engine_id}/backtest/progress")
                progress_results.append(progress_result)

                if progress_result["success"] and progress_result["data"]:
                    status = progress_result["data"].get("status", "Unknown")
                    logger.info(f"进度检查 {i+1}/{max_checks}: {status}")

                    if status in ["Completed", "Failed"]:
                        break

                await asyncio.sleep(2)

            flow_results.append({"step": "monitor_progress", "result": progress_results})

            # 5. 获取回测结果
            logger.info("步骤5: 获取回测结果")
            result_endpoints = [
                ("result", f"/api/engines/{engine_id}/backtest/result"),
                ("trades", f"/api/engines/{engine_id}/backtest/trades"),
                ("portfolio", f"/api/engines/{engine_id}/backtest/portfolio")
            ]

            for name, endpoint in result_endpoints:
                result = self._make_request("GET", endpoint)
                flow_results.append({"step": f"get_{name}", "result": result})

            # 验证数据一致性
            consistency_check = await self._validate_data_consistency(engine_id)
            flow_results.append({"step": "data_consistency", "result": consistency_check})

            test_result = {
                "test_name": "complete_backtest_flow",
                "success": True,
                "flow_results": flow_results,
                "engine_id": engine_id
            }

        except Exception as e:
            logger.error(f"完整回测流程测试失败: {str(e)}")
            test_result = {
                "test_name": "complete_backtest_flow",
                "success": False,
                "error": str(e),
                "flow_results": flow_results,
                "engine_id": engine_id
            }

        self.results.append(test_result)
        return test_result

    async def _validate_data_consistency(self, engine_id: str) -> Dict[str, Any]:
        """验证数据一致性"""
        logger.info("验证数据一致性")

        consistency_checks = []

        try:
            # 获取引擎信息
            engine_result = self._make_request("GET", f"/api/engines/{engine_id}")
            if engine_result["success"]:
                consistency_checks.append({
                    "check": "engine_exists",
                    "success": True,
                    "data": engine_result["data"]
                })

            # 获取配置信息
            config_result = self._make_request("GET", f"/api/engines/{engine_id}/backtest/config")
            if config_result["success"]:
                consistency_checks.append({
                    "check": "config_exists",
                    "success": True,
                    "data": config_result["data"]
                })

            # 验证结果数据完整性
            result_result = self._make_request("GET", f"/api/engines/{engine_id}/backtest/result")
            if result_result["success"] and result_result["data"]:
                result_data = result_result["data"]

                # 检查必要字段
                required_fields = ["total_return", "sharpe_ratio", "max_drawdown"]
                missing_fields = [field for field in required_fields if field not in result_data]

                consistency_checks.append({
                    "check": "result_completeness",
                    "success": len(missing_fields) == 0,
                    "missing_fields": missing_fields
                })

            return {
                "success": all(check["success"] for check in consistency_checks),
                "checks": consistency_checks
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "checks": consistency_checks
            }

    async def test_strategy_management(self) -> Dict[str, Any]:
        """测试策略管理功能"""
        logger.info("测试策略管理功能")

        strategy_results = []
        strategy_id = None

        try:
            # 1. 创建策略
            create_payload = {
                "name": "Functional Test Grid Strategy",
                "strategy_type": "grid",
                "description": "功能测试网格策略",
                "trading_pairs": ["BNB_USDT"],
                "initial_capital": "1000.00",
                "parameters": {
                    "grid_levels": 8,
                    "upper_price": "320.00",
                    "lower_price": "280.00",
                    "base_order_size": "0.1"
                },
                "risk_config": {
                    "max_position_size": "500.00",
                    "max_daily_loss": "50.00",
                    "max_drawdown": "100.00",
                    "stop_loss_percentage": "5.0",
                    "take_profit_percentage": "10.0",
                    "max_leverage": "1.0"
                }
            }

            create_result = self._make_request("POST", "/api/strategies", json=create_payload)
            strategy_results.append({"step": "create_strategy", "result": create_result})

            if create_result["success"] and create_result["data"]:
                strategy_id = create_result["data"].get("id")
                self.test_data["strategy_id"] = strategy_id

            # 2. 获取策略列表
            list_result = self._make_request("GET", "/api/strategies")
            strategy_results.append({"step": "list_strategies", "result": list_result})

            # 3. 获取策略详情
            if strategy_id:
                detail_result = self._make_request("GET", f"/api/strategies/{strategy_id}")
                strategy_results.append({"step": "get_strategy_detail", "result": detail_result})

            test_result = {
                "test_name": "strategy_management",
                "success": all(r["result"]["success"] for r in strategy_results),
                "strategy_results": strategy_results,
                "strategy_id": strategy_id
            }

        except Exception as e:
            test_result = {
                "test_name": "strategy_management",
                "success": False,
                "error": str(e),
                "strategy_results": strategy_results
            }

        self.results.append(test_result)
        return test_result

    async def test_data_validation(self) -> Dict[str, Any]:
        """测试数据验证功能"""
        logger.info("测试数据验证功能")

        validation_results = []

        try:
            # 1. 获取数据文件列表
            files_result = self._make_request("GET", "/api/backtest/files")
            validation_results.append({"step": "get_files", "result": files_result})

            if files_result["success"] and files_result["data"].get("files"):
                # 2. 验证第一个文件
                first_file = files_result["data"]["files"][0]["filename"]
                validate_result = self._make_request("GET", f"/api/backtest/files/{first_file}/validate")
                validation_results.append({"step": "validate_file", "result": validate_result})

                # 3. 获取文件数据
                data_result = self._make_request("GET", f"/api/backtest/files/{first_file}")
                validation_results.append({"step": "get_file_data", "result": data_result})

                # 4. 获取统计信息
                stats_result = self._make_request("GET", "/api/backtest/stats")
                validation_results.append({"step": "get_stats", "result": stats_result})

            test_result = {
                "test_name": "data_validation",
                "success": all(r["result"]["success"] for r in validation_results),
                "validation_results": validation_results
            }

        except Exception as e:
            test_result = {
                "test_name": "data_validation",
                "success": False,
                "error": str(e),
                "validation_results": validation_results
            }

        self.results.append(test_result)
        return test_result

    async def cleanup_test_resources(self):
        """清理测试资源"""
        logger.info("清理功能测试资源")

        # 清理引擎
        if "engine_id" in self.test_data:
            try:
                engine_id = self.test_data["engine_id"]
                cleanup_result = self._make_request("DELETE", f"/api/engines/{engine_id}")
                logger.info(f"清理引擎 {engine_id}: {cleanup_result['success']}")
            except Exception as e:
                logger.warning(f"清理引擎失败: {str(e)}")

        # 清理策略
        if "strategy_id" in self.test_data:
            try:
                strategy_id = self.test_data["strategy_id"]
                cleanup_result = self._make_request("DELETE", f"/api/strategies/{strategy_id}")
                logger.info(f"清理策略 {strategy_id}: {cleanup_result['success']}")
            except Exception as e:
                logger.warning(f"清理策略失败: {str(e)}")

    async def validate(self) -> Dict[str, Any]:
        """运行功能验证"""
        logger.info("开始功能验证")

        # 运行功能测试
        tests = [
            self.test_complete_backtest_flow(),
            self.test_strategy_management(),
            self.test_data_validation()
        ]

        # 顺序执行测试（功能测试可能有依赖关系）
        for test in tests:
            try:
                await test
            except Exception as e:
                logger.error(f"功能测试执行失败: {str(e)}")
                self.results.append({
                    "test_name": "unknown_test",
                    "success": False,
                    "error": str(e)
                })

        # 清理资源
        await self.cleanup_test_resources()

        # 生成验证报告
        return self._generate_validation_report()

    async def validate_scenario(self, scenario_name: str, scenario: Dict[str, Any]) -> Dict[str, Any]:
        """验证特定场景"""
        logger.info(f"验证场景: {scenario_name}")

        # 根据场景名称选择相应的测试
        if scenario_name == "complete_backtest_flow":
            result = await self.test_complete_backtest_flow()
        elif scenario_name == "backtest_data_management":
            result = await self.test_data_validation()
        else:
            # 默认运行完整流程测试
            result = await self.test_complete_backtest_flow()

        return {
            "scenario": scenario_name,
            "status": "pass" if result["success"] else "fail",
            "result": result
        }

    def _generate_validation_report(self) -> Dict[str, Any]:
        """生成功能验证报告"""
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results if r.get("success", False))
        failed_tests = total_tests - passed_tests

        # 分析功能覆盖
        functional_coverage = {
            "backtest_flow": any(r["test_name"] == "complete_backtest_flow" and r["success"] for r in self.results),
            "strategy_management": any(r["test_name"] == "strategy_management" and r["success"] for r in self.results),
            "data_validation": any(r["test_name"] == "data_validation" and r["success"] for r in self.results)
        }

        report = {
            "status": "pass" if failed_tests == 0 else "fail",
            "test_count": total_tests,
            "passed": passed_tests,
            "failed": failed_tests,
            "success_rate": (passed_tests / total_tests * 100) if total_tests > 0 else 0,
            "functional_coverage": functional_coverage,
            "test_results": self.results,
            "summary": {
                "core_functionality": "OK" if functional_coverage["backtest_flow"] else "FAIL",
                "strategy_features": "OK" if functional_coverage["strategy_management"] else "FAIL",
                "data_integrity": "OK" if functional_coverage["data_validation"] else "FAIL"
            }
        }

        logger.info(f"功能验证完成: {passed_tests}/{total_tests} 通过")
        return report
