//! 智能策略切换模块
//!
//! 提供策略适应性评估和智能切换功能
//! 
//! ## 重要说明
//! 当前实现为基础架构，智能切换功能暂时注释，专注于单策略模式的稳定运行
//! 未来可以通过取消注释相关代码来启用智能切换功能

use serde::{Deserialize, Serialize};
use std::collections::{HashMap, VecDeque};
use chrono::{DateTime, Utc, Duration};
use sigmax_core::{SigmaXResult};
use tracing::{info, debug};

use super::strategy_type::StrategyType;
use super::market_signal::{MarketSignal, MarketState};

/// 策略适应性评估结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StrategyFitness {
    pub strategy_type: StrategyType,
    pub fitness_score: f64,      // 0.0 - 1.0
    pub confidence: f64,         // 0.0 - 1.0
    pub expected_performance: ExpectedPerformance,
    pub risk_assessment: RiskAssessment,
}

/// 预期性能指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExpectedPerformance {
    pub expected_return: f64,
    pub expected_sharpe: f64,
    pub expected_max_drawdown: f64,
    pub win_probability: f64,
}

/// 风险评估
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskAssessment {
    pub volatility_risk: f64,
    pub liquidity_risk: f64,
    pub correlation_risk: f64,
    pub overall_risk: f64,
}

/// 策略切换决策
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SwitchDecision {
    pub action: SwitchAction,
    pub reason: String,
    pub confidence: f64,
    pub recommended_strategy: Option<StrategyType>,
    pub risk_factors: Vec<String>,
    pub timestamp: DateTime<Utc>,
}

/// 切换动作
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum SwitchAction {
    /// 保持当前策略
    Stay,
    /// 切换到新策略
    Switch(StrategyType),
    /// 暂停所有策略
    Pause,
    /// 紧急停止
    EmergencyStop,
}

/// 策略切换历史记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StrategySwitchRecord {
    pub from_strategy: Option<StrategyType>,
    pub to_strategy: Option<StrategyType>,
    pub switch_time: DateTime<Utc>,
    pub reason: String,
    pub market_state: MarketState,
    pub performance_before: Option<f64>,
    pub performance_after: Option<f64>,
}

/// 切换配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SwitchConfig {
    /// 启动策略的最低适应性阈值
    pub start_threshold: f64,
    /// 策略切换的最低改进阈值
    pub switch_threshold: f64,
    /// 紧急停止的适应性阈值
    pub emergency_threshold: f64,
    /// 最小切换间隔（防止频繁切换）
    pub min_switch_interval: Duration,
    /// 最大切换频率（每小时）
    pub max_switches_per_hour: u32,
    /// 置信度阈值
    pub confidence_threshold: f64,
}

impl Default for SwitchConfig {
    fn default() -> Self {
        Self {
            start_threshold: 0.6,
            switch_threshold: 0.15,
            emergency_threshold: 0.2,
            min_switch_interval: Duration::minutes(30),
            max_switches_per_hour: 4,
            confidence_threshold: 0.7,
        }
    }
}

/// 策略适应性评估器
/// 
/// 注意：当前实现为基础架构，智能评估功能暂时简化
pub struct StrategyFitnessEvaluator {
    /// 历史性能数据
    performance_history: HashMap<(StrategyType, MarketState), Vec<f64>>,
    /// 市场-策略适应性矩阵
    fitness_matrix: HashMap<(StrategyType, MarketState), f64>,
}

impl StrategyFitnessEvaluator {
    /// 创建新的策略适应性评估器
    pub fn new() -> Self {
        let mut evaluator = Self {
            performance_history: HashMap::new(),
            fitness_matrix: HashMap::new(),
        };
        
        // 初始化基础适应性矩阵
        evaluator.initialize_fitness_matrix();
        evaluator
    }
    
    /// 初始化策略-市场适应性矩阵
    fn initialize_fitness_matrix(&mut self) {
        use MarketState::*;
        use StrategyType::*;
        
        // 网格策略在震荡市场中表现最佳
        self.fitness_matrix.insert((AsymmetricVolatilityGrid, Sideways { volatility: 0.05, range: Default::default() }), 0.9);
        
        // 简化实现：为其他组合设置默认值
        for strategy_type in [AsymmetricVolatilityGrid] {
            for market_state in [Unknown] {
                self.fitness_matrix.entry((strategy_type, market_state))
                    .or_insert(0.5); // 默认中等适应性
            }
        }
    }
    
    /// 评估策略在当前市场条件下的适应性
    ///
    /// 注意：当前为简化实现，未来可以扩展为更复杂的机器学习模型
    pub async fn evaluate_strategy_fitness(
        &self,
        strategy_type: StrategyType,
        market_signal: &MarketSignal,
        _market_state: &MarketState,
    ) -> SigmaXResult<StrategyFitness> {

        // 简化的适应性评分
        let fitness_score = match (strategy_type, market_signal) {
            // 网格策略在震荡市场中表现最佳
            (StrategyType::AsymmetricVolatilityGrid, MarketSignal::Ranging { volatility, .. }) => {
                if *volatility > 0.02 && *volatility < 0.08 {
                    0.9  // 高适应性
                } else {
                    0.6  // 中等适应性
                }
            }
            
            // 高波动时降低适应性
            (_, MarketSignal::HighVolatility { .. }) => 0.3,
            
            // 流动性不足时大幅降低适应性
            (_, MarketSignal::LowLiquidity { .. }) => 0.1,
            
            // 默认中等适应性
            _ => 0.5,
        };
        
        let confidence = self.calculate_confidence(market_signal);
        
        Ok(StrategyFitness {
            strategy_type,
            fitness_score,
            confidence,
            expected_performance: self.estimate_performance(strategy_type, market_signal),
            risk_assessment: self.assess_risk(strategy_type, market_signal),
        })
    }
    
    /// 计算置信度
    fn calculate_confidence(&self, market_signal: &MarketSignal) -> f64 {
        match market_signal {
            MarketSignal::Ranging { confidence, .. } => *confidence,
            MarketSignal::Trending { confidence, .. } => *confidence,
            MarketSignal::HighVolatility { confidence, .. } => *confidence,
            MarketSignal::LowLiquidity { confidence, .. } => *confidence,
            MarketSignal::Breakout { confidence, .. } => *confidence,
            MarketSignal::NoSignal => 0.3,
        }
    }
    
    /// 估算预期性能
    fn estimate_performance(&self, strategy_type: StrategyType, market_signal: &MarketSignal) -> ExpectedPerformance {
        // 简化实现：基于策略类型和市场信号的静态估算
        match (strategy_type, market_signal) {
            (StrategyType::AsymmetricVolatilityGrid, MarketSignal::Ranging { .. }) => {
                ExpectedPerformance {
                    expected_return: 0.15,  // 15% 年化收益
                    expected_sharpe: 1.2,
                    expected_max_drawdown: 0.08,
                    win_probability: 0.65,
                }
            }
            _ => {
                ExpectedPerformance {
                    expected_return: 0.08,
                    expected_sharpe: 0.8,
                    expected_max_drawdown: 0.12,
                    win_probability: 0.55,
                }
            }
        }
    }
    
    /// 评估风险
    fn assess_risk(&self, _strategy_type: StrategyType, market_signal: &MarketSignal) -> RiskAssessment {
        let volatility_risk = match market_signal {
            MarketSignal::HighVolatility { vix_level, .. } => *vix_level,
            MarketSignal::Ranging { volatility, .. } => *volatility * 0.5,
            _ => 0.3,
        };
        
        let liquidity_risk = match market_signal {
            MarketSignal::LowLiquidity { spread_ratio, .. } => (*spread_ratio - 1.0).min(1.0),
            _ => 0.2,
        };
        
        let correlation_risk = 0.3; // 简化实现
        let overall_risk = (volatility_risk + liquidity_risk + correlation_risk) / 3.0;
        
        RiskAssessment {
            volatility_risk,
            liquidity_risk,
            correlation_risk,
            overall_risk,
        }
    }
}

/// 智能策略切换器
/// 
/// 注意：当前实现为基础架构，智能切换功能暂时注释
pub struct IntelligentStrategySwitcher {
    /// 当前活跃策略
    current_strategy: Option<StrategyType>,
    /// 策略适应性评估器
    fitness_evaluator: StrategyFitnessEvaluator,
    /// 切换历史记录
    switch_history: VecDeque<StrategySwitchRecord>,
    /// 切换配置
    switch_config: SwitchConfig,
    /// 最大历史记录长度
    max_history_length: usize,
}

impl IntelligentStrategySwitcher {
    /// 创建新的智能策略切换器
    pub fn new(config: SwitchConfig) -> Self {
        Self {
            current_strategy: None,
            fitness_evaluator: StrategyFitnessEvaluator::new(),
            switch_history: VecDeque::new(),
            switch_config: config,
            max_history_length: 100,
        }
    }
    
    /// 使用默认配置创建
    pub fn with_default_config() -> Self {
        Self::new(SwitchConfig::default())
    }
    
    /// 获取当前策略
    pub fn get_current_strategy(&self) -> Option<StrategyType> {
        self.current_strategy
    }
    
    /// 设置当前策略
    pub fn set_current_strategy(&mut self, strategy: Option<StrategyType>) {
        self.current_strategy = strategy;
    }

    /// 分析是否需要切换策略
    ///
    /// 注意：当前实现为基础架构，智能切换逻辑暂时注释
    /// 始终返回保持当前策略的决策，确保单策略模式的稳定运行
    pub async fn analyze_switch_decision(
        &mut self,
        _market_signal: &MarketSignal,
        _market_state: &MarketState,
    ) -> SigmaXResult<SwitchDecision> {

        debug!("分析策略切换决策，当前策略: {:?}", self.current_strategy);

        // 🔒 智能切换功能暂时注释，专注于单策略模式
        //
        // 未来启用智能切换时，可以取消注释以下代码：
        /*
        // 1. 评估所有策略的适应性
        let mut strategy_fitness = Vec::new();
        for strategy_type in StrategyType::all() {
            let fitness = self.fitness_evaluator
                .evaluate_strategy_fitness(strategy_type, market_signal, market_state).await?;
            strategy_fitness.push(fitness);
        }

        // 2. 选择最佳策略
        strategy_fitness.sort_by(|a, b| b.fitness_score.partial_cmp(&a.fitness_score).unwrap());
        let best_strategy = &strategy_fitness[0];

        // 3. 决定是否切换
        let decision = self.make_switch_decision(best_strategy, market_signal).await?;

        // 4. 记录决策
        self.record_decision(&decision, market_state).await?;

        return Ok(decision);
        */

        // 当前实现：始终保持现有策略或建议启动默认策略
        let decision = if self.current_strategy.is_none() {
            // 如果没有运行策略，建议启动默认策略
            SwitchDecision {
                action: SwitchAction::Switch(StrategyType::AsymmetricVolatilityGrid),
                reason: "启动默认网格策略".to_string(),
                confidence: 0.8,
                recommended_strategy: Some(StrategyType::AsymmetricVolatilityGrid),
                risk_factors: vec![],
                timestamp: Utc::now(),
            }
        } else {
            // 如果已有策略运行，保持当前策略
            SwitchDecision {
                action: SwitchAction::Stay,
                reason: "单策略模式，保持当前策略运行".to_string(),
                confidence: 0.9,
                recommended_strategy: self.current_strategy,
                risk_factors: vec![],
                timestamp: Utc::now(),
            }
        };

        debug!("策略切换决策: {:?}", decision);
        Ok(decision)
    }

    /// 记录策略切换
    pub async fn record_strategy_switch(
        &mut self,
        from_strategy: Option<StrategyType>,
        to_strategy: Option<StrategyType>,
        reason: String,
        market_state: &MarketState,
    ) -> SigmaXResult<()> {

        let record = StrategySwitchRecord {
            from_strategy,
            to_strategy,
            switch_time: Utc::now(),
            reason: reason.clone(),
            market_state: market_state.clone(),
            performance_before: None, // 简化实现
            performance_after: None,  // 简化实现
        };

        self.switch_history.push_back(record.clone());

        // 限制历史记录长度
        while self.switch_history.len() > self.max_history_length {
            self.switch_history.pop_front();
        }

        info!("记录策略切换: {:?} -> {:?}, 原因: {}",
              record.from_strategy, record.to_strategy, record.reason);

        Ok(())
    }

    /// 获取切换历史
    pub fn get_switch_history(&self) -> &VecDeque<StrategySwitchRecord> {
        &self.switch_history
    }

    /// 检查是否允许切换（频率限制）
    pub fn is_switch_allowed(&self) -> bool {
        let now = Utc::now();
        let one_hour_ago = now - Duration::hours(1);

        let recent_switches = self.switch_history.iter()
            .filter(|record| record.switch_time > one_hour_ago)
            .count();

        recent_switches < self.switch_config.max_switches_per_hour as usize
    }

    /// 获取最后一次切换时间
    pub fn get_last_switch_time(&self) -> Option<DateTime<Utc>> {
        self.switch_history.back().map(|record| record.switch_time)
    }

    // ==================== 未来智能切换功能（暂时注释） ====================

    /*
    /// 做出切换决策（智能切换功能，暂时注释）
    async fn make_switch_decision(
        &self,
        best_strategy: &StrategyFitness,
        market_signal: &MarketSignal,
    ) -> SigmaXResult<SwitchDecision> {

        // 紧急情况：立即停止
        if best_strategy.fitness_score < self.switch_config.emergency_threshold {
            return Ok(SwitchDecision {
                action: SwitchAction::EmergencyStop,
                reason: format!("市场条件恶劣，适应性仅 {:.2}", best_strategy.fitness_score),
                confidence: 0.95,
                recommended_strategy: None,
                risk_factors: vec!["市场条件恶劣".to_string()],
                timestamp: Utc::now(),
            });
        }

        // 当前没有策略运行
        if self.current_strategy.is_none() {
            if best_strategy.fitness_score > self.switch_config.start_threshold {
                return Ok(SwitchDecision {
                    action: SwitchAction::Switch(best_strategy.strategy_type),
                    reason: format!("启动 {:?} 策略，适应性 {:.2}",
                                  best_strategy.strategy_type, best_strategy.fitness_score),
                    confidence: best_strategy.confidence,
                    recommended_strategy: Some(best_strategy.strategy_type),
                    risk_factors: vec![],
                    timestamp: Utc::now(),
                });
            } else {
                return Ok(SwitchDecision {
                    action: SwitchAction::Pause,
                    reason: "当前市场条件不适合任何策略".to_string(),
                    confidence: 0.8,
                    recommended_strategy: None,
                    risk_factors: vec!["市场条件不佳".to_string()],
                    timestamp: Utc::now(),
                });
            }
        }

        // 当前有策略运行，检查是否需要切换
        let current_strategy = self.current_strategy.unwrap();

        // 如果最佳策略就是当前策略，继续运行
        if best_strategy.strategy_type == current_strategy {
            return Ok(SwitchDecision {
                action: SwitchAction::Stay,
                reason: format!("当前策略 {:?} 仍然最适合", current_strategy),
                confidence: best_strategy.confidence,
                recommended_strategy: Some(current_strategy),
                risk_factors: vec![],
                timestamp: Utc::now(),
            });
        }

        // 检查切换阈值和频率限制
        let mut risk_factors = Vec::new();

        if !self.is_switch_allowed() {
            risk_factors.push("切换频率过高".to_string());
        }

        if let Some(last_switch) = self.get_last_switch_time() {
            let time_since_last = Utc::now() - last_switch;
            if time_since_last < self.switch_config.min_switch_interval {
                risk_factors.push("距离上次切换时间过短".to_string());
            }
        }

        // 如果有风险因素，暂时不切换
        if !risk_factors.is_empty() {
            return Ok(SwitchDecision {
                action: SwitchAction::Stay,
                reason: "存在切换风险，保持当前策略".to_string(),
                confidence: 0.7,
                recommended_strategy: Some(current_strategy),
                risk_factors,
                timestamp: Utc::now(),
            });
        }

        // 检查性能改进是否足够
        let current_fitness = self.get_current_strategy_fitness();
        let improvement = best_strategy.fitness_score - current_fitness;

        if improvement > self.switch_config.switch_threshold {
            Ok(SwitchDecision {
                action: SwitchAction::Switch(best_strategy.strategy_type),
                reason: format!("切换到 {:?}，性能提升 {:.2}",
                              best_strategy.strategy_type, improvement),
                confidence: best_strategy.confidence,
                recommended_strategy: Some(best_strategy.strategy_type),
                risk_factors: vec![],
                timestamp: Utc::now(),
            })
        } else {
            Ok(SwitchDecision {
                action: SwitchAction::Stay,
                reason: "性能提升不足以支持策略切换".to_string(),
                confidence: 0.7,
                recommended_strategy: Some(current_strategy),
                risk_factors: vec!["性能提升不足".to_string()],
                timestamp: Utc::now(),
            })
        }
    }

    /// 获取当前策略的适应性评分
    fn get_current_strategy_fitness(&self) -> f64 {
        // 简化实现：返回默认值
        // 实际实现中应该基于历史性能数据计算
        0.6
    }

    /// 记录决策
    async fn record_decision(
        &mut self,
        decision: &SwitchDecision,
        market_state: &MarketState,
    ) -> SigmaXResult<()> {

        // 如果决策是切换策略，记录到历史中
        if let SwitchAction::Switch(new_strategy) = &decision.action {
            self.record_strategy_switch(
                self.current_strategy,
                Some(*new_strategy),
                decision.reason.clone(),
                market_state,
            ).await?;
        }

        Ok(())
    }
    */
}
