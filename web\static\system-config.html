<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SigmaX 系统配置管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/custom.css">
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <button onclick="goBack()" class="mr-4 p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <h1 class="text-xl font-semibold text-gray-900">系统配置管理</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="relative">
                        <button id="template-btn" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors">
                            <i class="fas fa-file-alt mr-2"></i>模板
                        </button>
                        <div id="template-dropdown" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border z-10">
                            <div class="py-1">
                                <button onclick="applyTemplate('development')" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-code mr-2"></i>开发环境模板
                                </button>
                                <button onclick="applyTemplate('production')" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-server mr-2"></i>生产环境模板
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="relative">
                        <button id="import-export-btn" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                            <i class="fas fa-exchange-alt mr-2"></i>导入/导出
                        </button>
                        <div id="import-export-dropdown" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border z-10">
                            <div class="py-1">
                                <button onclick="exportConfigs()" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-download mr-2"></i>导出配置
                                </button>
                                <label class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer">
                                    <i class="fas fa-upload mr-2"></i>导入配置
                                    <input type="file" id="import-file" accept=".json" class="hidden">
                                </label>
                            </div>
                        </div>
                    </div>
                    <button id="save-all-btn" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-save mr-2"></i>保存所有配置
                    </button>
                    <button id="reset-all-btn" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                        <i class="fas fa-undo mr-2"></i>重置所有
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 配置导航标签 -->
        <div class="bg-white rounded-lg shadow-sm mb-6">
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8 px-6">
                    <button class="config-tab active py-4 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600" data-tab="system">
                        <i class="fas fa-cog mr-2"></i>系统配置
                    </button>
                    <button class="config-tab py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="api">
                        <i class="fas fa-plug mr-2"></i>API配置
                    </button>
                    <button class="config-tab py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="cache">
                        <i class="fas fa-memory mr-2"></i>缓存配置
                    </button>
                    <button class="config-tab py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="monitoring">
                        <i class="fas fa-chart-line mr-2"></i>监控配置
                    </button>
                </nav>
            </div>
        </div>

        <!-- 配置内容区域 -->
        <div class="bg-white rounded-lg shadow-sm">
            <!-- 系统配置面板 -->
            <div id="system-config-panel" class="config-panel p-6">
                <div class="flex items-center justify-between mb-6">
                    <div>
                        <h2 class="text-lg font-semibold text-gray-900">系统配置</h2>
                        <p class="text-sm text-gray-600">管理系统核心配置信息</p>
                    </div>
                    <div class="flex space-x-2">
                        <button id="system-save-btn" class="bg-blue-600 text-white px-3 py-1.5 rounded text-sm hover:bg-blue-700">
                            <i class="fas fa-save mr-1"></i>保存
                        </button>
                        <button id="system-reset-btn" class="bg-gray-600 text-white px-3 py-1.5 rounded text-sm hover:bg-gray-700">
                            <i class="fas fa-undo mr-1"></i>重置
                        </button>
                    </div>
                </div>
                
                <form id="system-config-form" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">维护模式</label>
                            <select id="maintenance-mode" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="false">关闭</option>
                                <option value="true">开启</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">最大并发策略数</label>
                            <input type="number" id="max-concurrent-strategies" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="50" min="1" max="1000">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">数据保留天数</label>
                            <input type="number" id="data-retention-days" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="365" min="1" max="3650">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">备份开关</label>
                            <select id="backup-enabled" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="false">关闭</option>
                                <option value="true">开启</option>
                            </select>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">日志级别</label>
                        <select id="log-level" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="ERROR">ERROR</option>
                            <option value="WARN">WARN</option>
                            <option value="INFO">INFO</option>
                            <option value="DEBUG">DEBUG</option>
                            <option value="TRACE">TRACE</option>
                        </select>
                    </div>
                </form>
            </div>

            <!-- API配置面板 -->
            <div id="api-config-panel" class="config-panel hidden p-6">
                <div class="flex items-center justify-between mb-6">
                    <div>
                        <h2 class="text-lg font-semibold text-gray-900">API配置</h2>
                        <p class="text-sm text-gray-600">管理API服务相关配置</p>
                    </div>
                    <div class="flex space-x-2">
                        <button id="api-save-btn" class="bg-blue-600 text-white px-3 py-1.5 rounded text-sm hover:bg-blue-700">
                            <i class="fas fa-save mr-1"></i>保存
                        </button>
                        <button id="api-reset-btn" class="bg-gray-600 text-white px-3 py-1.5 rounded text-sm hover:bg-gray-700">
                            <i class="fas fa-undo mr-1"></i>重置
                        </button>
                    </div>
                </div>
                
                <form id="api-config-form" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">每分钟请求限制</label>
                            <input type="number" id="rate-limit-per-minute" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="1000" min="1" max="100000">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">最大请求大小 (字节)</label>
                            <input type="number" id="max-request-size" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="1048576" min="1024">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">请求超时 (秒)</label>
                            <input type="number" id="timeout-seconds" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="30" min="1" max="300">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">启用CORS</label>
                            <select id="cors-enabled" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="true">启用</option>
                                <option value="false">禁用</option>
                            </select>
                        </div>
                    </div>
                </form>
            </div>

            <!-- 缓存配置面板 -->
            <div id="cache-config-panel" class="config-panel hidden p-6">
                <div class="flex items-center justify-between mb-6">
                    <div>
                        <h2 class="text-lg font-semibold text-gray-900">缓存配置</h2>
                        <p class="text-sm text-gray-600">管理系统缓存相关配置</p>
                    </div>
                    <div class="flex space-x-2">
                        <button id="cache-save-btn" class="bg-blue-600 text-white px-3 py-1.5 rounded text-sm hover:bg-blue-700">
                            <i class="fas fa-save mr-1"></i>保存
                        </button>
                        <button id="cache-reset-btn" class="bg-gray-600 text-white px-3 py-1.5 rounded text-sm hover:bg-gray-700">
                            <i class="fas fa-undo mr-1"></i>重置
                        </button>
                    </div>
                </div>
                
                <form id="cache-config-form" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">配置名称</label>
                            <input type="text" id="cache-name" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="default">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">启用缓存</label>
                            <select id="cache-enabled" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="true">启用</option>
                                <option value="false">禁用</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">启用Redis</label>
                            <select id="redis-enabled" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="true">启用</option>
                                <option value="false">禁用</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">默认TTL (秒)</label>
                            <input type="number" id="default-ttl-seconds" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="3600" min="1">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">最大内存 (MB)</label>
                            <input type="number" id="max-memory-mb" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="512" min="1">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">最大条目数</label>
                            <input type="number" id="max-entries" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="100000" min="1">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">清理间隔 (秒)</label>
                            <input type="number" id="cleanup-interval-seconds" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="300" min="1">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">内存阈值 (%)</label>
                            <input type="number" id="memory-threshold-percentage" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="80" min="1" max="100">
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">配置描述</label>
                        <textarea id="cache-description" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="缓存配置描述..."></textarea>
                    </div>
                </form>
            </div>

            <!-- 监控配置面板 -->
            <div id="monitoring-config-panel" class="config-panel hidden p-6">
                <div class="flex items-center justify-between mb-6">
                    <div>
                        <h2 class="text-lg font-semibold text-gray-900">监控配置</h2>
                        <p class="text-sm text-gray-600">管理系统监控相关配置</p>
                    </div>
                    <div class="flex space-x-2">
                        <button id="monitoring-save-btn" class="bg-blue-600 text-white px-3 py-1.5 rounded text-sm hover:bg-blue-700">
                            <i class="fas fa-save mr-1"></i>保存
                        </button>
                        <button id="monitoring-reset-btn" class="bg-gray-600 text-white px-3 py-1.5 rounded text-sm hover:bg-gray-700">
                            <i class="fas fa-undo mr-1"></i>重置
                        </button>
                    </div>
                </div>
                
                <form id="monitoring-config-form" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">配置名称</label>
                            <input type="text" id="monitoring-name" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="default">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">启用监控</label>
                            <select id="monitoring-enabled" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="true">启用</option>
                                <option value="false">禁用</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">启用指标收集</label>
                            <select id="metrics-enabled" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="true">启用</option>
                                <option value="false">禁用</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">健康检查间隔 (秒)</label>
                            <input type="number" id="health-check-interval" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="60" min="1">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">数据收集间隔 (秒)</label>
                            <input type="number" id="data-collection-interval" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="30" min="1">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">数据保留天数</label>
                            <input type="number" id="data-retention-days" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="30" min="1">
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">配置描述</label>
                        <textarea id="monitoring-description" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="监控配置描述..."></textarea>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 加载中遮罩 -->
    <div id="loading-overlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span class="text-gray-700">正在处理...</span>
        </div>
    </div>

    <!-- 引入依赖脚本 -->
    <script src="js/config.js"></script>
    <script src="js/api-config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/config-utils.js"></script>
    <script src="js/services/system-config-service.js"></script>
    <script src="js/system-config-manager.js"></script>

    <script>
        // 下拉菜单控制
        document.addEventListener('DOMContentLoaded', () => {
            // 模板下拉菜单
            const templateBtn = document.getElementById('template-btn');
            const templateDropdown = document.getElementById('template-dropdown');

            templateBtn?.addEventListener('click', (e) => {
                e.stopPropagation();
                templateDropdown.classList.toggle('hidden');
                document.getElementById('import-export-dropdown')?.classList.add('hidden');
            });

            // 导入/导出下拉菜单
            const importExportBtn = document.getElementById('import-export-btn');
            const importExportDropdown = document.getElementById('import-export-dropdown');

            importExportBtn?.addEventListener('click', (e) => {
                e.stopPropagation();
                importExportDropdown.classList.toggle('hidden');
                templateDropdown?.classList.add('hidden');
            });

            // 点击其他地方关闭下拉菜单
            document.addEventListener('click', () => {
                templateDropdown?.classList.add('hidden');
                importExportDropdown?.classList.add('hidden');
            });

            // 文件导入处理
            const importFile = document.getElementById('import-file');
            importFile?.addEventListener('change', handleFileImport);
        });

        // 应用模板
        async function applyTemplate(templateType) {
            try {
                if (window.configManager && window.configManager.isDirty) {
                    if (!confirm('应用模板将覆盖当前未保存的更改，确定继续吗？')) {
                        return;
                    }
                }

                const template = window.ConfigUtils.applyTemplate(templateType);

                if (window.configManager) {
                    window.configManager.configs = template;
                    window.configManager.populateAllForms();
                    window.configManager.markDirty();
                }

                window.Utils?.showNotification(`已应用${window.ConfigUtils.getTemplateName(templateType)}模板`, 'success');

            } catch (error) {
                console.error('❌ 应用模板失败:', error);
                window.Utils?.showNotification('应用模板失败: ' + error.message, 'error');
            }
        }

        // 导出配置
        async function exportConfigs() {
            try {
                if (!window.configManager) {
                    throw new Error('配置管理器未初始化');
                }

                // 收集当前表单数据
                const currentConfigs = {};
                Object.keys(window.SystemConfigService.configTypes).forEach(configType => {
                    currentConfigs[configType] = window.configManager.collectFormData(configType);
                });

                window.ConfigUtils.exportConfigs(currentConfigs);
                window.Utils?.showNotification('配置导出成功', 'success');

            } catch (error) {
                console.error('❌ 导出配置失败:', error);
                window.Utils?.showNotification('导出配置失败: ' + error.message, 'error');
            }
        }

        // 处理文件导入
        async function handleFileImport(event) {
            try {
                const file = event.target.files[0];
                if (!file) return;

                if (window.configManager && window.configManager.isDirty) {
                    if (!confirm('导入配置将覆盖当前未保存的更改，确定继续吗？')) {
                        event.target.value = ''; // 清除文件选择
                        return;
                    }
                }

                const importedConfigs = await window.ConfigUtils.importConfigs(file);

                if (window.configManager) {
                    window.configManager.configs = importedConfigs;
                    window.configManager.populateAllForms();
                    window.configManager.markDirty();
                }

                window.Utils?.showNotification('配置导入成功', 'success');

            } catch (error) {
                console.error('❌ 导入配置失败:', error);
                window.Utils?.showNotification('导入配置失败: ' + error.message, 'error');
            } finally {
                event.target.value = ''; // 清除文件选择，允许重复导入同一文件
            }
        }
    </script>
