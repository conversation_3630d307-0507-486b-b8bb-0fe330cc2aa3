# SigmaX 测试脚本

这个目录包含了SigmaX项目的各种测试和演示脚本。

## 📋 脚本列表

### 1. `test_complete_backtest_flow.sh` - 完整API流程测试

**功能**: 测试SigmaX的完整API功能，包括回测引擎、策略管理、策略执行引擎和风控管理。

**初始资金配置**:
- BNB: 2
- USDT: 100
- 总价值: 约700 USDT

**测试覆盖**:
- ✅ 回测引擎管理 (13个API)
- ✅ 策略管理 (6个API) 
- ✅ 策略执行引擎 (6个API)
- ✅ 风控管理 (4个API)
- ✅ 总计: 29个API端点

**使用方法**:
```bash
# 1. 启动SigmaX服务器
cargo run --bin web_server

# 2. 在新终端运行测试
./scripts/test_complete_backtest_flow.sh
```

**测试流程**:
1. 健康检查
2. 获取和验证回测数据文件
3. 创建回测引擎（使用指定初始资金）
4. 设置网格策略配置
5. 启动回测并监控进度
6. 获取回测结果和交易记录
7. 测试策略管理功能
8. 测试策略执行引擎
9. 测试风控管理功能
10. 清理资源

### 2. `run_backtest_demo.sh` - 快速回测演示

**功能**: 快速演示回测功能的核心流程。

**特点**:
- 🚀 快速执行（约30秒）
- 💰 使用指定初始资金（2 BNB + 100 USDT）
- 📊 网格交易策略演示
- 📈 实时结果展示

**使用方法**:
```bash
# 1. 启动SigmaX服务器
cargo run --bin web_server

# 2. 运行快速演示
./scripts/run_backtest_demo.sh
```

## 🔧 依赖要求

### 系统依赖
- `curl` - HTTP请求工具
- `bc` - 数学计算工具（用于资金计算）

### 安装依赖

**macOS**:
```bash
brew install curl bc
```

**Ubuntu/Debian**:
```bash
sudo apt-get install curl bc
```

**CentOS/RHEL**:
```bash
sudo yum install curl bc
```

## 📊 测试配置

### 初始资金配置
```bash
INIT_BNB=2      # 初始BNB数量
INIT_USDT=100   # 初始USDT数量
```

### 策略配置
- **策略类型**: 网格交易
- **网格数量**: 12-15个
- **价格范围**: 6-8%
- **网格间距**: 0.5%
- **订单大小**: 基于初始资金的10%

### 风控配置
- **最大持仓**: 总资金的70-80%
- **最大订单**: 总资金的20%
- **止损**: 4-5%
- **止盈**: 12-15%
- **最大日损失**: 总资金的5-10%

## 🚀 运行示例

### 完整测试流程
```bash
# 启动服务器
cargo run --bin web_server &

# 等待服务器启动
sleep 5

# 运行完整测试
./scripts/test_complete_backtest_flow.sh

# 查看测试结果
echo "测试完成，查看上方输出的详细结果"
```

### 快速演示
```bash
# 启动服务器
cargo run --bin web_server &

# 运行快速演示
./scripts/run_backtest_demo.sh
```

## 📈 预期结果

### 成功输出示例
```
🎉 完整API流程测试完成!

📊 测试总结:
- 使用数据文件: BNB_USDT_1d.json
- 初始资金配置: 2 BNB + 100 USDT
- 引擎ID: 550e8400-e29b-41d4-a716-446655440000
- 策略ID: 6ba7b810-9dad-11d1-80b4-00c04fd430c8
- 风控规则ID: 6ba7b811-9dad-11d1-80b4-00c04fd430c8

🔧 测试覆盖范围:
✅ 回测引擎管理 (13个API)
✅ 策略管理 (6个API)
✅ 策略执行引擎 (6个API)
✅ 风控管理 (4个API)
✅ 总计: 29个API端点

💰 资金配置测试:
✅ 初始BNB余额: 2
✅ 初始USDT余额: 100
✅ 网格策略配置: 12网格，6%价格范围
✅ 风控配置: 基于初始资金的动态限制
```

## 🐛 故障排除

### 常见问题

1. **服务器未运行**
   ```
   [ERROR] 服务器未运行，请先启动: cargo run --bin web_server
   ```
   **解决**: 先启动SigmaX服务器

2. **依赖工具缺失**
   ```
   [ERROR] bc 未安装，请先安装 bc
   ```
   **解决**: 安装bc工具（见上方依赖安装）

3. **数据文件不存在**
   ```
   [WARNING] 未找到指定数据文件，使用可用的第一个文件
   ```
   **解决**: 确保bt_klines目录中有测试数据文件

4. **API调用失败**
   ```
   [WARNING] 策略创建失败
   ```
   **解决**: 检查服务器日志，确认数据库连接正常

### 调试模式

启用详细日志输出:
```bash
RUST_LOG=debug cargo run --bin web_server
```

## 💡 扩展使用

### 自定义初始资金
```bash
# 修改脚本中的配置
INIT_BNB=5      # 改为5个BNB
INIT_USDT=500   # 改为500 USDT
```

### 自定义策略参数
修改脚本中的策略配置部分:
```json
{
  "grid_count": 20,        // 网格数量
  "price_range": 0.1,      // 价格范围10%
  "grid_spacing": 0.005    // 网格间距0.5%
}
```

### 添加新的测试用例
1. 复制现有的测试函数
2. 修改测试参数
3. 在主函数中调用新的测试函数

## 📚 相关文档

- [策略执行引擎文档](../docs/strategy_execution_engine.md)
- [API文档](../docs/api/)
- [回测引擎文档](../docs/backtest_engine.md)
- [风控管理文档](../docs/risk_management.md)
