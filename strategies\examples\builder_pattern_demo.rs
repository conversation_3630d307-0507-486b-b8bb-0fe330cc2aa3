//! Builder模式演示
//!
//! 展示如何使用类型安全的Builder模式创建策略
//!
//! 注意：这是一个简化的演示，实际使用时需要提供真实的服务实现

use sigmax_strategies::{
    StrategyType, AsymmetricGridConfig,
    core::StrategyConfig,
};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt::init();

    println!("🎯 Builder模式演示");
    println!("==================");

    // 创建策略配置
    let config = AsymmetricGridConfig::balanced();

    println!("\n📋 配置信息:");
    println!("   基准价格: {}", config.base_price);
    println!("   订单金额: {}", config.order_amount);
    println!("   交易对: {}", config.trading_pair);
    println!("   交易所: {}", config.exchange_id);

    // 演示策略类型系统
    println!("\n🎯 策略类型系统演示");
    println!("   支持的策略类型: {:?}", StrategyType::all());

    for strategy_type in StrategyType::all() {
        println!("   - {}: {}", strategy_type.as_str(), strategy_type.display_name());
        println!("     描述: {}", strategy_type.description());
    }

    // 演示配置验证
    println!("\n🔍 配置验证演示");
    match config.validate() {
        Ok(()) => println!("✅ 配置验证通过"),
        Err(e) => println!("❌ 配置验证失败: {}", e),
    }

    // 演示配置序列化
    println!("\n📄 配置序列化演示");
    let json_config = serde_json::to_string_pretty(&config)?;
    println!("配置JSON:\n{}", json_config);

    println!("\n🎉 演示完成!");
    println!("   策略配置系统展示了类型安全和易用性");
    println!("   要创建实际策略实例，需要提供真实的服务容器");

    Ok(())
}