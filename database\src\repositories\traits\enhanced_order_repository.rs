//! 增强的订单仓储接口
//!
//! 提供完整的订单数据访问功能，包括基础CRUD、业务查询和聚合操作

use async_trait::async_trait;
use chrono::{DateTime, Utc};
use sigmax_core::{
    Order, OrderId, OrderStatus, StrategyId, TradingPair, ExchangeId, 
    OrderSide, OrderType, Quantity, Price, Amount, SigmaXResult
};

/// 订单查询条件
#[derive(Debug, Clone)]
pub struct OrderQueryFilter {
    pub strategy_id: Option<StrategyId>,
    pub exchange_id: Option<ExchangeId>,
    pub trading_pair: Option<TradingPair>,
    pub status: Option<OrderStatus>,
    pub side: Option<OrderSide>,
    pub order_type: Option<OrderType>,
    pub created_after: Option<DateTime<Utc>>,
    pub created_before: Option<DateTime<Utc>>,
    pub min_quantity: Option<Quantity>,
    pub max_quantity: Option<Quantity>,
    pub min_price: Option<Price>,
    pub max_price: Option<Price>,
}

impl Default for OrderQueryFilter {
    fn default() -> Self {
        Self {
            strategy_id: None,
            exchange_id: None,
            trading_pair: None,
            status: None,
            side: None,
            order_type: None,
            created_after: None,
            created_before: None,
            min_quantity: None,
            max_quantity: None,
            min_price: None,
            max_price: None,
        }
    }
}

/// 分页参数
#[derive(Debug, Clone)]
pub struct Pagination {
    pub offset: usize,
    pub limit: usize,
}

impl Default for Pagination {
    fn default() -> Self {
        Self {
            offset: 0,
            limit: 100,
        }
    }
}

/// 排序参数
#[derive(Debug, Clone)]
pub enum OrderSortBy {
    CreatedAt,
    UpdatedAt,
    Quantity,
    Price,
    Status,
}

#[derive(Debug, Clone)]
pub enum SortDirection {
    Asc,
    Desc,
}

#[derive(Debug, Clone)]
pub struct OrderSort {
    pub sort_by: OrderSortBy,
    pub direction: SortDirection,
}

impl Default for OrderSort {
    fn default() -> Self {
        Self {
            sort_by: OrderSortBy::CreatedAt,
            direction: SortDirection::Desc,
        }
    }
}

/// 订单统计信息
#[derive(Debug, Clone)]
pub struct OrderStatistics {
    pub total_count: u64,
    pub pending_count: u64,
    pub filled_count: u64,
    pub cancelled_count: u64,
    pub total_volume: Quantity,
    pub total_value: Amount,
    pub average_order_size: Quantity,
}

/// 增强的订单仓储接口
#[async_trait]
pub trait EnhancedOrderRepository: Send + Sync {
    // ============================================================================
    // 基础 CRUD 操作
    // ============================================================================
    
    /// 保存订单（插入或更新）
    async fn save(&self, order: &Order) -> SigmaXResult<()>;
    
    /// 根据ID查找订单
    async fn find_by_id(&self, id: OrderId) -> SigmaXResult<Option<Order>>;
    
    /// 更新订单
    async fn update(&self, order: &Order) -> SigmaXResult<()>;
    
    /// 删除订单
    async fn delete(&self, id: OrderId) -> SigmaXResult<()>;
    
    /// 检查订单是否存在
    async fn exists(&self, id: OrderId) -> SigmaXResult<bool>;

    // ============================================================================
    // 批量操作
    // ============================================================================
    
    /// 批量保存订单
    async fn save_batch(&self, orders: &[Order]) -> SigmaXResult<()>;
    
    /// 批量更新订单状态
    async fn update_status_batch(&self, ids: &[OrderId], status: OrderStatus) -> SigmaXResult<()>;
    
    /// 批量删除订单
    async fn delete_batch(&self, ids: &[OrderId]) -> SigmaXResult<()>;

    // ============================================================================
    // 条件查询
    // ============================================================================
    
    /// 根据条件查询订单
    async fn find_by_filter(
        &self, 
        filter: &OrderQueryFilter,
        pagination: Option<&Pagination>,
        sort: Option<&OrderSort>
    ) -> SigmaXResult<Vec<Order>>;
    
    /// 根据状态查找订单
    async fn find_by_status(&self, status: OrderStatus) -> SigmaXResult<Vec<Order>>;
    
    /// 根据策略查找订单
    async fn find_by_strategy(&self, strategy_id: StrategyId) -> SigmaXResult<Vec<Order>>;
    
    /// 根据交易对查找订单
    async fn find_by_trading_pair(&self, trading_pair: &TradingPair) -> SigmaXResult<Vec<Order>>;
    
    /// 根据交易所查找订单
    async fn find_by_exchange(&self, exchange_id: &ExchangeId) -> SigmaXResult<Vec<Order>>;
    
    /// 查找待处理订单
    async fn find_pending_orders(&self) -> SigmaXResult<Vec<Order>>;
    
    /// 查找活跃订单（待处理 + 部分成交）
    async fn find_active_orders(&self) -> SigmaXResult<Vec<Order>>;
    
    /// 根据时间范围查找订单
    async fn find_by_timerange(
        &self, 
        start: DateTime<Utc>, 
        end: DateTime<Utc>
    ) -> SigmaXResult<Vec<Order>>;

    // ============================================================================
    // 聚合查询
    // ============================================================================
    
    /// 统计订单数量
    async fn count_by_status(&self, status: OrderStatus) -> SigmaXResult<u64>;
    
    /// 统计策略的订单数量
    async fn count_by_strategy(&self, strategy_id: StrategyId) -> SigmaXResult<u64>;
    
    /// 计算策略的总交易量
    async fn get_total_volume_by_strategy(&self, strategy_id: StrategyId) -> SigmaXResult<Quantity>;
    
    /// 计算策略的总交易价值
    async fn get_total_value_by_strategy(&self, strategy_id: StrategyId) -> SigmaXResult<Amount>;
    
    /// 获取订单统计信息
    async fn get_statistics(&self, filter: Option<&OrderQueryFilter>) -> SigmaXResult<OrderStatistics>;
    
    /// 获取策略的订单统计
    async fn get_strategy_statistics(&self, strategy_id: StrategyId) -> SigmaXResult<OrderStatistics>;

    // ============================================================================
    // 业务查询
    // ============================================================================
    
    /// 查找需要风险检查的订单
    async fn find_orders_for_risk_check(&self) -> SigmaXResult<Vec<Order>>;
    
    /// 查找超时的待处理订单
    async fn find_timeout_pending_orders(&self, timeout_minutes: u32) -> SigmaXResult<Vec<Order>>;
    
    /// 查找大额订单（超过指定金额）
    async fn find_large_orders(&self, min_value: Amount) -> SigmaXResult<Vec<Order>>;
    
    /// 查找异常订单（价格偏离市场价格过多）
    async fn find_abnormal_orders(
        &self, 
        trading_pair: &TradingPair, 
        market_price: Price, 
        deviation_percent: f64
    ) -> SigmaXResult<Vec<Order>>;

    // ============================================================================
    // 性能优化查询
    // ============================================================================
    
    /// 获取最近的订单（用于缓存）
    async fn get_recent_orders(&self, limit: usize) -> SigmaXResult<Vec<Order>>;
    
    /// 获取热门交易对的订单
    async fn get_orders_by_popular_pairs(&self, limit: usize) -> SigmaXResult<Vec<Order>>;
    
    /// 预加载相关数据（用于减少N+1查询）
    async fn preload_orders_with_relations(&self, order_ids: &[OrderId]) -> SigmaXResult<Vec<Order>>;
}

/// 订单仓储构建器
pub struct OrderRepositoryBuilder {
    connection_string: Option<String>,
    pool_size: Option<u32>,
    timeout_seconds: Option<u64>,
    enable_cache: bool,
    cache_ttl_seconds: Option<u64>,
}

impl OrderRepositoryBuilder {
    pub fn new() -> Self {
        Self {
            connection_string: None,
            pool_size: None,
            timeout_seconds: None,
            enable_cache: false,
            cache_ttl_seconds: None,
        }
    }

    pub fn connection_string(mut self, connection_string: String) -> Self {
        self.connection_string = Some(connection_string);
        self
    }

    pub fn pool_size(mut self, pool_size: u32) -> Self {
        self.pool_size = Some(pool_size);
        self
    }

    pub fn timeout_seconds(mut self, timeout_seconds: u64) -> Self {
        self.timeout_seconds = Some(timeout_seconds);
        self
    }

    pub fn enable_cache(mut self, enable: bool) -> Self {
        self.enable_cache = enable;
        self
    }

    pub fn cache_ttl_seconds(mut self, ttl_seconds: u64) -> Self {
        self.cache_ttl_seconds = Some(ttl_seconds);
        self
    }

    pub async fn build_postgres(self) -> SigmaXResult<Box<dyn EnhancedOrderRepository>> {
        // 这里会实现PostgreSQL版本的Repository
        todo!("实现PostgreSQL订单仓储")
    }

    pub async fn build_mock(self) -> SigmaXResult<Box<dyn EnhancedOrderRepository>> {
        // 这里会实现Mock版本的Repository
        todo!("实现Mock订单仓储")
    }
}

impl Default for OrderRepositoryBuilder {
    fn default() -> Self {
        Self::new()
    }
}
