//! 风控配置转换器
//!
//! 负责将数据库中的风控规则转换为风控模块可用的配置

use sigmax_core::{RiskManagementConfig, SigmaXResult};
use sigmax_database::repositories::traits::RiskRuleRecord;
use serde::{Deserialize, Serialize};
use serde_json::Value;
use rust_decimal::Decimal;
use std::collections::HashMap;

// ============================================================================
// 配置转换器
// ============================================================================

/// 风控配置转换器
/// 
/// 负责：
/// 1. 将数据库中的 RiskRuleRecord 转换为具体的风控策略配置
/// 2. 合并多个规则为统一的配置
/// 3. 提供配置验证和默认值处理
pub struct RiskConfigConverter;

impl RiskConfigConverter {
    /// 将风控规则转换为配置
    pub fn rules_to_config(rules: &[RiskRuleRecord]) -> SigmaXResult<RiskManagementConfig> {
        let mut config = RiskManagementConfig::default();
        
        // 按规则类型分组处理
        let rules_by_type = Self::group_rules_by_type(rules);
        
        // 处理各种类型的规则
        for (rule_type, type_rules) in rules_by_type {
            match rule_type.as_str() {
                "max_order_size" => Self::apply_order_size_rules(&mut config, &type_rules)?,
                "position_limit" => Self::apply_position_limit_rules(&mut config, &type_rules)?,
                "daily_loss_limit" => Self::apply_daily_loss_rules(&mut config, &type_rules)?,
                "leverage_limit" => Self::apply_leverage_rules(&mut config, &type_rules)?,
                "volatility_control" => Self::apply_volatility_rules(&mut config, &type_rules)?,
                "time_restriction" => Self::apply_time_rules(&mut config, &type_rules)?,
                "emergency_stop" => Self::apply_emergency_rules(&mut config, &type_rules)?,
                _ => {
                    tracing::warn!("Unknown rule type: {}", rule_type);
                }
            }
        }
        
        // 验证配置
        config.validate_complete()?;
        
        Ok(config)
    }

    /// 按类型分组规则
    fn group_rules_by_type(rules: &[RiskRuleRecord]) -> HashMap<String, Vec<&RiskRuleRecord>> {
        let mut grouped = HashMap::new();
        
        for rule in rules {
            grouped
                .entry(rule.rule_type.clone())
                .or_insert_with(Vec::new)
                .push(rule);
        }
        
        grouped
    }

    /// 应用订单大小限制规则
    fn apply_order_size_rules(
        config: &mut RiskManagementConfig,
        rules: &[&RiskRuleRecord],
    ) -> SigmaXResult<()> {
        for rule in rules {
            let params = &rule.parameters;
            
            // 解析最大订单金额
            if let Some(max_amount) = params.get("max_amount") {
                if let Ok(amount) = serde_json::from_value::<f64>(max_amount.clone()) {
                    config.risk_parameters.basic.max_order_amount = Decimal::from_f64_retain(amount)
                        .unwrap_or(config.risk_parameters.basic.max_order_amount);
                }
            }
            
            // 解析货币类型特定限制
            if let Some(currency) = params.get("currency") {
                if let Ok(currency_str) = serde_json::from_value::<String>(currency.clone()) {
                    // 可以根据货币类型设置不同的限制
                    tracing::debug!("Applied order size rule for currency: {}", currency_str);
                }
            }
        }
        
        Ok(())
    }

    /// 应用持仓限制规则
    fn apply_position_limit_rules(
        config: &mut RiskManagementConfig,
        rules: &[&RiskRuleRecord],
    ) -> SigmaXResult<()> {
        for rule in rules {
            let params = &rule.parameters;
            
            // 解析最大持仓比例
            if let Some(max_position) = params.get("max_position_percent") {
                if let Ok(percent) = serde_json::from_value::<f64>(max_position.clone()) {
                    config.risk_parameters.position.max_position_per_symbol = 
                        Decimal::from_f64_retain(percent / 100.0)
                            .unwrap_or(config.risk_parameters.position.max_position_per_symbol);
                }
            }
            
            // 解析集中度限制
            if let Some(concentration) = params.get("concentration_limit") {
                if let Ok(limit) = serde_json::from_value::<f64>(concentration.clone()) {
                    config.risk_parameters.position.concentration_limit = 
                        Decimal::from_f64_retain(limit / 100.0)
                            .unwrap_or(config.risk_parameters.position.concentration_limit);
                }
            }
        }
        
        Ok(())
    }

    /// 应用日损失限制规则
    fn apply_daily_loss_rules(
        config: &mut RiskManagementConfig,
        rules: &[&RiskRuleRecord],
    ) -> SigmaXResult<()> {
        for rule in rules {
            let params = &rule.parameters;
            
            // 解析最大日损失百分比
            if let Some(max_loss) = params.get("max_daily_loss_percent") {
                if let Ok(percent) = serde_json::from_value::<f64>(max_loss.clone()) {
                    config.risk_parameters.basic.max_daily_loss_percent = 
                        Decimal::from_f64_retain(percent / 100.0)
                            .unwrap_or(config.risk_parameters.basic.max_daily_loss_percent);
                }
            }
            
            // 解析最大回撤
            if let Some(max_drawdown) = params.get("max_drawdown_percent") {
                if let Ok(percent) = serde_json::from_value::<f64>(max_drawdown.clone()) {
                    config.risk_parameters.basic.max_drawdown_percent = 
                        Decimal::from_f64_retain(percent / 100.0)
                            .unwrap_or(config.risk_parameters.basic.max_drawdown_percent);
                }
            }
        }
        
        Ok(())
    }

    /// 应用杠杆限制规则
    fn apply_leverage_rules(
        config: &mut RiskManagementConfig,
        rules: &[&RiskRuleRecord],
    ) -> SigmaXResult<()> {
        for rule in rules {
            let params = &rule.parameters;
            
            // 解析最大杠杆倍数
            if let Some(max_leverage) = params.get("max_leverage") {
                if let Ok(leverage) = serde_json::from_value::<f64>(max_leverage.clone()) {
                    config.risk_parameters.position.max_leverage = 
                        Decimal::from_f64_retain(leverage)
                            .unwrap_or(config.risk_parameters.position.max_leverage);
                }
            }
        }
        
        Ok(())
    }

    /// 应用波动率控制规则
    fn apply_volatility_rules(
        config: &mut RiskManagementConfig,
        rules: &[&RiskRuleRecord],
    ) -> SigmaXResult<()> {
        for rule in rules {
            let params = &rule.parameters;
            
            // 解析波动率阈值
            if let Some(threshold) = params.get("volatility_threshold") {
                if let Ok(vol) = serde_json::from_value::<f64>(threshold.clone()) {
                    config.risk_parameters.market.volatility_threshold = 
                        Decimal::from_f64_retain(vol)
                            .unwrap_or(config.risk_parameters.market.volatility_threshold);
                }
            }
        }
        
        Ok(())
    }

    /// 应用时间限制规则
    fn apply_time_rules(
        config: &mut RiskManagementConfig,
        rules: &[&RiskRuleRecord],
    ) -> SigmaXResult<()> {
        for rule in rules {
            let params = &rule.parameters;
            
            // 解析交易时间限制
            if let Some(trading_hours) = params.get("trading_hours") {
                // 这里可以解析具体的交易时间配置
                tracing::debug!("Applied time restriction rule: {:?}", trading_hours);
            }
            
            // 解析最大交易次数
            if let Some(max_trades) = params.get("max_daily_trades") {
                if let Ok(trades) = serde_json::from_value::<u32>(max_trades.clone()) {
                    config.risk_parameters.trading.max_daily_trades = trades;
                }
            }
        }
        
        Ok(())
    }

    /// 应用紧急停止规则
    fn apply_emergency_rules(
        config: &mut RiskManagementConfig,
        rules: &[&RiskRuleRecord],
    ) -> SigmaXResult<()> {
        for rule in rules {
            let params = &rule.parameters;
            
            // 解析紧急停止阈值
            if let Some(threshold) = params.get("emergency_stop_threshold") {
                if let Ok(thresh) = serde_json::from_value::<f64>(threshold.clone()) {
                    config.risk_parameters.advanced.emergency_measures.emergency_stop_threshold = 
                        Decimal::from_f64_retain(thresh / 100.0)
                            .unwrap_or(config.risk_parameters.advanced.emergency_measures.emergency_stop_threshold);
                }
            }
        }
        
        Ok(())
    }

    /// 创建规则特定的配置
    pub fn create_rule_config(rule: &RiskRuleRecord) -> SigmaXResult<RuleConfig> {
        let config = match rule.rule_type.as_str() {
            "max_order_size" => RuleConfig::OrderSize(Self::parse_order_size_config(&rule.parameters)?),
            "position_limit" => RuleConfig::PositionLimit(Self::parse_position_limit_config(&rule.parameters)?),
            "daily_loss_limit" => RuleConfig::DailyLoss(Self::parse_daily_loss_config(&rule.parameters)?),
            "leverage_limit" => RuleConfig::Leverage(Self::parse_leverage_config(&rule.parameters)?),
            "volatility_control" => RuleConfig::Volatility(Self::parse_volatility_config(&rule.parameters)?),
            _ => RuleConfig::Generic(rule.parameters.clone()),
        };
        
        Ok(config)
    }

    /// 解析订单大小配置
    fn parse_order_size_config(params: &Value) -> SigmaXResult<OrderSizeConfig> {
        Ok(OrderSizeConfig {
            max_amount: params.get("max_amount")
                .and_then(|v| serde_json::from_value(v.clone()).ok())
                .unwrap_or(1000.0),
            currency: params.get("currency")
                .and_then(|v| serde_json::from_value(v.clone()).ok())
                .unwrap_or_else(|| "USDT".to_string()),
        })
    }

    /// 解析持仓限制配置
    fn parse_position_limit_config(params: &Value) -> SigmaXResult<PositionLimitConfig> {
        Ok(PositionLimitConfig {
            max_position_percent: params.get("max_position_percent")
                .and_then(|v| serde_json::from_value(v.clone()).ok())
                .unwrap_or(30.0),
            concentration_limit: params.get("concentration_limit")
                .and_then(|v| serde_json::from_value(v.clone()).ok())
                .unwrap_or(50.0),
        })
    }

    /// 解析日损失配置
    fn parse_daily_loss_config(params: &Value) -> SigmaXResult<DailyLossConfig> {
        Ok(DailyLossConfig {
            max_daily_loss_percent: params.get("max_daily_loss_percent")
                .and_then(|v| serde_json::from_value(v.clone()).ok())
                .unwrap_or(5.0),
            max_drawdown_percent: params.get("max_drawdown_percent")
                .and_then(|v| serde_json::from_value(v.clone()).ok())
                .unwrap_or(10.0),
        })
    }

    /// 解析杠杆配置
    fn parse_leverage_config(params: &Value) -> SigmaXResult<LeverageConfig> {
        Ok(LeverageConfig {
            max_leverage: params.get("max_leverage")
                .and_then(|v| serde_json::from_value(v.clone()).ok())
                .unwrap_or(3.0),
        })
    }

    /// 解析波动率配置
    fn parse_volatility_config(params: &Value) -> SigmaXResult<VolatilityConfig> {
        Ok(VolatilityConfig {
            volatility_threshold: params.get("volatility_threshold")
                .and_then(|v| serde_json::from_value(v.clone()).ok())
                .unwrap_or(0.2),
        })
    }
}

// ============================================================================
// 规则配置类型
// ============================================================================

/// 规则配置枚举
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RuleConfig {
    OrderSize(OrderSizeConfig),
    PositionLimit(PositionLimitConfig),
    DailyLoss(DailyLossConfig),
    Leverage(LeverageConfig),
    Volatility(VolatilityConfig),
    Generic(Value),
}

/// 订单大小配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrderSizeConfig {
    pub max_amount: f64,
    pub currency: String,
}

/// 持仓限制配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PositionLimitConfig {
    pub max_position_percent: f64,
    pub concentration_limit: f64,
}

/// 日损失配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DailyLossConfig {
    pub max_daily_loss_percent: f64,
    pub max_drawdown_percent: f64,
}

/// 杠杆配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LeverageConfig {
    pub max_leverage: f64,
}

/// 波动率配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VolatilityConfig {
    pub volatility_threshold: f64,
}
