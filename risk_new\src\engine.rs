//! 新的风险引擎实现

use async_trait::async_trait;
use std::sync::Arc;
use std::time::Instant;
use tracing::{debug, info, warn, error};

use crate::types::*;
use serde::{Serialize, Deserialize};
use crate::rules::RuleEngine;
use crate::metrics::RiskMetricsCalculator;
use sigmax_core::{Order, SigmaXResult, SigmaXError};

/// 风险引擎接口
#[async_trait]
pub trait RiskEngine: Send + Sync {
    /// 检查订单风险
    async fn check_order_risk(
        &self,
        order: &Order,
        context: &RiskContext,
    ) -> SigmaXResult<RiskCheckResult>;
    
    /// 检查持仓风险
    async fn check_position_risk(
        &self,
        positions: &[Position],
        context: &RiskContext,
    ) -> SigmaXResult<RiskCheckResult>;
    
    /// 检查投资组合风险
    async fn check_portfolio_risk(
        &self,
        portfolio: &PortfolioState,
        context: &RiskContext,
    ) -> SigmaXResult<RiskCheckResult>;
    
    /// 获取实时风险指标
    async fn get_risk_metrics(&self, context: &RiskContext) -> SigmaXResult<RiskMetrics>;
    
    /// 批量风险检查
    async fn check_batch_risk(
        &self,
        requests: Vec<RiskCheckRequest>,
    ) -> SigmaXResult<Vec<RiskCheckResult>>;
}

/// 风险检查请求
#[derive(Debug, Clone)]
pub struct RiskCheckRequest {
    pub check_type: RiskCheckType,
    pub order: Option<Order>,
    pub positions: Option<Vec<Position>>,
    pub portfolio: Option<PortfolioState>,
    pub context: RiskContext,
}

/// 统一风险引擎实现
pub struct UnifiedRiskEngine {
    /// 规则引擎
    rule_engine: Arc<dyn RuleEngine>,
    /// 风险指标计算器
    metrics_calculator: Arc<dyn RiskMetricsCalculator>,
    /// 配置
    config: RiskEngineConfig,
}

/// 风险引擎配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskEngineConfig {
    /// 是否启用并行规则执行
    pub parallel_execution: bool,
    /// 最大执行时间（毫秒）
    pub max_execution_time_ms: u64,
    /// 是否启用缓存
    pub enable_cache: bool,
    /// 默认风险等级
    pub default_risk_level: RiskLevel,
}

impl Default for RiskEngineConfig {
    fn default() -> Self {
        Self {
            parallel_execution: true,
            max_execution_time_ms: 5000,
            enable_cache: true,
            default_risk_level: RiskLevel::Medium,
        }
    }
}

impl UnifiedRiskEngine {
    /// 创建新的风险引擎实例
    pub fn new(
        rule_engine: Arc<dyn RuleEngine>,
        metrics_calculator: Arc<dyn RiskMetricsCalculator>,
        config: Option<RiskEngineConfig>,
    ) -> Self {
        Self {
            rule_engine,
            metrics_calculator,
            config: config.unwrap_or_default(),
        }
    }

    /// 执行核心风险检查逻辑
    async fn execute_risk_check(
        &self,
        request: RiskCheckRequest,
    ) -> SigmaXResult<RiskCheckResult> {
        let start_time = Instant::now();
        
        debug!("开始执行风险检查: {:?}", request.check_type);

        // 1. 获取适用的规则
        let applicable_rules = self.rule_engine
            .get_applicable_rules(&request.context)
            .await?;

        if applicable_rules.is_empty() {
            debug!("没有找到适用的风险规则");
            return Ok(RiskCheckResult {
                passed: true,
                risk_level: RiskLevel::Low,
                risk_score: 0.0,
                check_type: request.check_type,
                message: Some("没有适用的风险规则".to_string()),
                warnings: vec![],
                suggestions: vec![],
                violated_rules: vec![],
                timestamp: chrono::Utc::now(),
                execution_time_ms: start_time.elapsed().as_millis() as u64,
            });
        }

        // 2. 构建规则执行上下文
        let rule_context = crate::rules::RuleExecutionContext {
            order: request.order.clone(),
            positions: request.positions.clone(),
            portfolio: request.portfolio.clone(),
            market_data: None, // TODO: 从市场数据服务获取
            risk_context: request.context.clone(),
        };

        // 3. 执行规则检查
        let rule_results = self.rule_engine
            .execute_rules(&applicable_rules, &rule_context)
            .await?;

        // 4. 分析结果并生成最终检查结果
        let check_result = self.analyze_rule_results(
            request.check_type,
            rule_results,
            start_time.elapsed().as_millis() as u64,
        ).await?;

        debug!("风险检查完成: 通过={}, 风险等级={:?}", 
               check_result.passed, check_result.risk_level);

        Ok(check_result)
    }

    /// 分析规则执行结果并生成最终检查结果
    async fn analyze_rule_results(
        &self,
        check_type: RiskCheckType,
        rule_results: Vec<crate::rules::RuleExecutionResult>,
        execution_time_ms: u64,
    ) -> SigmaXResult<RiskCheckResult> {
        let mut violated_rules = Vec::new();
        let mut warnings = Vec::new();
        let mut suggestions = Vec::new();
        let mut max_risk_score: f64 = 0.0;
        let mut max_risk_level = RiskLevel::Low;

        for result in rule_results {
            match result.result {
                crate::rules::RuleResult::Pass => {
                    debug!("规则 {} 检查通过", result.rule_name);
                }
                crate::rules::RuleResult::Fail { reason, severity } => {
                    warn!("规则 {} 检查失败: {}", result.rule_name, reason);
                    
                    let violated_rule = ViolatedRule {
                        rule_id: result.rule_id,
                        rule_name: result.rule_name.clone(),
                        rule_type: RuleType::Custom(result.rule_name.clone()), // 简化处理
                        violation_reason: reason,
                        severity,
                        suggested_action: result.details,
                    };
                    
                    violated_rules.push(violated_rule);
                    
                    // 更新最高风险等级
                    if severity > max_risk_level {
                        max_risk_level = severity;
                    }
                    
                    // 更新风险评分
                    max_risk_score = max_risk_score.max(result.risk_contribution);
                }
                crate::rules::RuleResult::Skip { reason } => {
                    debug!("规则 {} 被跳过: {}", result.rule_name, reason);
                }
                crate::rules::RuleResult::Error { error } => {
                    error!("规则 {} 执行错误: {}", result.rule_name, error);
                    warnings.push(format!("规则 {} 执行异常: {}", result.rule_name, error));
                }
            }
        }

        let passed = violated_rules.is_empty();
        let message = if passed {
            Some("所有风险检查通过".to_string())
        } else {
            Some(format!("风险检查失败，违反 {} 个规则", violated_rules.len()))
        };

        Ok(RiskCheckResult {
            passed,
            risk_level: max_risk_level,
            risk_score: max_risk_score,
            check_type,
            message,
            warnings,
            suggestions,
            violated_rules,
            timestamp: chrono::Utc::now(),
            execution_time_ms,
        })
    }
}

#[async_trait]
impl RiskEngine for UnifiedRiskEngine {
    async fn check_order_risk(
        &self,
        order: &Order,
        context: &RiskContext,
    ) -> SigmaXResult<RiskCheckResult> {
        let request = RiskCheckRequest {
            check_type: RiskCheckType::Order,
            order: Some(order.clone()),
            positions: None,
            portfolio: None,
            context: context.clone(),
        };

        self.execute_risk_check(request).await
    }

    async fn check_position_risk(
        &self,
        positions: &[Position],
        context: &RiskContext,
    ) -> SigmaXResult<RiskCheckResult> {
        let request = RiskCheckRequest {
            check_type: RiskCheckType::Position,
            order: None,
            positions: Some(positions.to_vec()),
            portfolio: None,
            context: context.clone(),
        };

        self.execute_risk_check(request).await
    }

    async fn check_portfolio_risk(
        &self,
        portfolio: &PortfolioState,
        context: &RiskContext,
    ) -> SigmaXResult<RiskCheckResult> {
        let request = RiskCheckRequest {
            check_type: RiskCheckType::Portfolio,
            order: None,
            positions: Some(portfolio.positions.clone()),
            portfolio: Some(portfolio.clone()),
            context: context.clone(),
        };

        self.execute_risk_check(request).await
    }

    async fn get_risk_metrics(&self, context: &RiskContext) -> SigmaXResult<RiskMetrics> {
        // 如果上下文中有投资组合状态，计算投资组合指标
        if let Some(portfolio) = &context.portfolio_state {
            self.metrics_calculator
                .calculate_portfolio_metrics(portfolio, context)
                .await
        } else {
            // 返回默认的空指标
            Ok(RiskMetrics {
                var_95: rust_decimal::Decimal::ZERO,
                var_99: rust_decimal::Decimal::ZERO,
                cvar_95: rust_decimal::Decimal::ZERO,
                cvar_99: rust_decimal::Decimal::ZERO,
                volatility: 0.0,
                sharpe_ratio: 0.0,
                max_drawdown: 0.0,
                beta: None,
                calculated_at: chrono::Utc::now(),
            })
        }
    }

    async fn check_batch_risk(
        &self,
        requests: Vec<RiskCheckRequest>,
    ) -> SigmaXResult<Vec<RiskCheckResult>> {
        let mut results = Vec::with_capacity(requests.len());

        if self.config.parallel_execution {
            // 并行执行（简化实现，实际应使用 tokio::spawn）
            for request in requests {
                let result = self.execute_risk_check(request).await?;
                results.push(result);
            }
        } else {
            // 串行执行
            for request in requests {
                let result = self.execute_risk_check(request).await?;
                results.push(result);
            }
        }

        Ok(results)
    }
}
