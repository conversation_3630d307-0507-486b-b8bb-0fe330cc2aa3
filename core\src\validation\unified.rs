//! 统一验证系统
//!
//! 提供基于validator crate的统一验证接口，替换旧的Validatable trait

use validator::Validate;
use crate::{SigmaXResult, SigmaXError};

/// 统一验证trait
/// 
/// 所有需要验证的数据结构都应该实现此trait
pub trait UnifiedValidate {
    /// 执行验证并返回SigmaX标准错误
    fn validate_unified(&self) -> SigmaXResult<()>;
}

/// 为所有实现了validator::Validate的类型自动实现UnifiedValidate
impl<T> UnifiedValidate for T 
where 
    T: Validate,
{
    fn validate_unified(&self) -> SigmaXResult<()> {
        match self.validate() {
            Ok(()) => Ok(()),
            Err(validation_errors) => Err(SigmaXError::from(validation_errors)),
        }
    }
}

/// 验证工具函数集合
pub struct ValidationUtils;

impl ValidationUtils {
    /// 验证字符串非空
    pub fn validate_non_empty_string(value: &str, field_name: &str) -> SigmaXResult<()> {
        if value.trim().is_empty() {
            return Err(SigmaXError::ValidationError(
                format!("{} cannot be empty", field_name)
            ));
        }
        Ok(())
    }

    /// 验证正数
    pub fn validate_positive_decimal(value: &rust_decimal::Decimal, field_name: &str) -> SigmaXResult<()> {
        if *value <= rust_decimal::Decimal::ZERO {
            return Err(SigmaXError::ValidationError(
                format!("{} must be positive", field_name)
            ));
        }
        Ok(())
    }

    /// 验证非负数
    pub fn validate_non_negative_decimal(value: &rust_decimal::Decimal, field_name: &str) -> SigmaXResult<()> {
        if *value < rust_decimal::Decimal::ZERO {
            return Err(SigmaXError::ValidationError(
                format!("{} cannot be negative", field_name)
            ));
        }
        Ok(())
    }

    /// 验证范围
    pub fn validate_range_decimal(
        value: &rust_decimal::Decimal, 
        min: rust_decimal::Decimal, 
        max: rust_decimal::Decimal, 
        field_name: &str
    ) -> SigmaXResult<()> {
        if *value < min || *value > max {
            return Err(SigmaXError::ValidationError(
                format!("{} must be between {} and {}", field_name, min, max)
            ));
        }
        Ok(())
    }

    /// 验证百分比（0-1之间）
    pub fn validate_percentage(value: &rust_decimal::Decimal, field_name: &str) -> SigmaXResult<()> {
        Self::validate_range_decimal(
            value, 
            rust_decimal::Decimal::ZERO, 
            rust_decimal::Decimal::ONE, 
            field_name
        )
    }

    /// 验证UUID格式
    pub fn validate_uuid_string(value: &str, field_name: &str) -> SigmaXResult<()> {
        if uuid::Uuid::parse_str(value).is_err() {
            return Err(SigmaXError::ValidationError(
                format!("{} must be a valid UUID", field_name)
            ));
        }
        Ok(())
    }

    /// 批量验证多个条件
    pub fn validate_all(validations: Vec<SigmaXResult<()>>) -> SigmaXResult<()> {
        let errors: Vec<String> = validations
            .into_iter()
            .filter_map(|result| match result {
                Err(SigmaXError::ValidationError(msg)) => Some(msg),
                Err(other_error) => Some(other_error.to_string()),
                Ok(()) => None,
            })
            .collect();

        if errors.is_empty() {
            Ok(())
        } else {
            Err(SigmaXError::ValidationError(errors.join("; ")))
        }
    }
}

/// 自定义验证器函数类型
pub type CustomValidator<T> = fn(&T) -> SigmaXResult<()>;

/// 验证器组合器
pub struct ValidatorComposer<T> {
    validators: Vec<CustomValidator<T>>,
}

impl<T> ValidatorComposer<T> {
    /// 创建新的验证器组合器
    pub fn new() -> Self {
        Self {
            validators: Vec::new(),
        }
    }

    /// 添加验证器
    pub fn add_validator(mut self, validator: CustomValidator<T>) -> Self {
        self.validators.push(validator);
        self
    }

    /// 执行所有验证器
    pub fn validate(&self, value: &T) -> SigmaXResult<()> {
        let results: Vec<SigmaXResult<()>> = self.validators
            .iter()
            .map(|validator| validator(value))
            .collect();

        ValidationUtils::validate_all(results)
    }
}

impl<T> Default for ValidatorComposer<T> {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use validator::Validate;

    #[derive(Validate)]
    struct TestStruct {
        #[validate(length(min = 1, message = "Name cannot be empty"))]
        name: String,
        #[validate(range(min = 0.0, message = "Age must be non-negative"))]
        age: f64,
    }

    #[test]
    fn test_unified_validate_success() {
        let test_data = TestStruct {
            name: "Test".to_string(),
            age: 25.0,
        };

        assert!(test_data.validate_unified().is_ok());
    }

    #[test]
    fn test_unified_validate_failure() {
        let test_data = TestStruct {
            name: "".to_string(),
            age: -1.0,
        };

        let result = test_data.validate_unified();
        assert!(result.is_err());
        
        if let Err(SigmaXError::ValidationError(msg)) = result {
            assert!(msg.contains("Name cannot be empty"));
            assert!(msg.contains("Age must be non-negative"));
        } else {
            panic!("Expected ValidationError");
        }
    }

    #[test]
    fn test_validation_utils() {
        // 测试非空字符串验证
        assert!(ValidationUtils::validate_non_empty_string("test", "field").is_ok());
        assert!(ValidationUtils::validate_non_empty_string("", "field").is_err());

        // 测试正数验证
        let positive = rust_decimal::Decimal::new(100, 0);
        let zero = rust_decimal::Decimal::ZERO;
        assert!(ValidationUtils::validate_positive_decimal(&positive, "field").is_ok());
        assert!(ValidationUtils::validate_positive_decimal(&zero, "field").is_err());
    }

    #[test]
    fn test_validator_composer() {
        let composer = ValidatorComposer::new()
            .add_validator(|s: &String| {
                if s.len() < 3 {
                    Err(SigmaXError::ValidationError("Too short".to_string()))
                } else {
                    Ok(())
                }
            })
            .add_validator(|s: &String| {
                if s.contains("invalid") {
                    Err(SigmaXError::ValidationError("Contains invalid text".to_string()))
                } else {
                    Ok(())
                }
            });

        assert!(composer.validate(&"valid_string".to_string()).is_ok());
        assert!(composer.validate(&"no".to_string()).is_err());
        assert!(composer.validate(&"invalid_string".to_string()).is_err());
    }
}
