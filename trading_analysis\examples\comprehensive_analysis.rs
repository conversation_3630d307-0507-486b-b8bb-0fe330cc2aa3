//! 综合交易分析示例
//! 
//! 展示 SigmaX Trading Analysis 模块的全部功能：
//! - FIFO 盈亏分析
//! - 绩效指标计算
//! - 风险分析
//! - 技术指标计算

use sigmax_trading_analysis::{
    // FIFO 分析
    analyze_trades_with_fifo,
    
    // 绩效分析
    calculate_performance_metrics,
    
    // 风险分析
    calculate_risk_metrics,
    
    // 技术分析
    calculate_technical_indicators,
    calculate_sma,
    calculate_ema,
    calculate_rsi,
};

use sigmax_core::{Trade, TradingPair, OrderSide, ExchangeId};
use chrono::Utc;
use rust_decimal::Decimal;
use uuid::Uuid;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 SigmaX 综合交易分析示例");
    println!("{}", "=".repeat(60));

    // 创建测试数据
    let trading_pair = TradingPair::new("BTC", "USDT");
    let trades = create_comprehensive_test_data(&trading_pair);
    let price_series = create_price_series();
    let initial_capital = Decimal::from(100000);
    let risk_free_rate = Decimal::from_f32_retain(0.03).unwrap(); // 3% 年化无风险利率

    println!("📊 开始分析 {} 笔交易记录...\n", trades.len());

    // 1. FIFO 盈亏分析
    println!("🔍 **第一步：FIFO 盈亏分析**");
    println!("{}", "-".repeat(40));
    
    let fifo_result = analyze_trades_with_fifo(&trades)?;
    
    println!("✅ FIFO 分析完成:");
    println!("   📈 已实现交易对: {}", fifo_result.realized_pnl_pairs.len());
    println!("   📦 未平仓头寸: {}", fifo_result.unrealized_positions.len());
    println!("   💰 总已实现盈亏: {} USDT", fifo_result.total_realized_pnl());
    println!("   🎯 胜率: {:.2}%", fifo_result.win_rate() * Decimal::from(100));
    println!();

    // 2. 绩效指标分析
    println!("📈 **第二步：绩效指标分析**");
    println!("{}", "-".repeat(40));
    
    let performance_metrics = calculate_performance_metrics(
        &fifo_result,
        initial_capital,
        risk_free_rate,
    )?;
    
    println!("✅ 绩效分析完成:");
    println!("   📊 总收益率: {:.2}%", performance_metrics.total_return * Decimal::from(100));
    println!("   📅 年化收益率: {:.2}%", performance_metrics.annualized_return * Decimal::from(100));
    println!("   ⚡ 夏普比率: {:.2}", performance_metrics.sharpe_ratio);
    println!("   📉 最大回撤: {:.2}%", performance_metrics.max_drawdown * Decimal::from(100));
    println!("   🎯 卡尔马比率: {:.2}", performance_metrics.calmar_ratio);
    println!("   📈 波动率(年化): {:.2}%", performance_metrics.volatility * Decimal::from(100));
    println!("   🔄 索提诺比率: {:.2}", performance_metrics.sortino_ratio);
    println!("   ⏱️  平均持仓时间: {:.1} 天", performance_metrics.average_holding_period);
    println!("   🔄 交易频率: {:.1} 次/月", performance_metrics.trading_frequency);
    println!("   🏆 风险等级: {:?}", performance_metrics.risk_level());
    println!();

    // 3. 风险分析
    println!("⚠️  **第三步：风险分析**");
    println!("{}", "-".repeat(40));
    
    let risk_metrics = calculate_risk_metrics(
        &fifo_result.realized_pnl_pairs,
        initial_capital,
        None, // 没有基准数据
    )?;
    
    println!("✅ 风险分析完成:");
    println!("   📊 VaR (95%): {:.2}%", risk_metrics.var_95 * Decimal::from(100));
    println!("   📊 VaR (99%): {:.2}%", risk_metrics.var_99 * Decimal::from(100));
    println!("   📉 CVaR (95%): {:.2}%", risk_metrics.cvar_95 * Decimal::from(100));
    println!("   📉 CVaR (99%): {:.2}%", risk_metrics.cvar_99 * Decimal::from(100));
    println!("   📈 日波动率: {:.2}%", risk_metrics.daily_volatility * Decimal::from(100));
    println!("   📅 年化波动率: {:.2}%", risk_metrics.annual_volatility * Decimal::from(100));
    println!("   📉 下行偏差: {:.2}%", risk_metrics.downside_deviation * Decimal::from(100));
    println!("   🔴 最大连续亏损: {} 次", risk_metrics.max_consecutive_losses);
    println!("   💸 最大连续亏损金额: {} USDT", risk_metrics.max_consecutive_loss_amount);
    println!("   🎯 风险评估: {:?}", risk_metrics.risk_assessment());
    println!();

    // 4. 技术指标分析
    println!("📊 **第四步：技术指标分析**");
    println!("{}", "-".repeat(40));
    
    let technical_indicators = calculate_technical_indicators(&price_series, 14)?;
    
    println!("✅ 技术分析完成:");
    
    // SMA
    let sma_20 = calculate_sma(&price_series, 20);
    if !sma_20.is_empty() {
        println!("   📈 SMA(20) 最新值: {}", sma_20.last().unwrap());
    }
    
    // EMA
    let ema_12 = calculate_ema(&price_series, 12);
    if !ema_12.is_empty() {
        println!("   📊 EMA(12) 最新值: {}", ema_12.last().unwrap());
    }
    
    // RSI
    let rsi_14 = calculate_rsi(&price_series, 14)?;
    if !rsi_14.is_empty() {
        let latest_rsi = rsi_14.last().unwrap();
        println!("   🎯 RSI(14) 最新值: {:.2}", latest_rsi);
        
        let rsi_signal = if *latest_rsi > Decimal::from(70) {
            "超买"
        } else if *latest_rsi < Decimal::from(30) {
            "超卖"
        } else {
            "中性"
        };
        println!("   📊 RSI 信号: {}", rsi_signal);
    }
    
    // MACD
    if !technical_indicators.macd_line.is_empty() {
        println!("   📈 MACD 线数量: {}", technical_indicators.macd_line.len());
        println!("   📊 信号线数量: {}", technical_indicators.macd_signal.len());
        println!("   📊 柱状图数量: {}", technical_indicators.macd_histogram.len());
    }
    
    // 布林带
    if !technical_indicators.bollinger_upper.is_empty() {
        let latest_upper = technical_indicators.bollinger_upper.last().unwrap();
        let latest_middle = technical_indicators.bollinger_middle.last().unwrap();
        let latest_lower = technical_indicators.bollinger_lower.last().unwrap();
        let current_price = price_series.last().unwrap();
        
        println!("   📊 布林带上轨: {}", latest_upper);
        println!("   📊 布林带中轨: {}", latest_middle);
        println!("   📊 布林带下轨: {}", latest_lower);
        
        let bollinger_signal = if *current_price > *latest_upper {
            "价格突破上轨"
        } else if *current_price < *latest_lower {
            "价格跌破下轨"
        } else {
            "价格在通道内"
        };
        println!("   🎯 布林带信号: {}", bollinger_signal);
    }
    
    println!();

    // 5. 综合评估
    println!("🎯 **综合评估报告**");
    println!("{}", "=".repeat(40));
    
    let overall_score = calculate_overall_score(&performance_metrics, &risk_metrics);
    
    println!("📊 **投资组合综合评分: {:.1}/10**", overall_score);
    
    if overall_score >= 8.0 {
        println!("🏆 评级: 优秀 - 这是一个表现卓越的交易策略！");
    } else if overall_score >= 6.0 {
        println!("👍 评级: 良好 - 策略表现不错，有改进空间");
    } else if overall_score >= 4.0 {
        println!("⚠️  评级: 一般 - 策略需要优化");
    } else {
        println!("❌ 评级: 较差 - 建议重新评估策略");
    }
    
    println!("\n✨ 综合分析完成！");
    println!("📝 建议: 基于以上分析结果，可以进一步优化交易策略的风险控制和收益目标。");

    Ok(())
}

/// 创建综合测试数据
fn create_comprehensive_test_data(trading_pair: &TradingPair) -> Vec<Trade> {
    vec![
        // 第一轮交易 - 盈利
        create_trade(trading_pair, OrderSide::Buy, "2.0", "45000.0", "2023-01-01T10:00:00Z"),
        create_trade(trading_pair, OrderSide::Sell, "1.0", "47000.0", "2023-01-03T10:00:00Z"),
        
        // 第二轮交易 - 亏损
        create_trade(trading_pair, OrderSide::Buy, "1.5", "48000.0", "2023-01-05T10:00:00Z"),
        create_trade(trading_pair, OrderSide::Sell, "1.0", "46000.0", "2023-01-07T10:00:00Z"),
        
        // 第三轮交易 - 盈利
        create_trade(trading_pair, OrderSide::Sell, "2.0", "50000.0", "2023-01-10T10:00:00Z"),
        
        // 第四轮交易 - 小幅盈利
        create_trade(trading_pair, OrderSide::Buy, "0.8", "49500.0", "2023-01-12T10:00:00Z"),
        create_trade(trading_pair, OrderSide::Sell, "0.3", "50500.0", "2023-01-14T10:00:00Z"),
    ]
}

/// 创建价格序列用于技术分析
fn create_price_series() -> Vec<Decimal> {
    vec![
        "45000.0", "45200.0", "44800.0", "45500.0", "46000.0",
        "46200.0", "45800.0", "46500.0", "47000.0", "46800.0",
        "47200.0", "47500.0", "47800.0", "48000.0", "47600.0",
        "48200.0", "48500.0", "48800.0", "49000.0", "48700.0",
        "49200.0", "49500.0", "49800.0", "50000.0", "49600.0",
        "50200.0", "50500.0", "50800.0", "51000.0", "50700.0",
    ].iter().map(|s| s.parse().unwrap()).collect()
}

/// 创建单个交易记录
fn create_trade(
    trading_pair: &TradingPair,
    side: OrderSide,
    quantity: &str,
    price: &str,
    timestamp: &str,
) -> Trade {
    let dt = chrono::DateTime::parse_from_rfc3339(timestamp)
        .unwrap()
        .with_timezone(&Utc);
    
    Trade {
        id: Uuid::new_v4(),
        order_id: Uuid::new_v4(),
        exchange_id: ExchangeId::Simulator,
        trading_pair: trading_pair.clone(),
        side,
        quantity: quantity.parse().unwrap(),
        price: price.parse().unwrap(),
        fee: Decimal::ZERO,
        fee_asset: Some("USDT".to_string()),
        executed_at: dt,
        created_at: dt,
    }
}

/// 计算综合评分
fn calculate_overall_score(
    performance: &sigmax_trading_analysis::PerformanceMetrics,
    _risk: &sigmax_trading_analysis::RiskMetrics,
) -> f64 {
    let mut score: f64 = 5.0; // 基础分
    
    // 收益率评分 (0-2分)
    let return_score = if performance.annualized_return > Decimal::from_f32_retain(0.2).unwrap() {
        2.0
    } else if performance.annualized_return > Decimal::from_f32_retain(0.1).unwrap() {
        1.5
    } else if performance.annualized_return > Decimal::ZERO {
        1.0
    } else {
        0.0
    };
    
    // 夏普比率评分 (0-2分)
    let sharpe_score = if performance.sharpe_ratio > Decimal::from(2) {
        2.0
    } else if performance.sharpe_ratio > Decimal::from(1) {
        1.5
    } else if performance.sharpe_ratio > Decimal::ZERO {
        1.0
    } else {
        0.0
    };
    
    // 最大回撤评分 (0-1分)
    let drawdown_score = if performance.max_drawdown < Decimal::from_f32_retain(0.05).unwrap() {
        1.0
    } else if performance.max_drawdown < Decimal::from_f32_retain(0.1).unwrap() {
        0.7
    } else if performance.max_drawdown < Decimal::from_f32_retain(0.2).unwrap() {
        0.4
    } else {
        0.0
    };
    
    score += return_score + sharpe_score + drawdown_score;
    score.min(10.0)
}
