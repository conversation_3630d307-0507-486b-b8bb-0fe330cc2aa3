//! FIFO分析模块基本使用示例

use sigmax_trading_analysis::{analyze_trades_with_fifo, FifoAnalysisResult};
use sigmax_core::{Trade, TradingPair, OrderSide, ExchangeId};
use chrono::{Utc, TimeZone};
use rust_decimal::Decimal;
use uuid::Uuid;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 SigmaX FIFO分析模块示例");
    println!("{}", "=".repeat(50));

    // 创建测试交易数据
    let trading_pair = TradingPair::new("BTC", "USDT");
    let trades = create_sample_trades(&trading_pair);

    println!("📊 分析 {} 笔交易记录...", trades.len());
    
    // 执行FIFO分析
    let result = analyze_trades_with_fifo(&trades)?;
    
    // 显示分析结果
    display_analysis_results(&result);
    
    Ok(())
}

/// 创建示例交易数据
fn create_sample_trades(trading_pair: &TradingPair) -> Vec<Trade> {
    vec![
        // 第一次买入
        Trade {
            id: Uuid::new_v4(),
            order_id: Uuid::new_v4(),
            exchange_id: ExchangeId::Simulator,
            trading_pair: trading_pair.clone(),
            side: OrderSide::Buy,
            quantity: "1.0".parse().unwrap(),
            price: "50000.0".parse().unwrap(),
            fee: "25.0".parse().unwrap(),
            fee_asset: Some("USDT".to_string()),
            executed_at: Utc.with_ymd_and_hms(2023, 1, 1, 10, 0, 0).unwrap(),
            created_at: Utc.with_ymd_and_hms(2023, 1, 1, 10, 0, 0).unwrap(),
        },
        // 第二次买入
        Trade {
            id: Uuid::new_v4(),
            order_id: Uuid::new_v4(),
            exchange_id: ExchangeId::Simulator,
            trading_pair: trading_pair.clone(),
            side: OrderSide::Buy,
            quantity: "0.5".parse().unwrap(),
            price: "48000.0".parse().unwrap(),
            fee: "12.0".parse().unwrap(),
            fee_asset: Some("USDT".to_string()),
            executed_at: Utc.with_ymd_and_hms(2023, 1, 2, 10, 0, 0).unwrap(),
            created_at: Utc.with_ymd_and_hms(2023, 1, 2, 10, 0, 0).unwrap(),
        },
        // 第一次卖出（部分）
        Trade {
            id: Uuid::new_v4(),
            order_id: Uuid::new_v4(),
            exchange_id: ExchangeId::Simulator,
            trading_pair: trading_pair.clone(),
            side: OrderSide::Sell,
            quantity: "0.8".parse().unwrap(),
            price: "52000.0".parse().unwrap(),
            fee: "20.8".parse().unwrap(),
            fee_asset: Some("USDT".to_string()),
            executed_at: Utc.with_ymd_and_hms(2023, 1, 3, 10, 0, 0).unwrap(),
            created_at: Utc.with_ymd_and_hms(2023, 1, 3, 10, 0, 0).unwrap(),
        },
        // 第二次卖出
        Trade {
            id: Uuid::new_v4(),
            order_id: Uuid::new_v4(),
            exchange_id: ExchangeId::Simulator,
            trading_pair: trading_pair.clone(),
            side: OrderSide::Sell,
            quantity: "0.7".parse().unwrap(),
            price: "49000.0".parse().unwrap(),
            fee: "17.15".parse().unwrap(),
            fee_asset: Some("USDT".to_string()),
            executed_at: Utc.with_ymd_and_hms(2023, 1, 4, 10, 0, 0).unwrap(),
            created_at: Utc.with_ymd_and_hms(2023, 1, 4, 10, 0, 0).unwrap(),
        },
    ]
}

/// 显示分析结果
fn display_analysis_results(result: &FifoAnalysisResult) {
    println!("\n📈 **FIFO分析结果**");
    println!("{}", "-".repeat(30));
    
    // 基本统计
    println!("🔢 **基本统计**");
    println!("   总交易次数: {}", result.total_trades());
    println!("   已实现交易对: {}", result.realized_pnl_pairs.len());
    println!("   未平仓头寸: {}", result.unrealized_positions.len());
    
    // 盈亏分析
    println!("\n💰 **盈亏分析**");
    println!("   总已实现盈亏: {} USDT", result.total_realized_pnl());
    println!("   胜率: {:.2}%", result.win_rate() * Decimal::from(100));
    println!("   盈利交易数: {}", result.winning_trades_count());
    println!("   亏损交易数: {}", result.losing_trades_count());
    
    if result.winning_trades_count() > 0 {
        println!("   平均盈利: {} USDT", result.average_win());
    }
    
    if result.losing_trades_count() > 0 {
        println!("   平均亏损: {} USDT", result.average_loss());
        println!("   盈亏比: {:.2}", result.profit_factor());
    }
    
    // 详细交易对
    if !result.realized_pnl_pairs.is_empty() {
        println!("\n📋 **已实现交易对详情**");
        for (i, pair) in result.realized_pnl_pairs.iter().enumerate() {
            let status = if pair.is_profitable() { "✅ 盈利" } else { "❌ 亏损" };
            println!("   {}. {} | 数量: {} | 买入: {} | 卖出: {} | 盈亏: {} USDT ({:.2}%)",
                i + 1,
                status,
                pair.quantity,
                pair.entry_price,
                pair.exit_price,
                pair.pnl,
                pair.pnl_percentage
            );
        }
    }
    
    // 未平仓头寸
    if !result.unrealized_positions.is_empty() {
        println!("\n📦 **未平仓头寸**");
        for (i, position) in result.unrealized_positions.iter().enumerate() {
            println!("   {}. 数量: {} | 平均成本: {} | 持仓天数: {}",
                i + 1,
                position.quantity,
                position.average_entry_price,
                position.holding_days()
            );
        }
    }
    
    println!("\n✨ 分析完成！");
}
