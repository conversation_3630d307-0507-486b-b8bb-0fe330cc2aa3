//! SQL 交易记录仓储实现

use std::sync::Arc;
use chrono::{DateTime, Utc};
use sqlx::Row;
use sigmax_core::{
    Trade, TradingPair, SigmaXResult, SigmaXError, OrderSide, ExchangeId, Amount,
};
use uuid::Uuid;

type TradeId = Uuid;
use crate::DatabaseManager;
use crate::repositories::traits::TradeRepository;

/// SQL交易仓库实现
pub struct SqlTradeRepository {
    db: Arc<DatabaseManager>,
}

impl SqlTradeRepository {
    pub fn new(db: Arc<DatabaseManager>) -> Self {
        Self { db }
    }
}

impl TradeRepository for SqlTradeRepository {
    async fn save_trade(&self, trade: &Trade) -> SigmaXResult<()> {
        let pool = self.db.pool();

        sqlx::query(
            r#"
            INSERT INTO trades (
                id, order_id, trading_pair_id, side, quantity, price, fee, fee_asset,
                exchange_id, exchange_trade_id, executed_at, created_at
            ) VALUES (
                $1, $2,
                (SELECT id FROM trading_pairs WHERE symbol = $3),
                $4, $5, $6, $7, $8, $9, $10, $11, $12
            )
            ON CONFLICT (id) DO UPDATE SET
                quantity = EXCLUDED.quantity,
                price = EXCLUDED.price,
                fee = EXCLUDED.fee
            "#
        )
        .bind(&trade.id)
        .bind(&trade.order_id)
        .bind(&trade.trading_pair.symbol())
        .bind(&format!("{:?}", trade.side))
        .bind(&trade.quantity)
        .bind(&trade.price)
        .bind(&trade.fee)
        .bind(&trade.fee_asset)
        .bind(&trade.exchange_id.to_string())
        .bind(None::<String>) // exchange_trade_id
        .bind(&trade.executed_at)
        .bind(&trade.created_at)
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("Failed to save trade: {}", e)))?;

        Ok(())
    }

    async fn get_trades(&self) -> SigmaXResult<Vec<Trade>> {
        let pool = self.db.pool();

        let rows = sqlx::query(
            r#"
            SELECT t.*, tp.symbol as trading_pair_symbol
            FROM trades t
            JOIN trading_pairs tp ON t.trading_pair_id = tp.id
            ORDER BY t.executed_at DESC
            "#
        )
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("Failed to get trades: {}", e)))?;

        let mut trades = Vec::new();
        for row in rows {
            trades.push(self.row_to_trade(row)?);
        }

        Ok(trades)
    }

    async fn get_trades_by_pair(&self, pair: &TradingPair) -> SigmaXResult<Vec<Trade>> {
        let pool = self.db.pool();

        let rows = sqlx::query(
            r#"
            SELECT t.*, tp.symbol as trading_pair_symbol
            FROM trades t
            JOIN trading_pairs tp ON t.trading_pair_id = tp.id
            WHERE tp.symbol = $1
            ORDER BY t.executed_at DESC
            "#
        )
        .bind(&pair.symbol())
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("Failed to get trades by pair: {}", e)))?;

        let mut trades = Vec::new();
        for row in rows {
            trades.push(self.row_to_trade(row)?);
        }

        Ok(trades)
    }

    async fn get_trades_by_timerange(&self, start: DateTime<Utc>, end: DateTime<Utc>) -> SigmaXResult<Vec<Trade>> {
        let pool = self.db.pool();

        let rows = sqlx::query(
            r#"
            SELECT t.*, tp.symbol as trading_pair_symbol
            FROM trades t
            JOIN trading_pairs tp ON t.trading_pair_id = tp.id
            WHERE t.executed_at >= $1 AND t.executed_at <= $2
            ORDER BY t.executed_at DESC
            "#
        )
        .bind(&start)
        .bind(&end)
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("Failed to get trades by timerange: {}", e)))?;

        let mut trades = Vec::new();
        for row in rows {
            trades.push(self.row_to_trade(row)?);
        }

        Ok(trades)
    }
}

impl SqlTradeRepository {
    // ============================================================================
    // 批量操作
    // ============================================================================

    /// 批量保存交易记录
    pub async fn save_trades_batch(&self, trades: &[Trade]) -> SigmaXResult<()> {
        if trades.is_empty() {
            return Ok(());
        }

        let pool = self.db.pool();
        let mut tx = pool.begin().await
            .map_err(|e| SigmaXError::Database(format!("开始事务失败: {}", e)))?;

        for trade in trades {
            sqlx::query(
                r#"
                INSERT INTO trades (
                    id, order_id, trading_pair_id, side, quantity, price, fee, fee_asset,
                    exchange_id, exchange_trade_id, executed_at, created_at
                ) VALUES (
                    $1, $2,
                    (SELECT id FROM trading_pairs WHERE symbol = $3),
                    $4, $5, $6, $7, $8, $9, $10, $11, $12
                )
                ON CONFLICT (id) DO UPDATE SET
                    quantity = EXCLUDED.quantity,
                    price = EXCLUDED.price,
                    fee = EXCLUDED.fee
                "#
            )
            .bind(&trade.id)
            .bind(&trade.order_id)
            .bind(&trade.trading_pair.symbol())
            .bind(&format!("{:?}", trade.side))
            .bind(&trade.quantity)
            .bind(&trade.price)
            .bind(&trade.fee)
            .bind(&trade.fee_asset)
            .bind(&trade.exchange_id.to_string())
            .bind(None::<String>) // exchange_trade_id
            .bind(&trade.executed_at)
            .bind(&trade.created_at)
            .execute(&mut *tx)
            .await
            .map_err(|e| SigmaXError::Database(format!("批量保存交易记录失败: {}", e)))?;
        }

        tx.commit().await
            .map_err(|e| SigmaXError::Database(format!("提交事务失败: {}", e)))?;

        Ok(())
    }

    /// 批量删除交易记录
    pub async fn delete_trades_batch(&self, trade_ids: &[TradeId]) -> SigmaXResult<()> {
        if trade_ids.is_empty() {
            return Ok(());
        }

        let pool = self.db.pool();
        let mut query_builder = sqlx::QueryBuilder::new("DELETE FROM trades WHERE id = ANY(");
        query_builder.push_bind(trade_ids);
        query_builder.push(")");

        let result = query_builder.build()
            .execute(pool)
            .await
            .map_err(|e| SigmaXError::Database(format!("批量删除交易记录失败: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(SigmaXError::Database("没有交易记录被删除".to_string()));
        }

        Ok(())
    }

    /// 批量更新交易记录费用
    pub async fn update_fees_batch(&self, updates: &[(TradeId, Amount, Option<String>)]) -> SigmaXResult<()> {
        if updates.is_empty() {
            return Ok(());
        }

        let pool = self.db.pool();
        let mut tx = pool.begin().await
            .map_err(|e| SigmaXError::Database(format!("开始事务失败: {}", e)))?;

        for (trade_id, fee, fee_asset) in updates {
            sqlx::query(
                r#"
                UPDATE trades SET
                    fee = $2,
                    fee_asset = $3
                WHERE id = $1
                "#
            )
            .bind(trade_id)
            .bind(fee)
            .bind(fee_asset)
            .execute(&mut *tx)
            .await
            .map_err(|e| SigmaXError::Database(format!("批量更新交易费用失败: {}", e)))?;
        }

        tx.commit().await
            .map_err(|e| SigmaXError::Database(format!("提交事务失败: {}", e)))?;

        Ok(())
    }
}

impl SqlTradeRepository {
    fn row_to_trade(&self, row: sqlx::postgres::PgRow) -> SigmaXResult<Trade> {
        let trading_pair_symbol: String = row.get("trading_pair_symbol");
        let trading_pair = TradingPair::from_symbol(&trading_pair_symbol)
            .map_err(|e| SigmaXError::Database(format!("Invalid trading pair: {}", e)))?;

        let side_str: String = row.get("side");
        let side = match side_str.as_str() {
            "Buy" => OrderSide::Buy,
            "Sell" => OrderSide::Sell,
            _ => return Err(SigmaXError::Database(format!("Unknown order side: {}", side_str))),
        };

        let exchange_id_str: String = row.get("exchange_id");
        let exchange_id = ExchangeId::from(exchange_id_str);

        Ok(Trade {
            id: row.get("id"),
            order_id: row.get("order_id"),
            exchange_id,
            trading_pair,
            side,
            quantity: row.get("quantity"),
            price: row.get("price"),
            fee: row.get("fee"),
            fee_asset: row.get("fee_asset"),
            executed_at: row.get("executed_at"),
            created_at: row.get("created_at"),
        })
    }
}
