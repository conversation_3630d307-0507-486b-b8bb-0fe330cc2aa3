// SigmaX Trading System - 应用控制器层
// 协调Service和View之间的交互，处理应用逻辑

/**
 * 应用控制器类
 * 负责协调Service和View之间的交互，处理页面导航和应用生命周期
 */
class AppController {
    constructor(service, view) {
        this.service = service;
        this.view = view;
        this.initialized = false;

        // 绑定方法上下文
        this.init = this.init.bind(this);
        this.handleSidebarToggle = this.handleSidebarToggle.bind(this);
        this.handlePageNavigate = this.handlePageNavigate.bind(this);
        this.handleWindowResize = this.handleWindowResize.bind(this);
        this.handleHashChange = this.handleHashChange.bind(this);
        this.handleRetryInit = this.handleRetryInit.bind(this);
        this.handleEnterOffline = this.handleEnterOffline.bind(this);
        this.handleSystemStatusUpdate = this.handleSystemStatusUpdate.bind(this);
    }

    /**
     * 初始化应用控制器
     */
    async init() {
        if (this.initialized) {
            console.warn('应用控制器已经初始化');
            return;
        }

        console.log('🚀 开始初始化SigmaX应用...');

        try {
            // 显示初始化界面
            this.view.showSystemInitializing();

            // 绑定事件处理器
            this.bindEventHandlers();

            // 检查系统健康状态
            const isReady = await this.service.checkSystemHealth();

            if (isReady) {
                await this.initializeSuccessFlow();
            } else {
                this.initializeErrorFlow();
            }

            this.initialized = true;
            console.log('✅ SigmaX应用初始化完成');

        } catch (error) {
            console.error('❌ 应用初始化失败:', error);
            this.initializeErrorFlow();
        }
    }

    /**
     * 成功初始化流程
     */
    async initializeSuccessFlow() {
        // 启动系统状态检查
        this.service.startSystemStatusCheck(this.handleSystemStatusUpdate);

        // 加载初始页面
        await this.loadInitialPage();

        // 隐藏初始化界面
        this.view.hideSystemInitializing();
    }

    /**
     * 错误初始化流程
     */
    initializeErrorFlow() {
        this.view.showSystemError();
    }

    /**
     * 绑定事件处理器
     */
    bindEventHandlers() {
        this.view.bindEvents({
            onSidebarToggle: this.handleSidebarToggle,
            onPageNavigate: this.handlePageNavigate,
            onWindowResize: this.handleWindowResize,
            onHashChange: this.handleHashChange,
            onRetryInit: this.handleRetryInit,
            onEnterOffline: this.handleEnterOffline,
            onMobileResize: () => {
                // 移动端自动隐藏侧边栏
                if (this.service.getSidebarState()) {
                    this.handleSidebarToggle();
                }
            }
        });

        console.log('应用控制器事件绑定完成');
    }

    /**
     * 加载初始页面
     */
    async loadInitialPage() {
        const hash = window.location.hash.slice(1);
        const initialPage = hash || 'dashboard';
        await this.navigateTo(initialPage);
    }

    /**
     * 处理侧边栏切换
     */
    handleSidebarToggle() {
        const newState = this.service.toggleSidebar();
        this.view.toggleSidebar(newState);
        console.log(`侧边栏${newState ? '打开' : '关闭'}`);
    }

    /**
     * 处理页面导航
     * @param {string} page 目标页面
     */
    async handlePageNavigate(page) {
        await this.navigateTo(page);
    }

    /**
     * 处理窗口大小变化
     */
    handleWindowResize() {
        this.view.handleResize();
    }

    /**
     * 处理Hash变化
     * @param {string} hash 新的hash值
     */
    async handleHashChange(hash) {
        if (hash) {
            await this.navigateTo(hash);
        }
    }

    /**
     * 处理重试初始化
     */
    async handleRetryInit() {
        console.log('重试系统初始化...');
        this.initialized = false;
        await this.init();
    }

    /**
     * 处理进入离线模式
     */
    handleEnterOffline() {
        console.log('进入离线模式...');

        // 移除初始化界面
        const overlay = document.getElementById('system-initializing');
        if (overlay) {
            overlay.remove();
        }

        // 显示离线模式通知
        Utils.showNotification('已进入离线模式，部分功能可能不可用', 'warning', 5000);

        // 更新系统状态
        this.view.updateSystemStatus(false, '离线模式');

        // 加载初始页面
        this.loadInitialPage();

        this.initialized = true;
    }

    /**
     * 处理系统状态更新
     * @param {boolean} healthy 系统是否健康
     */
    handleSystemStatusUpdate(healthy) {
        this.view.updateSystemStatus(healthy);
    }

    /**
     * 导航到指定页面
     * @param {string} page 目标页面
     */
    async navigateTo(page) {
        console.log(`导航到页面: ${page}`);

        try {
            // 更新服务状态
            this.service.setCurrentPage(page);

            // 更新URL hash
            if (window.location.hash.slice(1) !== page) {
                window.location.hash = page;
            }

            // 获取页面信息
            const pageInfo = this.service.getPageInfo(page);

            // 更新页面标题
            this.view.updatePageTitle(pageInfo);

            // 更新菜单状态
            this.view.updateMenuState(page);

            // 加载页面内容
            await this.loadPageContent(page);

        } catch (error) {
            console.error('页面导航失败:', error);
            this.view.showPageError(error.message);
        }
    }

    /**
     * 加载页面内容
     * @param {string} page 页面名称
     */
    async loadPageContent(page) {
        // 显示加载状态
        this.view.showPageLoading();

        try {
            // 获取页面组件
            const component = await this.service.getPageComponent(page);

            // 显示页面内容
            this.view.showPageContent(component);

            // 初始化页面特定功能
            await this.service.initPageSpecificFeatures(page);

        } catch (error) {
            console.error('加载页面内容失败:', error);
            this.view.showPageError(error.message);
        }
    }

    /**
     * 显示开发信息
     * @param {string} page 页面名称
     */
    showDevelopmentInfo(page) {
        const info = this.service.getDevelopmentInfo(page);
        this.view.showDevelopmentInfo(info);
    }

    /**
     * 获取当前应用状态
     * @returns {Object} 应用状态
     */
    getState() {
        return {
            initialized: this.initialized,
            service: this.service.getState()
        };
    }

    /**
     * 重置应用状态
     */
    reset() {
        this.service.reset();
        this.view.updateSystemStatus(false);
        this.view.updateMenuState('dashboard');
        console.log('应用控制器状态已重置');
    }

    /**
     * 销毁控制器
     */
    destroy() {
        if (this.service && typeof this.service.destroy === 'function') {
            this.service.destroy();
        }

        if (this.view && typeof this.view.destroy === 'function') {
            this.view.destroy();
        }

        this.initialized = false;
        console.log('应用控制器已销毁');
    }

    /**
     * 检查系统健康状态
     * @returns {Promise<boolean>} 系统健康状态
     */
    async checkHealth() {
        try {
            return await this.service.checkSystemHealth();
        } catch (error) {
            console.error('健康检查失败:', error);
            return false;
        }
    }

    /**
     * 获取服务层实例
     * @returns {AppService} 服务层实例
     */
    getService() {
        return this.service;
    }

    /**
     * 获取视图层实例
     * @returns {AppView} 视图层实例
     */
    getView() {
        return this.view;
    }

    /**
     * 设置调试模式
     * @param {boolean} enabled 是否启用调试
     */
    setDebug(enabled) {
        if (enabled) {
            console.log('应用调试模式已启用');
            window.appDebug = {
                controller: this,
                service: this.service,
                view: this.view,
                state: () => this.getState()
            };
        } else {
            console.log('应用调试模式已禁用');
            delete window.appDebug;
        }
    }
}

// 导出控制器类
window.AppController = AppController;

// 兼容CommonJS
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AppController;
}
