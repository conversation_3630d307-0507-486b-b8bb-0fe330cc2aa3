//! 风控配置管理服务
//!
//! 负责从数据库读取、缓存和管理风控配置

use sigmax_core::{RiskManagementConfig, SigmaXResult};
use sigmax_database::repositories::traits::{RiskRepository, RiskRuleRecord};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock as AsyncRwLock;

// ============================================================================
// 配置管理器
// ============================================================================

/// 风控配置管理器
///
/// 负责：
/// 1. 从数据库读取风控规则
/// 2. 缓存配置以提高性能
/// 3. 监听配置变更
/// 4. 提供配置热更新功能
pub struct RiskConfigManager {
    /// 数据库仓储
    repository: Arc<dyn RiskRepository>,
    /// 默认配置
    default_config: RiskManagementConfig,
    /// 规则缓存
    rules_cache: Arc<AsyncRwLock<RulesCache>>,
    /// 缓存配置
    cache_config: CacheConfig,
}

/// 规则缓存
#[derive(Debug, Clone)]
struct RulesCache {
    /// 按类型分组的规则
    rules_by_type: HashMap<String, Vec<RiskRuleRecord>>,
    /// 所有启用的规则
    enabled_rules: Vec<RiskRuleRecord>,
    /// 最后更新时间
    last_updated: Instant,
}

/// 缓存配置
#[derive(Debug, Clone)]
pub struct CacheConfig {
    /// 配置缓存过期时间
    pub config_ttl: Duration,
    /// 规则缓存过期时间
    pub rules_ttl: Duration,
    /// 自动刷新间隔
    pub refresh_interval: Duration,
    /// 是否启用自动刷新
    pub auto_refresh: bool,
}

impl Default for CacheConfig {
    fn default() -> Self {
        Self {
            config_ttl: Duration::from_secs(300),      // 5分钟
            rules_ttl: Duration::from_secs(60),        // 1分钟
            refresh_interval: Duration::from_secs(30), // 30秒
            auto_refresh: true,
        }
    }
}

impl RiskConfigManager {
    /// 创建新的配置管理器
    pub fn new(
        repository: Arc<dyn RiskRepository>,
        cache_config: Option<CacheConfig>,
    ) -> Self {
        Self {
            repository,
            default_config: RiskManagementConfig::default(),
            rules_cache: Arc::new(AsyncRwLock::new(RulesCache::new())),
            cache_config: cache_config.unwrap_or_default(),
        }
    }

    /// 获取全局风控配置
    /// 由于线上数据库没有 risk_config 表，直接返回默认配置
    pub async fn get_global_config(&self) -> SigmaXResult<RiskManagementConfig> {
        Ok(self.default_config.clone())
    }

    /// 获取策略特定的风控配置
    /// 由于线上数据库没有 risk_config 表，直接返回默认配置
    pub async fn get_strategy_config(&self, _strategy_type: &str) -> SigmaXResult<RiskManagementConfig> {
        Ok(self.default_config.clone())
    }

    /// 获取启用的风控规则
    pub async fn get_enabled_rules(&self) -> SigmaXResult<Vec<RiskRuleRecord>> {
        // 首先尝试从缓存获取
        if let Some(rules) = self.get_cached_enabled_rules().await? {
            return Ok(rules);
        }

        // 缓存未命中，从数据库读取
        let rules = self.repository.get_enabled_risk_rules().await?;
        
        // 更新缓存
        self.cache_enabled_rules(&rules).await?;
        
        Ok(rules)
    }

    /// 根据类型获取风控规则
    pub async fn get_rules_by_type(&self, rule_type: &str) -> SigmaXResult<Vec<RiskRuleRecord>> {
        // 首先尝试从缓存获取
        if let Some(rules) = self.get_cached_rules_by_type(rule_type).await? {
            return Ok(rules);
        }

        // 缓存未命中，从数据库读取
        let rules = self.repository.get_risk_rules_by_type(rule_type).await?;
        
        // 更新缓存
        self.cache_rules_by_type(rule_type, &rules).await?;
        
        Ok(rules)
    }

    /// 强制刷新规则缓存
    pub async fn refresh_cache(&self) -> SigmaXResult<()> {
        // 清空规则缓存
        {
            let mut rules_cache = self.rules_cache.write().await;
            *rules_cache = RulesCache::new();
        }

        // 预加载常用规则
        let _ = self.get_enabled_rules().await?;

        Ok(())
    }
}

// ============================================================================
// 私有方法 - 缓存操作
// ============================================================================

impl RiskConfigManager {

    /// 从缓存获取启用的规则
    async fn get_cached_enabled_rules(&self) -> SigmaXResult<Option<Vec<RiskRuleRecord>>> {
        let cache = self.rules_cache.read().await;
        
        if cache.last_updated.elapsed() < self.cache_config.rules_ttl {
            return Ok(Some(cache.enabled_rules.clone()));
        }
        
        Ok(None)
    }

    /// 缓存启用的规则
    async fn cache_enabled_rules(&self, rules: &[RiskRuleRecord]) -> SigmaXResult<()> {
        let mut cache = self.rules_cache.write().await;
        
        cache.enabled_rules = rules.to_vec();
        cache.last_updated = Instant::now();
        
        Ok(())
    }

    /// 从缓存获取指定类型的规则
    async fn get_cached_rules_by_type(&self, rule_type: &str) -> SigmaXResult<Option<Vec<RiskRuleRecord>>> {
        let cache = self.rules_cache.read().await;
        
        if cache.last_updated.elapsed() < self.cache_config.rules_ttl {
            if let Some(rules) = cache.rules_by_type.get(rule_type) {
                return Ok(Some(rules.clone()));
            }
        }
        
        Ok(None)
    }

    /// 缓存指定类型的规则
    async fn cache_rules_by_type(&self, rule_type: &str, rules: &[RiskRuleRecord]) -> SigmaXResult<()> {
        let mut cache = self.rules_cache.write().await;
        
        cache.rules_by_type.insert(rule_type.to_string(), rules.to_vec());
        cache.last_updated = Instant::now();
        
        Ok(())
    }
}

impl Clone for RiskConfigManager {
    fn clone(&self) -> Self {
        Self {
            repository: Arc::clone(&self.repository),
            default_config: self.default_config.clone(),
            rules_cache: Arc::clone(&self.rules_cache),
            cache_config: self.cache_config.clone(),
        }
    }
}

impl RulesCache {
    fn new() -> Self {
        Self {
            rules_by_type: HashMap::new(),
            enabled_rules: Vec::new(),
            last_updated: Instant::now(),
        }
    }
}
