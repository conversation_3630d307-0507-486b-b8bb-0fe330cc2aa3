//! 服务注册和发现系统
//!
//! 提供服务生命周期管理、健康检查、依赖关系处理和优雅启停功能

use crate::{SigmaXResult, SigmaXError};
use async_trait::async_trait;
use std::{
    collections::HashMap,
    sync::Arc,
    time::{Duration, SystemTime, UNIX_EPOCH},
};
use tokio::sync::RwLock;
use serde::{Serialize, Deserialize};
use uuid::Uuid;

/// 服务ID类型
pub type ServiceId = Uuid;

/// 服务状态
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ServiceStatus {
    /// 未启动
    Stopped,
    /// 启动中
    Starting,
    /// 运行中
    Running,
    /// 停止中
    Stopping,
    /// 错误状态
    Error(String),
}

/// 服务健康状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceHealth {
    /// 服务ID
    pub service_id: ServiceId,
    /// 服务名称
    pub service_name: String,
    /// 健康状态
    pub is_healthy: bool,
    /// 状态消息
    pub message: String,
    /// 检查时间戳
    pub timestamp: u64,
    /// 响应时间（毫秒）
    pub response_time_ms: u64,
    /// 额外的健康指标
    pub metrics: HashMap<String, f64>,
}

impl ServiceHealth {
    /// 创建健康状态
    pub fn healthy(service_id: ServiceId, service_name: String, response_time_ms: u64) -> Self {
        Self {
            service_id,
            service_name,
            is_healthy: true,
            message: "Service is healthy".to_string(),
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
            response_time_ms,
            metrics: HashMap::new(),
        }
    }

    /// 创建不健康状态
    pub fn unhealthy(service_id: ServiceId, service_name: String, message: String) -> Self {
        Self {
            service_id,
            service_name,
            is_healthy: false,
            message,
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
            response_time_ms: 0,
            metrics: HashMap::new(),
        }
    }

    /// 添加指标
    pub fn with_metric(mut self, key: &str, value: f64) -> Self {
        self.metrics.insert(key.to_string(), value);
        self
    }
}

/// 服务信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceInfo {
    /// 服务ID
    pub id: ServiceId,
    /// 服务名称
    pub name: String,
    /// 服务类型
    pub service_type: String,
    /// 服务版本
    pub version: String,
    /// 服务状态
    pub status: ServiceStatus,
    /// 服务地址
    pub address: Option<String>,
    /// 服务端口
    pub port: Option<u16>,
    /// 依赖的服务
    pub dependencies: Vec<ServiceId>,
    /// 注册时间
    pub registered_at: u64,
    /// 最后健康检查时间
    pub last_health_check: Option<u64>,
    /// 元数据
    pub metadata: HashMap<String, String>,
}

impl ServiceInfo {
    /// 创建新的服务信息
    pub fn new(name: String, service_type: String, version: String) -> Self {
        Self {
            id: Uuid::new_v4(),
            name,
            service_type,
            version,
            status: ServiceStatus::Stopped,
            address: None,
            port: None,
            dependencies: Vec::new(),
            registered_at: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
            last_health_check: None,
            metadata: HashMap::new(),
        }
    }

    /// 设置服务地址
    pub fn with_address(mut self, address: String, port: u16) -> Self {
        self.address = Some(address);
        self.port = Some(port);
        self
    }

    /// 添加依赖
    pub fn with_dependency(mut self, dependency: ServiceId) -> Self {
        self.dependencies.push(dependency);
        self
    }

    /// 添加元数据
    pub fn with_metadata(mut self, key: String, value: String) -> Self {
        self.metadata.insert(key, value);
        self
    }
}

/// 服务trait
#[async_trait]
pub trait Service: Send + Sync {
    /// 获取服务名称
    fn service_name(&self) -> &str;

    /// 获取服务类型
    fn service_type(&self) -> &str {
        "generic"
    }

    /// 获取服务版本
    fn service_version(&self) -> &str {
        "1.0.0"
    }

    /// 启动服务
    async fn start(&self) -> SigmaXResult<()>;

    /// 停止服务
    async fn stop(&self) -> SigmaXResult<()>;

    /// 健康检查
    async fn health_check(&self) -> SigmaXResult<ServiceHealth>;

    /// 获取服务依赖
    fn dependencies(&self) -> Vec<String> {
        Vec::new()
    }

    /// 获取服务元数据
    fn metadata(&self) -> HashMap<String, String> {
        HashMap::new()
    }

    /// 服务是否正在运行
    async fn is_running(&self) -> bool {
        false
    }
}

/// 服务注册表
pub struct ServiceRegistry {
    /// 注册的服务
    services: Arc<RwLock<HashMap<ServiceId, Arc<dyn Service>>>>,
    /// 服务信息
    service_infos: Arc<RwLock<HashMap<ServiceId, ServiceInfo>>>,
    /// 服务名称到ID的映射
    name_to_id: Arc<RwLock<HashMap<String, ServiceId>>>,
}

impl ServiceRegistry {
    /// 创建新的服务注册表
    pub fn new() -> Self {
        Self {
            services: Arc::new(RwLock::new(HashMap::new())),
            service_infos: Arc::new(RwLock::new(HashMap::new())),
            name_to_id: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// 注册服务
    pub async fn register<S: Service + 'static>(&self, service: S) -> SigmaXResult<ServiceId> {
        let service_name = service.service_name().to_string();
        let service_type = service.service_type().to_string();
        let service_version = service.service_version().to_string();

        // 检查服务名称是否已存在
        {
            let name_map = self.name_to_id.read().await;
            if name_map.contains_key(&service_name) {
                return Err(SigmaXError::Internal(format!(
                    "服务名称 '{}' 已存在",
                    service_name
                )));
            }
        }

        let service_info = ServiceInfo::new(service_name.clone(), service_type, service_version);
        let service_id = service_info.id;

        let service_arc: Arc<dyn Service> = Arc::new(service);

        // 注册服务
        {
            let mut services = self.services.write().await;
            services.insert(service_id, Arc::clone(&service_arc));
        }

        // 保存服务信息
        {
            let mut infos = self.service_infos.write().await;
            infos.insert(service_id, service_info);
        }

        // 更新名称映射
        {
            let mut name_map = self.name_to_id.write().await;
            name_map.insert(service_name, service_id);
        }

        Ok(service_id)
    }

    /// 注销服务
    pub async fn unregister(&self, service_id: ServiceId) -> SigmaXResult<()> {
        let service_name = {
            let infos = self.service_infos.read().await;
            infos.get(&service_id)
                .map(|info| info.name.clone())
                .ok_or_else(|| SigmaXError::Internal(format!("服务 {} 不存在", service_id)))?
        };

        // 移除服务
        {
            let mut services = self.services.write().await;
            services.remove(&service_id);
        }

        // 移除服务信息
        {
            let mut infos = self.service_infos.write().await;
            infos.remove(&service_id);
        }

        // 移除名称映射
        {
            let mut name_map = self.name_to_id.write().await;
            name_map.remove(&service_name);
        }

        Ok(())
    }

    /// 根据ID获取服务
    pub async fn get_service(&self, service_id: ServiceId) -> SigmaXResult<Arc<dyn Service>> {
        let services = self.services.read().await;
        services.get(&service_id)
            .cloned()
            .ok_or_else(|| SigmaXError::Internal(format!("服务 {} 不存在", service_id)))
    }

    /// 根据名称获取服务
    pub async fn get_service_by_name(&self, name: &str) -> SigmaXResult<Arc<dyn Service>> {
        let service_id = {
            let name_map = self.name_to_id.read().await;
            name_map.get(name)
                .cloned()
                .ok_or_else(|| SigmaXError::Internal(format!("服务 '{}' 不存在", name)))?
        };

        self.get_service(service_id).await
    }

    /// 根据类型获取服务列表
    pub async fn get_services_by_type(&self, service_type: &str) -> SigmaXResult<Vec<Arc<dyn Service>>> {
        let mut result = Vec::new();

        let infos = self.service_infos.read().await;
        let services = self.services.read().await;

        for (service_id, info) in infos.iter() {
            if info.service_type == service_type {
                if let Some(service) = services.get(service_id) {
                    result.push(Arc::clone(service));
                }
            }
        }

        Ok(result)
    }

    /// 获取所有服务信息
    pub async fn get_all_service_infos(&self) -> Vec<ServiceInfo> {
        let infos = self.service_infos.read().await;
        infos.values().cloned().collect()
    }

    /// 获取服务信息
    pub async fn get_service_info(&self, service_id: ServiceId) -> SigmaXResult<ServiceInfo> {
        let infos = self.service_infos.read().await;
        infos.get(&service_id)
            .cloned()
            .ok_or_else(|| SigmaXError::Internal(format!("服务 {} 不存在", service_id)))
    }

    /// 更新服务状态
    pub async fn update_service_status(&self, service_id: ServiceId, status: ServiceStatus) -> SigmaXResult<()> {
        let mut infos = self.service_infos.write().await;
        if let Some(info) = infos.get_mut(&service_id) {
            info.status = status;
            Ok(())
        } else {
            Err(SigmaXError::Internal(format!("服务 {} 不存在", service_id)))
        }
    }

    /// 对所有服务进行健康检查
    pub async fn health_check_all(&self) -> SigmaXResult<Vec<ServiceHealth>> {
        let mut results = Vec::new();

        let services = self.services.read().await;
        for (service_id, service) in services.iter() {
            match service.health_check().await {
                Ok(health) => {
                    results.push(health);
                    // 更新最后健康检查时间
                    let mut infos = self.service_infos.write().await;
                    if let Some(info) = infos.get_mut(service_id) {
                        info.last_health_check = Some(
                            SystemTime::now()
                                .duration_since(UNIX_EPOCH)
                                .unwrap_or_default()
                                .as_secs()
                        );
                    }
                }
                Err(e) => {
                    let health = ServiceHealth::unhealthy(
                        *service_id,
                        service.service_name().to_string(),
                        format!("健康检查失败: {}", e)
                    );
                    results.push(health);
                }
            }
        }

        Ok(results)
    }

    /// 检查特定服务的健康状态
    pub async fn health_check_service(&self, service_id: ServiceId) -> SigmaXResult<ServiceHealth> {
        let service = self.get_service(service_id).await?;
        service.health_check().await
    }

    /// 获取服务数量
    pub async fn service_count(&self) -> usize {
        let services = self.services.read().await;
        services.len()
    }
}

impl Default for ServiceRegistry {
    fn default() -> Self {
        Self::new()
    }
}

/// 服务管理器
pub struct ServiceManager {
    /// 服务注册表
    registry: Arc<ServiceRegistry>,
    /// 启动顺序
    startup_order: Vec<ServiceId>,
    /// 健康检查间隔
    health_check_interval: Duration,
    /// 是否正在运行
    running: Arc<RwLock<bool>>,
}

impl ServiceManager {
    /// 创建新的服务管理器
    pub fn new(registry: Arc<ServiceRegistry>) -> Self {
        Self {
            registry,
            startup_order: Vec::new(),
            health_check_interval: Duration::from_secs(30),
            running: Arc::new(RwLock::new(false)),
        }
    }

    /// 设置健康检查间隔
    pub fn with_health_check_interval(mut self, interval: Duration) -> Self {
        self.health_check_interval = interval;
        self
    }

    /// 设置启动顺序
    pub fn with_startup_order(mut self, order: Vec<ServiceId>) -> Self {
        self.startup_order = order;
        self
    }

    /// 启动所有服务
    pub async fn start_all(&self) -> SigmaXResult<()> {
        let mut running = self.running.write().await;
        if *running {
            return Err(SigmaXError::Internal("服务管理器已经在运行".to_string()));
        }

        // 按启动顺序启动服务
        if !self.startup_order.is_empty() {
            for service_id in &self.startup_order {
                self.start_service_internal(*service_id).await?;
            }
        } else {
            // 如果没有指定启动顺序，启动所有服务
            let service_infos = self.registry.get_all_service_infos().await;
            for info in service_infos {
                self.start_service_internal(info.id).await?;
            }
        }

        // 启动健康检查循环
        self.start_health_check_loop().await;

        *running = true;
        Ok(())
    }

    /// 停止所有服务
    pub async fn stop_all(&self) -> SigmaXResult<()> {
        let mut running = self.running.write().await;
        if !*running {
            return Ok(());
        }

        // 按相反顺序停止服务
        let mut stop_order = self.startup_order.clone();
        stop_order.reverse();

        if !stop_order.is_empty() {
            for service_id in &stop_order {
                self.stop_service_internal(*service_id).await?;
            }
        } else {
            // 如果没有指定启动顺序，停止所有服务
            let service_infos = self.registry.get_all_service_infos().await;
            for info in service_infos {
                self.stop_service_internal(info.id).await?;
            }
        }

        *running = false;
        Ok(())
    }

    /// 重启服务
    pub async fn restart_service(&self, service_id: ServiceId) -> SigmaXResult<()> {
        self.stop_service_internal(service_id).await?;
        tokio::time::sleep(Duration::from_millis(100)).await; // 短暂等待
        self.start_service_internal(service_id).await?;
        Ok(())
    }

    /// 启动特定服务
    pub async fn start_service(&self, service_id: ServiceId) -> SigmaXResult<()> {
        self.start_service_internal(service_id).await
    }

    /// 停止特定服务
    pub async fn stop_service(&self, service_id: ServiceId) -> SigmaXResult<()> {
        self.stop_service_internal(service_id).await
    }

    /// 内部启动服务方法
    async fn start_service_internal(&self, service_id: ServiceId) -> SigmaXResult<()> {
        // 更新状态为启动中
        self.registry.update_service_status(service_id, ServiceStatus::Starting).await?;

        let service = self.registry.get_service(service_id).await?;

        match service.start().await {
            Ok(_) => {
                self.registry.update_service_status(service_id, ServiceStatus::Running).await?;
                tracing::info!("服务 '{}' 启动成功", service.service_name());
                Ok(())
            }
            Err(e) => {
                let error_msg = format!("启动失败: {}", e);
                self.registry.update_service_status(
                    service_id,
                    ServiceStatus::Error(error_msg.clone())
                ).await?;
                tracing::error!("服务 '{}' 启动失败: {}", service.service_name(), e);
                Err(e)
            }
        }
    }

    /// 内部停止服务方法
    async fn stop_service_internal(&self, service_id: ServiceId) -> SigmaXResult<()> {
        // 更新状态为停止中
        self.registry.update_service_status(service_id, ServiceStatus::Stopping).await?;

        let service = self.registry.get_service(service_id).await?;

        match service.stop().await {
            Ok(_) => {
                self.registry.update_service_status(service_id, ServiceStatus::Stopped).await?;
                tracing::info!("服务 '{}' 停止成功", service.service_name());
                Ok(())
            }
            Err(e) => {
                let error_msg = format!("停止失败: {}", e);
                self.registry.update_service_status(
                    service_id,
                    ServiceStatus::Error(error_msg.clone())
                ).await?;
                tracing::error!("服务 '{}' 停止失败: {}", service.service_name(), e);
                Err(e)
            }
        }
    }

    /// 启动健康检查循环
    async fn start_health_check_loop(&self) {
        let registry = Arc::clone(&self.registry);
        let interval = self.health_check_interval;
        let running = Arc::clone(&self.running);

        tokio::spawn(async move {
            let mut interval_timer = tokio::time::interval(interval);

            loop {
                interval_timer.tick().await;

                // 检查是否还在运行
                {
                    let is_running = running.read().await;
                    if !*is_running {
                        break;
                    }
                }

                // 执行健康检查
                match registry.health_check_all().await {
                    Ok(health_results) => {
                        for health in health_results {
                            if !health.is_healthy {
                                tracing::warn!(
                                    "服务 '{}' 健康检查失败: {}",
                                    health.service_name,
                                    health.message
                                );
                            }
                        }
                    }
                    Err(e) => {
                        tracing::error!("健康检查失败: {}", e);
                    }
                }
            }
        });
    }

    /// 获取服务注册表
    pub fn registry(&self) -> &Arc<ServiceRegistry> {
        &self.registry
    }

    /// 检查管理器是否正在运行
    pub async fn is_running(&self) -> bool {
        *self.running.read().await
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::sync::atomic::{AtomicBool, AtomicU64, Ordering};
    use tokio::time::Duration as TokioDuration;

    // 测试用的模拟服务
    struct MockService {
        name: String,
        service_type: String,
        version: String,
        running: Arc<AtomicBool>,
        start_count: Arc<AtomicU64>,
        stop_count: Arc<AtomicU64>,
        should_fail_start: bool,
        should_fail_health_check: bool,
    }

    impl MockService {
        fn new(name: &str) -> Self {
            Self {
                name: name.to_string(),
                service_type: "mock".to_string(),
                version: "1.0.0".to_string(),
                running: Arc::new(AtomicBool::new(false)),
                start_count: Arc::new(AtomicU64::new(0)),
                stop_count: Arc::new(AtomicU64::new(0)),
                should_fail_start: false,
                should_fail_health_check: false,
            }
        }

        fn with_failure_mode(mut self, fail_start: bool, fail_health: bool) -> Self {
            self.should_fail_start = fail_start;
            self.should_fail_health_check = fail_health;
            self
        }

        fn start_count(&self) -> u64 {
            self.start_count.load(Ordering::SeqCst)
        }

        fn stop_count(&self) -> u64 {
            self.stop_count.load(Ordering::SeqCst)
        }
    }

    #[async_trait]
    impl Service for MockService {
        fn service_name(&self) -> &str {
            &self.name
        }

        fn service_type(&self) -> &str {
            &self.service_type
        }

        fn service_version(&self) -> &str {
            &self.version
        }

        async fn start(&self) -> SigmaXResult<()> {
            self.start_count.fetch_add(1, Ordering::SeqCst);

            if self.should_fail_start {
                return Err(SigmaXError::Internal("模拟启动失败".to_string()));
            }

            self.running.store(true, Ordering::SeqCst);
            Ok(())
        }

        async fn stop(&self) -> SigmaXResult<()> {
            self.stop_count.fetch_add(1, Ordering::SeqCst);
            self.running.store(false, Ordering::SeqCst);
            Ok(())
        }

        async fn health_check(&self) -> SigmaXResult<ServiceHealth> {
            if self.should_fail_health_check {
                return Err(SigmaXError::Internal("模拟健康检查失败".to_string()));
            }

            let service_id = Uuid::new_v4(); // 在实际使用中，这应该是固定的ID
            if self.running.load(Ordering::SeqCst) {
                Ok(ServiceHealth::healthy(service_id, self.name.clone(), 10))
            } else {
                Ok(ServiceHealth::unhealthy(
                    service_id,
                    self.name.clone(),
                    "服务未运行".to_string()
                ))
            }
        }

        async fn is_running(&self) -> bool {
            self.running.load(Ordering::SeqCst)
        }
    }

    #[tokio::test]
    async fn test_service_registry_creation() {
        let registry = ServiceRegistry::new();
        assert_eq!(registry.service_count().await, 0);
    }

    #[tokio::test]
    async fn test_service_registration() {
        let registry = ServiceRegistry::new();
        let service = MockService::new("test-service");

        let service_id = registry.register(service).await.unwrap();
        assert_eq!(registry.service_count().await, 1);

        let service_info = registry.get_service_info(service_id).await.unwrap();
        assert_eq!(service_info.name, "test-service");
        assert_eq!(service_info.service_type, "mock");
        assert_eq!(service_info.version, "1.0.0");
    }

    #[tokio::test]
    async fn test_service_registration_duplicate_name() {
        let registry = ServiceRegistry::new();
        let service1 = MockService::new("duplicate-service");
        let service2 = MockService::new("duplicate-service");

        registry.register(service1).await.unwrap();
        let result = registry.register(service2).await;

        assert!(result.is_err());
        assert_eq!(registry.service_count().await, 1);
    }

    #[tokio::test]
    async fn test_service_unregistration() {
        let registry = ServiceRegistry::new();
        let service = MockService::new("test-service");

        let service_id = registry.register(service).await.unwrap();
        assert_eq!(registry.service_count().await, 1);

        registry.unregister(service_id).await.unwrap();
        assert_eq!(registry.service_count().await, 0);

        let result = registry.get_service(service_id).await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_service_retrieval_by_name() {
        let registry = ServiceRegistry::new();
        let service = MockService::new("named-service");

        registry.register(service).await.unwrap();

        let retrieved_service = registry.get_service_by_name("named-service").await.unwrap();
        assert_eq!(retrieved_service.service_name(), "named-service");

        let result = registry.get_service_by_name("non-existent").await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_services_by_type() {
        let registry = ServiceRegistry::new();
        let service1 = MockService::new("service1");
        let service2 = MockService::new("service2");

        registry.register(service1).await.unwrap();
        registry.register(service2).await.unwrap();

        let mock_services = registry.get_services_by_type("mock").await.unwrap();
        assert_eq!(mock_services.len(), 2);

        let other_services = registry.get_services_by_type("other").await.unwrap();
        assert_eq!(other_services.len(), 0);
    }

    #[tokio::test]
    async fn test_health_check_all() {
        let registry = Arc::new(ServiceRegistry::new());
        let healthy_service = MockService::new("healthy-service");
        let unhealthy_service = MockService::new("unhealthy-service")
            .with_failure_mode(false, true);

        let healthy_id = registry.register(healthy_service).await.unwrap();
        let _unhealthy_id = registry.register(unhealthy_service).await.unwrap();

        // 启动健康的服务，这样它的健康检查才会返回健康状态
        let manager = ServiceManager::new(Arc::clone(&registry));
        manager.start_service(healthy_id).await.unwrap();

        let health_results = registry.health_check_all().await.unwrap();
        assert_eq!(health_results.len(), 2);

        // 检查健康状态
        let healthy_count = health_results.iter().filter(|h| h.is_healthy).count();
        let unhealthy_count = health_results.iter().filter(|h| !h.is_healthy).count();

        assert_eq!(healthy_count, 1);
        assert_eq!(unhealthy_count, 1);

        // 清理：停止服务
        manager.stop_service(healthy_id).await.unwrap();
    }

    #[tokio::test]
    async fn test_service_manager_creation() {
        let registry = Arc::new(ServiceRegistry::new());
        let manager = ServiceManager::new(registry);

        assert!(!manager.is_running().await);
    }

    #[tokio::test]
    async fn test_service_manager_start_stop() {
        let registry = Arc::new(ServiceRegistry::new());
        let service = MockService::new("managed-service");
        let service_id = registry.register(service).await.unwrap();

        let manager = ServiceManager::new(registry);

        // 启动服务
        manager.start_service(service_id).await.unwrap();
        let service_info = manager.registry().get_service_info(service_id).await.unwrap();
        assert_eq!(service_info.status, ServiceStatus::Running);

        // 停止服务
        manager.stop_service(service_id).await.unwrap();
        let service_info = manager.registry().get_service_info(service_id).await.unwrap();
        assert_eq!(service_info.status, ServiceStatus::Stopped);
    }

    #[tokio::test]
    async fn test_service_manager_restart() {
        let registry = Arc::new(ServiceRegistry::new());
        let service = MockService::new("restart-service");
        let service_id = registry.register(service).await.unwrap();

        let manager = ServiceManager::new(registry);

        // 启动服务
        manager.start_service(service_id).await.unwrap();

        // 重启服务
        manager.restart_service(service_id).await.unwrap();

        let service_info = manager.registry().get_service_info(service_id).await.unwrap();
        assert_eq!(service_info.status, ServiceStatus::Running);
    }

    #[tokio::test]
    async fn test_service_start_failure() {
        let registry = Arc::new(ServiceRegistry::new());
        let failing_service = MockService::new("failing-service")
            .with_failure_mode(true, false);
        let service_id = registry.register(failing_service).await.unwrap();

        let manager = ServiceManager::new(registry);

        let result = manager.start_service(service_id).await;
        assert!(result.is_err());

        let service_info = manager.registry().get_service_info(service_id).await.unwrap();
        assert!(matches!(service_info.status, ServiceStatus::Error(_)));
    }

    #[tokio::test]
    async fn test_service_health_metrics() {
        let service_id = Uuid::new_v4();
        let health = ServiceHealth::healthy(service_id, "test-service".to_string(), 50)
            .with_metric("cpu_usage", 75.5)
            .with_metric("memory_usage", 60.2);

        assert!(health.is_healthy);
        assert_eq!(health.response_time_ms, 50);
        assert_eq!(health.metrics.get("cpu_usage"), Some(&75.5));
        assert_eq!(health.metrics.get("memory_usage"), Some(&60.2));
    }

    #[tokio::test]
    async fn test_service_info_builder() {
        let info = ServiceInfo::new("test-service".to_string(), "test".to_string(), "1.0.0".to_string())
            .with_address("localhost".to_string(), 8080)
            .with_dependency(Uuid::new_v4())
            .with_metadata("env".to_string(), "test".to_string());

        assert_eq!(info.name, "test-service");
        assert_eq!(info.address, Some("localhost".to_string()));
        assert_eq!(info.port, Some(8080));
        assert_eq!(info.dependencies.len(), 1);
        assert_eq!(info.metadata.get("env"), Some(&"test".to_string()));
    }

    #[tokio::test]
    async fn test_service_manager_with_startup_order() {
        let registry = Arc::new(ServiceRegistry::new());

        let service1 = MockService::new("service1");
        let service2 = MockService::new("service2");
        let service3 = MockService::new("service3");

        let id1 = registry.register(service1).await.unwrap();
        let id2 = registry.register(service2).await.unwrap();
        let id3 = registry.register(service3).await.unwrap();

        let startup_order = vec![id1, id2, id3];
        let manager = ServiceManager::new(registry)
            .with_startup_order(startup_order)
            .with_health_check_interval(TokioDuration::from_millis(100));

        manager.start_all().await.unwrap();

        // 验证所有服务都已启动
        for &service_id in &[id1, id2, id3] {
            let service_info = manager.registry().get_service_info(service_id).await.unwrap();
            assert_eq!(service_info.status, ServiceStatus::Running);
        }

        manager.stop_all().await.unwrap();

        // 验证所有服务都已停止
        for &service_id in &[id1, id2, id3] {
            let service_info = manager.registry().get_service_info(service_id).await.unwrap();
            assert_eq!(service_info.status, ServiceStatus::Stopped);
        }
    }
}
