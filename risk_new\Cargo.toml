[package]
name = "sigmax-risk-new"
version = "2.0.0"
edition = "2021"
description = "SigmaX-R 重构后的风险管理模块"
authors = ["SigmaX Team"]

[dependencies]
# 核心依赖
async-trait = "0.1"
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
uuid = { version = "1.0", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }
rust_decimal = { version = "1.0", features = ["serde"] }
tracing = "0.1"

# 数据库
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "postgres", "uuid", "chrono", "rust_decimal"], optional = true }

# 配置
toml = "0.8"

# 内部依赖
sigmax-core = { path = "../core" }

[dev-dependencies]
tokio-test = "0.4"
tracing-subscriber = "0.3"

[features]
default = []
test-utils = []
database = ["sqlx"]
