#!/usr/bin/env python3
"""
报告生成器 - 生成验证报告
支持多种格式的报告生成：JSON、HTML、CSV等
"""

import json
import csv
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging

logger = logging.getLogger(__name__)

class ReportGenerator:
    """验证报告生成器"""
    
    def __init__(self, output_dir: str = "reports"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def generate_json_report(self, data: Dict[str, Any], filename: Optional[str] = None) -> str:
        """生成JSON格式报告"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"validation_report_{timestamp}.json"
        
        filepath = self.output_dir / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"JSON报告已生成: {filepath}")
        return str(filepath)
    
    def generate_html_report(self, data: Dict[str, Any], filename: Optional[str] = None) -> str:
        """生成HTML格式报告"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"validation_report_{timestamp}.html"
        
        filepath = self.output_dir / filename
        
        html_content = self._create_html_content(data)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info(f"HTML报告已生成: {filepath}")
        return str(filepath)
    
    def _create_html_content(self, data: Dict[str, Any]) -> str:
        """创建HTML报告内容"""
        summary = data.get("validation_summary", {})
        
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>SigmaX API 验证报告</title>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background: #f0f0f0; padding: 20px; border-radius: 5px; }}
                .summary {{ margin: 20px 0; }}
                .test-result {{ margin: 10px 0; padding: 10px; border: 1px solid #ddd; }}
                .pass {{ background: #d4edda; }}
                .fail {{ background: #f8d7da; }}
                .error {{ background: #fff3cd; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>SigmaX API 验证报告</h1>
                <p>生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>
            </div>
            
            <div class="summary">
                <h2>验证摘要</h2>
                <p>总测试数: {summary.get('total_tests', 0)}</p>
                <p>通过: {summary.get('passed_tests', 0)}</p>
                <p>失败: {summary.get('failed_tests', 0)}</p>
                <p>成功率: {summary.get('success_rate', 0):.1f}%</p>
                <p>总耗时: {summary.get('total_duration', 0):.2f}秒</p>
            </div>
            
            <div class="details">
                <h2>详细结果</h2>
                <!-- 这里可以添加更多详细信息 -->
            </div>
        </body>
        </html>
        """
        
        return html
    
    def generate_csv_report(self, data: Dict[str, Any], filename: Optional[str] = None) -> str:
        """生成CSV格式报告"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"validation_report_{timestamp}.csv"
        
        filepath = self.output_dir / filename
        
        # 提取测试结果数据
        test_results = []
        for level, result in data.get("level_results", {}).items():
            if isinstance(result, dict) and "test_results" in result:
                for test in result["test_results"]:
                    test_results.append({
                        "level": level,
                        "test_name": test.get("test_name", ""),
                        "success": test.get("success", False),
                        "response_time": test.get("response_time", 0),
                        "error": test.get("error", "")
                    })
        
        # 写入CSV文件
        if test_results:
            with open(filepath, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=test_results[0].keys())
                writer.writeheader()
                writer.writerows(test_results)
        
        logger.info(f"CSV报告已生成: {filepath}")
        return str(filepath)
