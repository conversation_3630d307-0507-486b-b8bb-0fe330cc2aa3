//! Repository Traits
//!
//! 定义所有数据仓储的接口规范

pub mod order_repository;
pub mod trade_repository;
pub mod strategy_repository;
pub mod event_repository;
pub mod enhanced_order_repository;
pub mod risk_repository;
pub mod system_config_repository;
pub mod notification_repository;
pub mod api_repository;
pub mod trading_repository;
pub mod monitoring_repository;
pub mod cache_repository;

// 重新导出所有 trait
pub use order_repository::OrderRepository;
pub use trade_repository::TradeRepository;
pub use strategy_repository::StrategyRepository;
pub use event_repository::EventRepository;
pub use enhanced_order_repository::{
    EnhancedOrderRepository, OrderQueryFilter, Pagination, OrderSort,
    OrderSortBy, SortDirection, OrderStatistics, OrderRepositoryBuilder
};
pub use risk_repository::{
    RiskRepository, RiskCheckRecord, RiskRuleRecord,
    RiskViolationRecord, RiskQueryFilter, RiskRuleFilter
};
pub use system_config_repository::{
    SystemConfigRepository, ConfigStatistics
};
pub use notification_repository::{
    NotificationRepository, NotificationRecord, NotificationStatus
};
pub use api_repository::{
    ApiRepository, ApiRequestRecord, ApiRateLimitRecord, ApiRequestStats
};
pub use trading_repository::TradingRepository;
pub use monitoring_repository::{
    MonitoringRepository, MonitoringConfigRecord, MonitoringConfigStatistics,
    MonitoringMetricsRecord, AlertRecord, AlertStatus
};
pub use cache_repository::{
    CacheRepository, CacheStatsRecord, CachePerformanceRecord,
    CacheHealthRecord, CacheAlertRecord
};
