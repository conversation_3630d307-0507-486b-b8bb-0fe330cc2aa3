核心设计原则 (Core Design Principles)
高内聚，低耦合 (High Cohesion, Low Coupling)
关注点分离 (Separation of Concerns, SoC)
面向接口设计 (Design to an Interface)
可测试性设计 (Design for Testability)
简洁与可演化性 (Simplicity and Evolvability)

# SigmaX风控模块架构设计

架构设计，开始基于项目对架构设计进行评审与分析，并对方案评分

graph TB
    subgraph "🏗️ engines模块 - 引擎管理层"
        subgraph "📊 backtest子模块 - 回测引擎"
            BacktestEngine[BacktestEngine<br/>• 回测引擎核心<br/>• 历史数据处理<br/>• 批量订单执行<br/>• 性能统计分析]
            
            BacktestAdapter[BacktestRiskAdapter<br/>• 纯风控适配逻辑<br/>• 高吞吐量优化<br/>• 实现EngineRiskAdapter<br/>• 实现RiskManager trait]
            
            BacktestConfig[BacktestConfig<br/>• 回测参数配置<br/>• 数据源设置<br/>• 性能调优参数<br/>• 不可变配置]
        end
        
        subgraph "⚡ live子模块 - 实盘引擎"
            LiveEngine[LiveTradingEngine<br/>• 实盘引擎核心<br/>• 实时数据处理<br/>• 订单执行管理<br/>• 实时风险监控]
            
            LiveAdapter[LiveTradingRiskAdapter<br/>• 纯风控适配逻辑<br/>• 低延迟优化<br/>• 实现EngineRiskAdapter<br/>• 实现RiskManager trait]
            
            LiveConfig[LiveTradingConfig<br/>• 实盘交易配置<br/>• 风险参数设置<br/>• 延迟优化参数<br/>• 热更新支持]
        end
        
        subgraph "🛡️ risk子模块 - 风控集成层"
            RiskRouter[RiskRouter<br/>• 纯路由分发逻辑<br/>• 引擎类型路由<br/>• 无业务逻辑<br/>• 轻量级设计]
            
            AdapterFactory[AdapterFactory<br/>• 适配器工厂<br/>• 依赖注入管理<br/>• 配置驱动创建<br/>• 生命周期管理]
            
            RiskContainer[RiskServiceContainer<br/>• 依赖注入容器<br/>• 服务生命周期<br/>• 配置管理<br/>• 测试支持]
        end
        
        subgraph "🔧 base子模块 - 基础组件"
            BaseEngine[BaseEngine<br/>• 引擎基础辅助<br/>• 状态管理<br/>• 生命周期控制<br/>• 单策略模式]
            
            EngineFactory[EngineFactory<br/>• 引擎工厂<br/>• 配置注入<br/>• 依赖管理<br/>• 类型安全创建]
        end
    end
    
    subgraph "🧠 core模块 - 接口抽象层"
        subgraph "🔌 风控接口"
            RiskEngineInterface[RiskEngine trait<br/>• check_order_risk<br/>• check_position_risk<br/>• reload_rules<br/>• 核心业务抽象]
            
            EngineRiskInterface[EngineRiskAdapter trait<br/>• check_order_risk<br/>• check_position_risk<br/>• engine_type<br/>• 适配器抽象]
            
            RiskManagerInterface[RiskManager trait<br/>• check_order_risk<br/>• check_position_risk<br/>• get_max_order_size<br/>• 向后兼容接口]
        end
        
        subgraph "🔌 服务接口"
            CacheInterface[CacheService trait<br/>• get<T><br/>• set<T><br/>• invalidate<br/>• 缓存抽象]
            
            MetricsInterface[MetricsCollector trait<br/>• record_risk_check<br/>• record_cache_hit<br/>• record_latency<br/>• 指标抽象]
            
            ConfigInterface[ConfigService trait<br/>• get_config<br/>• reload_config<br/>• validate_config<br/>• 配置抽象]
        end
        
        subgraph "📋 基础类型"
            CoreTypes[Core Types<br/>• Order, Balance<br/>• TradingPair<br/>• RiskCheckResult<br/>• 基础数据类型]
        end
    end
    
    subgraph "🔌 services模块 - 横切关注点服务层"
        subgraph "💾 缓存服务"
            RedisCacheService[RedisCacheService<br/>• Redis缓存实现<br/>• 分布式缓存<br/>• TTL管理<br/>• 实现CacheService trait]
            
            MemoryCacheService[MemoryCacheService<br/>• 内存缓存实现<br/>• 高性能缓存<br/>• LRU策略<br/>• 实现CacheService trait]
            
            CacheStrategy[CacheStrategy<br/>• 缓存策略选择<br/>• 智能路由<br/>• 性能优化<br/>• 策略模式]
        end
        
        subgraph "📈 指标服务"
            PrometheusMetrics[PrometheusMetrics<br/>• Prometheus指标<br/>• 性能监控<br/>• 业务指标<br/>• 实现MetricsCollector trait]
            
            LogMetrics[LogMetrics<br/>• 日志指标<br/>• 结构化日志<br/>• 审计追踪<br/>• 实现MetricsCollector trait]
            
            MetricsAggregator[MetricsAggregator<br/>• 指标聚合<br/>• 多收集器支持<br/>• 观察者模式<br/>• 组合模式]
        end
        
        subgraph "⚙️ 配置服务"
            DatabaseConfigService[DatabaseConfigService<br/>• 数据库配置<br/>• 动态配置<br/>• 热更新<br/>• 实现ConfigService trait]
            
            FileConfigService[FileConfigService<br/>• 文件配置<br/>• 静态配置<br/>• 版本管理<br/>• 实现ConfigService trait]
        end
    end
    
    subgraph "🛡️ risk模块 - 核心风控引擎"
        UnifiedEngine[UnifiedRiskEngine<br/>• 核心风控引擎<br/>• 规则执行引擎<br/>• 决策逻辑<br/>• 实现RiskEngine trait]
        
        RuleEngine[RuleEngine<br/>• 规则管理<br/>• 条件评估<br/>• 优先级排序<br/>• 热更新支持]
        
        RiskRepository[RiskRepository<br/>• 数据访问层<br/>• 规则存储<br/>• 执行历史<br/>• 事务管理]
    end
    
    subgraph "🌐 web模块 - API层"
        WebRiskAdapter[WebApiRiskAdapter<br/>• Web API适配器<br/>• 详细结果返回<br/>• 完整信息展示<br/>• RESTful接口]
        
        RiskHandlers[Risk Handlers<br/>• HTTP处理器<br/>• 请求验证<br/>• 响应格式化<br/>• 错误处理]
    end
    
    subgraph "🎯 strategies模块 - 策略层"
        StrategyRiskAdapter[StrategyRiskAdapter<br/>• 策略专用风控<br/>• 切换风险评估<br/>• 性能分析<br/>• 风险建议]
        
        StrategySwitchManager[StrategySwitchManager<br/>• 策略切换管理<br/>• 风险评估<br/>• 切换决策<br/>• 状态管理]
    end
    
    subgraph "💾 数据存储层"
        Database[(PostgreSQL<br/>unified_risk_rules<br/>risk_rule_executions<br/>system_config<br/>audit_logs)]
        
        RedisCache[(Redis Cluster<br/>热数据缓存<br/>会话存储<br/>分布式锁<br/>消息队列)]
    end
    
    subgraph "🧪 测试支持层"
        MockServices[Mock Services<br/>• MockRiskEngine<br/>• MockCacheService<br/>• MockMetricsCollector<br/>• 测试专用实现]
        
        TestContainer[TestContainer<br/>• 测试依赖注入<br/>• Mock对象管理<br/>• 测试配置<br/>• 隔离环境]
    end
    
    %% 引擎内部连接
    BacktestEngine --> BacktestAdapter
    LiveEngine --> LiveAdapter
    
    BacktestAdapter --> RiskRouter
    LiveAdapter --> RiskRouter
    
    RiskRouter --> AdapterFactory
    AdapterFactory --> RiskContainer
    
    BaseEngine --> EngineFactory
    EngineFactory --> RiskContainer
    
    %% 接口实现关系 (虚线表示实现)
    BacktestAdapter -.-> EngineRiskInterface
    LiveAdapter -.-> EngineRiskInterface
    WebRiskAdapter -.-> EngineRiskInterface
    StrategyRiskAdapter -.-> EngineRiskInterface
    
    BacktestAdapter -.-> RiskManagerInterface
    LiveAdapter -.-> RiskManagerInterface
    
    UnifiedEngine -.-> RiskEngineInterface
    
    RedisCacheService -.-> CacheInterface
    MemoryCacheService -.-> CacheInterface
    
    PrometheusMetrics -.-> MetricsInterface
    LogMetrics -.-> MetricsInterface
    
    DatabaseConfigService -.-> ConfigInterface
    FileConfigService -.-> ConfigInterface
    
    %% 依赖注入关系 (点线表示依赖接口)
    BacktestAdapter -.-> RiskEngineInterface
    LiveAdapter -.-> RiskEngineInterface
    WebRiskAdapter -.-> RiskEngineInterface
    StrategyRiskAdapter -.-> RiskEngineInterface
    
    BacktestAdapter -.-> CacheInterface
    LiveAdapter -.-> CacheInterface
    
    RiskRouter -.-> MetricsInterface
    BacktestAdapter -.-> MetricsInterface
    LiveAdapter -.-> MetricsInterface
    
    RiskContainer -.-> ConfigInterface
    
    %% 服务层连接
    CacheStrategy --> RedisCacheService
    CacheStrategy --> MemoryCacheService
    
    MetricsAggregator --> PrometheusMetrics
    MetricsAggregator --> LogMetrics
    
    %% 核心引擎连接
    UnifiedEngine --> RuleEngine
    UnifiedEngine --> RiskRepository
    
    %% 数据层连接
    RiskRepository --> Database
    RedisCacheService --> RedisCache
    DatabaseConfigService --> Database
    
    %% Web层连接
    WebRiskAdapter --> RiskRouter
    RiskHandlers --> WebRiskAdapter
    
    %% 策略层连接
    StrategyRiskAdapter --> RiskRouter
    StrategySwitchManager --> StrategyRiskAdapter
    
    %% 测试支持连接
    TestContainer -.-> MockServices
    MockServices -.-> RiskEngineInterface
    MockServices -.-> CacheInterface
    MockServices -.-> MetricsInterface
    
    %% 配置连接
    BacktestConfig -.-> ConfigInterface
    LiveConfig -.-> ConfigInterface
    
    %% 样式定义 - 核心组件
    style BacktestEngine fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    style LiveEngine fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    style RiskRouter fill:#fff3e0,stroke:#e65100,stroke-width:2px
    style UnifiedEngine fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    style RiskContainer fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    
    %% 样式定义 - 接口层
    style RiskEngineInterface fill:#e0f2f1,stroke:#004d40,stroke-width:2px
    style EngineRiskInterface fill:#e8eaf6,stroke:#3f51b5,stroke-width:2px
    style CacheInterface fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    style MetricsInterface fill:#fef7ff,stroke:#4a148c,stroke-width:2px
    
    %% 样式定义 - 适配器
    style BacktestAdapter fill:#e1f5fe,stroke:#0277bd,stroke-width:1px
    style LiveAdapter fill:#e8f5e8,stroke:#388e3c,stroke-width:1px
    style WebRiskAdapter fill:#fff8e1,stroke:#f57c00,stroke-width:1px
    style StrategyRiskAdapter fill:#f1f8e9,stroke:#689f38,stroke-width:1px
    
    %% 样式定义 - 服务层
    style RedisCacheService fill:#ffebee,stroke:#c62828,stroke-width:1px
    style PrometheusMetrics fill:#e3f2fd,stroke:#1565c0,stroke-width:1px
    style DatabaseConfigService fill:#f3e5f5,stroke:#6a1b9a,stroke-width:1px
    
    %% 样式定义 - 测试支持
    style MockServices fill:#c8e6c9,stroke:#2e7d32,stroke-width:1px
    style TestContainer fill:#c8e6c9,stroke:#388e3c,stroke-width:1px

// ----------------------------------------------------------------------------

graph TB
    subgraph "📋 依赖关系层次"
        subgraph "🔝 应用层 - Application Layer"
            WebApp[Web Application<br/>• REST API<br/>• WebSocket<br/>• 用户界面]
            
            EngineApp[Engine Application<br/>• 回测应用<br/>• 实盘应用<br/>• 策略应用]
        end
        
        subgraph "🎯 适配器层 - Adapter Layer"
            WebAdapter[WebApiRiskAdapter<br/>依赖: RiskEngine trait<br/>依赖: MetricsCollector trait<br/>实现: EngineRiskAdapter trait]
            
            BacktestAdapter[BacktestRiskAdapter<br/>依赖: RiskEngine trait<br/>依赖: CacheService trait<br/>依赖: MetricsCollector trait<br/>实现: EngineRiskAdapter trait<br/>实现: RiskManager trait]
            
            LiveAdapter[LiveTradingRiskAdapter<br/>依赖: RiskEngine trait<br/>依赖: CacheService trait<br/>依赖: MetricsCollector trait<br/>实现: EngineRiskAdapter trait<br/>实现: RiskManager trait]
            
            StrategyAdapter[StrategyRiskAdapter<br/>依赖: RiskEngine trait<br/>依赖: MetricsCollector trait<br/>实现: EngineRiskAdapter trait]
        end
        
        subgraph "🏗️ 服务层 - Service Layer"
            RiskRouter[RiskRouter<br/>依赖: EngineRiskAdapter trait<br/>依赖: MetricsCollector trait<br/>职责: 路由分发]
            
            AdapterFactory[AdapterFactory<br/>依赖: RiskServiceContainer<br/>依赖: ConfigService trait<br/>职责: 适配器创建]
            
            RiskContainer[RiskServiceContainer<br/>依赖: RiskEngine trait<br/>依赖: CacheService trait<br/>依赖: MetricsCollector trait<br/>依赖: ConfigService trait<br/>职责: 依赖注入]
        end
        
        subgraph "🔌 基础设施层 - Infrastructure Layer"
            CacheServices[Cache Services<br/>• RedisCacheService<br/>• MemoryCacheService<br/>• CacheStrategy<br/>实现: CacheService trait]
            
            MetricsServices[Metrics Services<br/>• PrometheusMetrics<br/>• LogMetrics<br/>• MetricsAggregator<br/>实现: MetricsCollector trait]
            
            ConfigServices[Config Services<br/>• DatabaseConfigService<br/>• FileConfigService<br/>实现: ConfigService trait]
        end
        
        subgraph "🧠 领域层 - Domain Layer"
            RiskEngine[UnifiedRiskEngine<br/>依赖: RuleEngine<br/>依赖: RiskRepository<br/>实现: RiskEngine trait<br/>职责: 核心业务逻辑]
            
            RuleEngine[RuleEngine<br/>依赖: RiskRepository<br/>职责: 规则管理]
        end
        
        subgraph "💾 数据层 - Data Layer"
            RiskRepository[RiskRepository<br/>依赖: Database<br/>职责: 数据访问]
            
            Database[(PostgreSQL<br/>Redis Cluster)]
        end
    end
    
    subgraph "🔌 接口契约定义"
        subgraph "核心业务接口"
            IRiskEngine[RiskEngine trait<br/>+ check_order_risk<br/>+ check_position_risk<br/>+ reload_rules<br/>+ get_risk_metrics]
            
            IEngineRiskAdapter[EngineRiskAdapter trait<br/>+ check_order_risk<br/>+ check_position_risk<br/>+ engine_type<br/>+ get_metrics]
            
            IRiskManager[RiskManager trait<br/>+ check_order_risk<br/>+ check_position_risk<br/>+ get_max_order_size]
        end
        
        subgraph "基础设施接口"
            ICacheService[CacheService trait<br/>+ get&lt;T&gt;<br/>+ set&lt;T&gt;<br/>+ invalidate<br/>+ clear]
            
            IMetricsCollector[MetricsCollector trait<br/>+ record_risk_check<br/>+ record_cache_hit<br/>+ record_latency<br/>+ get_metrics]
            
            IConfigService[ConfigService trait<br/>+ get_config&lt;T&gt;<br/>+ reload_config<br/>+ validate_config<br/>+ watch_changes]
        end
    end
    
    %% 依赖关系 - 应用层
    WebApp --> WebAdapter
    EngineApp --> BacktestAdapter
    EngineApp --> LiveAdapter
    EngineApp --> StrategyAdapter
    
    %% 依赖关系 - 适配器层
    WebAdapter --> RiskRouter
    BacktestAdapter --> RiskRouter
    LiveAdapter --> RiskRouter
    StrategyAdapter --> RiskRouter
    
    %% 依赖关系 - 服务层
    RiskRouter --> AdapterFactory
    AdapterFactory --> RiskContainer
    
    %% 依赖关系 - 基础设施层
    RiskContainer --> CacheServices
    RiskContainer --> MetricsServices
    RiskContainer --> ConfigServices
    
    %% 依赖关系 - 领域层
    RiskContainer --> RiskEngine
    RiskEngine --> RuleEngine
    
    %% 依赖关系 - 数据层
    RuleEngine --> RiskRepository
    RiskRepository --> Database
    CacheServices --> Database
    ConfigServices --> Database
    
    %% 接口实现关系
    RiskEngine -.-> IRiskEngine
    BacktestAdapter -.-> IEngineRiskAdapter
    LiveAdapter -.-> IEngineRiskAdapter
    WebAdapter -.-> IEngineRiskAdapter
    StrategyAdapter -.-> IEngineRiskAdapter
    
    BacktestAdapter -.-> IRiskManager
    LiveAdapter -.-> IRiskManager
    
    CacheServices -.-> ICacheService
    MetricsServices -.-> IMetricsCollector
    ConfigServices -.-> IConfigService
    
    %% 接口依赖关系
    BacktestAdapter -.-> IRiskEngine
    LiveAdapter -.-> IRiskEngine
    WebAdapter -.-> IRiskEngine
    StrategyAdapter -.-> IRiskEngine
    
    BacktestAdapter -.-> ICacheService
    LiveAdapter -.-> ICacheService
    
    RiskRouter -.-> IMetricsCollector
    BacktestAdapter -.-> IMetricsCollector
    LiveAdapter -.-> IMetricsCollector
    WebAdapter -.-> IMetricsCollector
    StrategyAdapter -.-> IMetricsCollector
    
    RiskContainer -.-> IConfigService
    
    %% 样式定义
    style WebApp fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    style EngineApp fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    style RiskRouter fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    style RiskEngine fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    style RiskContainer fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    
    style IRiskEngine fill:#e0f2f1,stroke:#004d40,stroke-width:2px
    style IEngineRiskAdapter fill:#e8eaf6,stroke:#3f51b5,stroke-width:2px
    style ICacheService fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    style IMetricsCollector fill:#fef7ff,stroke:#4a148c,stroke-width:2px
    style IConfigService fill:#fff8e1,stroke:#ef6c00,stroke-width:2px



    //-----


    graph TB
    subgraph "📁 重构后的完整文件结构"
        subgraph "🏗️ engines/src/"
            EnginesLib[lib.rs<br/>• 统一模块导出<br/>• 公共接口暴露<br/>• 版本兼容性管理<br/>• 特性标志控制]
            
            subgraph "📊 backtest/"
                BacktestMod[mod.rs<br/>• 回测模块导出<br/>• 公共接口定义]
                BacktestEngineFile[engine.rs<br/>• BacktestEngine实现<br/>• 历史数据处理<br/>• 批量执行逻辑<br/>• 性能统计分析]
                BacktestAdapterFile[risk_adapter.rs<br/>• BacktestRiskAdapter<br/>• 高吞吐量优化<br/>• 批量缓存策略<br/>• 接口实现]
                BacktestConfigFile[config.rs<br/>• 回测配置管理<br/>• 参数验证<br/>• 默认值设置]
                BacktestTestFile[tests.rs<br/>• 单元测试<br/>• 集成测试<br/>• 性能测试]
            end
            
            subgraph "⚡ live/"
                LiveMod[mod.rs<br/>• 实盘模块导出<br/>• 公共接口定义]
                LiveEngineFile[engine.rs<br/>• LiveTradingEngine实现<br/>• 实时数据处理<br/>• 订单执行管理<br/>• 实时风险监控]
                LiveAdapterFile[risk_adapter.rs<br/>• LiveTradingRiskAdapter<br/>• 低延迟优化<br/>• 热数据缓存<br/>• 熔断器保护]
                LiveConfigFile[config.rs<br/>• 实盘配置管理<br/>• 风险参数设置<br/>• 延迟优化参数]
                LiveTestFile[tests.rs<br/>• 单元测试<br/>• 集成测试<br/>• 压力测试]
            end
            
            subgraph "🛡️ risk/"
                RiskMod[mod.rs<br/>• 风控模块导出<br/>• trait定义<br/>• 公共类型]
                RouterFile[router.rs<br/>• RiskRouter<br/>• 纯路由分发逻辑<br/>• 引擎类型路由<br/>• 轻量级设计]
                FactoryFile[factory.rs<br/>• AdapterFactory<br/>• 依赖注入管理<br/>• 配置驱动创建<br/>• 生命周期管理]
                ContainerFile[container.rs<br/>• RiskServiceContainer<br/>• 依赖注入容器<br/>• 服务生命周期<br/>• 测试支持]
                TraitsFile[traits.rs<br/>• EngineRiskAdapter trait<br/>• 接口定义<br/>• 类型约束]
                IntegrationFile[integration.rs<br/>• 现有集成逻辑<br/>• 向后兼容<br/>• 迁移支持]
                TestFile[tests.rs<br/>• 适配器测试<br/>• 路由测试<br/>• Mock测试]
            end
            
            subgraph "🔧 base/"
                BaseMod[mod.rs<br/>• 基础模块导出]
                BaseEngineFile[engine.rs<br/>• BaseEngine辅助结构<br/>• 状态管理<br/>• 生命周期控制<br/>• 单策略模式]
                BaseTraitsFile[traits.rs<br/>• TradingEngine trait<br/>• 引擎接口定义<br/>• 生命周期管理]
                BaseFactoryFile[factory.rs<br/>• EngineFactory<br/>• 配置注入<br/>• 依赖管理<br/>• 类型安全创建]
            end
        end
        
        subgraph "🧠 core/src/"
            CoreLib[lib.rs<br/>• 核心模块导出<br/>• 基础类型定义]
            
            subgraph "🔌 traits/"
                RiskTraitsFile[risk.rs<br/>• RiskEngine trait<br/>• RiskManager trait<br/>• 核心风控接口]
                ServiceTraitsFile[services.rs<br/>• CacheService trait<br/>• MetricsCollector trait<br/>• ConfigService trait]
                EngineTraitsFile[engine.rs<br/>• TradingEngine trait<br/>• Strategy trait<br/>• 引擎接口]
            end
            
            subgraph "📋 types/"
                CoreTypesFile[mod.rs<br/>• Order, Balance<br/>• TradingPair<br/>• RiskCheckResult<br/>• 基础数据类型]
                ErrorTypesFile[errors.rs<br/>• SigmaXError<br/>• SigmaXResult<br/>• 错误处理]
            end
        end
        
        subgraph "🔌 services/src/"
            ServicesLib[lib.rs<br/>• 服务模块导出<br/>• 横切关注点]
            
            subgraph "💾 cache/"
                CacheMod[mod.rs<br/>• 缓存服务导出]
                RedisCacheFile[redis.rs<br/>• RedisCacheService<br/>• 分布式缓存<br/>• TTL管理]
                MemoryCacheFile[memory.rs<br/>• MemoryCacheService<br/>• 高性能缓存<br/>• LRU策略]
                CacheStrategyFile[strategy.rs<br/>• CacheStrategy<br/>• 智能路由<br/>• 策略模式]
            end
            
            subgraph "📈 metrics/"
                MetricsMod[mod.rs<br/>• 指标服务导出]
                PrometheusFile[prometheus.rs<br/>• PrometheusMetrics<br/>• 性能监控<br/>• 业务指标]
                LogMetricsFile[log.rs<br/>• LogMetrics<br/>• 结构化日志<br/>• 审计追踪]
                AggregatorFile[aggregator.rs<br/>• MetricsAggregator<br/>• 多收集器支持<br/>• 观察者模式]
            end
            
            subgraph "⚙️ config/"
                ConfigMod[mod.rs<br/>• 配置服务导出]
                DatabaseConfigFile[database.rs<br/>• DatabaseConfigService<br/>• 动态配置<br/>• 热更新]
                FileConfigFile[file.rs<br/>• FileConfigService<br/>• 静态配置<br/>• 版本管理]
            end
        end
    end
    
    subgraph "🚀 实施计划时间线"
        subgraph "第1周：基础架构"
            Week1Task1[创建接口定义<br/>• core/traits/<br/>• 所有trait定义<br/>• 类型约束]
            Week1Task2[创建services模块<br/>• 横切关注点<br/>• 基础实现<br/>• Mock支持]
            Week1Task3[重构engines/risk/<br/>• 路由器<br/>• 工厂<br/>• 容器]
        end
        
        subgraph "第2周：适配器实现"
            Week2Task1[BacktestRiskAdapter<br/>• 高吞吐量优化<br/>• 批量缓存<br/>• 接口实现]
            Week2Task2[LiveTradingRiskAdapter<br/>• 低延迟优化<br/>• 热缓存<br/>• 熔断器]
            Week2Task3[WebApiRiskAdapter<br/>• 详细结果<br/>• 完整信息<br/>• RESTful]
        end
        
        subgraph "第3周：集成测试"
            Week3Task1[引擎集成<br/>• 更新BacktestEngine<br/>• 更新LiveEngine<br/>• 依赖注入]
            Week3Task2[单元测试<br/>• 适配器测试<br/>• 服务测试<br/>• Mock测试]
            Week3Task3[集成测试<br/>• 端到端测试<br/>• 性能测试<br/>• 兼容性测试]
        end
        
        subgraph "第4周：优化发布"
            Week4Task1[性能优化<br/>• 缓存调优<br/>• 延迟优化<br/>• 吞吐量提升]
            Week4Task2[代码清理<br/>• 移除重复<br/>• 文档更新<br/>• 最佳实践]
            Week4Task3[发布准备<br/>• 版本标记<br/>• 迁移指南<br/>• 回滚计划]
        end
    end
    
    %% 文件依赖关系
    BacktestEngineFile --> BacktestAdapterFile
    LiveEngineFile --> LiveAdapterFile
    
    BacktestAdapterFile --> RouterFile
    LiveAdapterFile --> RouterFile
    
    RouterFile --> FactoryFile
    FactoryFile --> ContainerFile
    
    ContainerFile --> RedisCacheFile
    ContainerFile --> PrometheusFile
    ContainerFile --> DatabaseConfigFile
    
    %% 接口依赖
    BacktestAdapterFile -.-> RiskTraitsFile
    LiveAdapterFile -.-> RiskTraitsFile
    
    RouterFile -.-> ServiceTraitsFile
    ContainerFile -.-> ServiceTraitsFile
    
    %% 实施计划依赖
    Week1Task1 --> Week1Task2
    Week1Task2 --> Week1Task3
    Week1Task3 --> Week2Task1
    
    Week2Task1 --> Week2Task2
    Week2Task2 --> Week2Task3
    Week2Task3 --> Week3Task1
    
    Week3Task1 --> Week3Task2
    Week3Task2 --> Week3Task3
    Week3Task3 --> Week4Task1
    
    Week4Task1 --> Week4Task2
    Week4Task2 --> Week4Task3
    
    %% 样式定义
    style EnginesLib fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    style RouterFile fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    style ContainerFile fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    style RiskTraitsFile fill:#e0f2f1,stroke:#004d40,stroke-width:2px
    style ServiceTraitsFile fill:#e8eaf6,stroke:#3f51b5,stroke-width:2px
    
    style Week1Task1 fill:#c8e6c9,stroke:#2e7d32,stroke-width:1px
    style Week2Task1 fill:#bbdefb,stroke:#1976d2,stroke-width:1px
    style Week3Task1 fill:#fff3e0,stroke:#f57c00,stroke-width:1px
    style Week4Task1 fill:#f3e5f5,stroke:#7b1fa2,stroke-width:1px