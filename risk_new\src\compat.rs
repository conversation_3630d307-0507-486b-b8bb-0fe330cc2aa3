//! 兼容性适配器
//!
//! 提供新旧系统之间的兼容性接口

use crate::types::*;
// 注意：旧的风险管理模块已被重构，这里提供兼容性转换
// use sigmax_risk; // 旧的风险管理模块

/// 将新的 RiskCheckResult 转换为旧的格式
pub fn convert_to_old_result(new_result: &RiskCheckResult) -> sigmax_risk::RiskCheckResult {
    sigmax_risk::RiskCheckResult {
        passed: new_result.passed,
        rules_executed: new_result.violated_rules.len(),
        failed_rules: new_result.violated_rules.iter().map(|rule| {
            sigmax_risk::FailedRule {
                rule_id: rule.rule_id,
                rule_name: rule.rule_name.clone(),
                rule_type: format!("{:?}", rule.rule_type),
                failure_reason: rule.violation_reason.clone(),
            }
        }).collect(),
        skipped_rules: vec![], // 简化处理
        execution_time_ms: new_result.execution_time_ms,
        checked_at: new_result.timestamp,
    }
}

/// 将旧的 UnifiedRiskRule 转换为新的 RiskRule
pub fn convert_from_old_rule(old_rule: &sigmax_risk::UnifiedRiskRule) -> Result<RiskRule, String> {
    // 解析旧的参数格式
    let parameters = parse_old_parameters(&old_rule.rule_type, &old_rule.parameters)?;
    
    Ok(RiskRule {
        id: old_rule.id,
        name: old_rule.name.clone(),
        description: old_rule.description.clone(),
        rule_type: parse_rule_type(&old_rule.rule_type)?,
        parameters,
        conditions: RuleConditions {
            market_conditions: None,
            strategy_conditions: None,
            time_conditions: None,
            custom_conditions: None,
        },
        enabled: old_rule.enabled,
        priority: old_rule.priority,
        strategy_types: if let Some(ref strategy_type) = old_rule.strategy_type {
            vec![strategy_type.clone()]
        } else {
            Vec::<String>::new()
        },
        trading_pairs: old_rule.trading_pairs.clone(),
        created_at: old_rule.created_at,
        updated_at: old_rule.updated_at,
    })
}

/// 解析旧的规则类型
fn parse_rule_type(old_type: &str) -> Result<RuleType, String> {
    match old_type {
        "position_limit" => Ok(RuleType::PositionLimit),
        "order_size" => Ok(RuleType::OrderSize),
        "daily_loss" => Ok(RuleType::DailyLoss),
        "volatility" => Ok(RuleType::Volatility),
        "concentration" => Ok(RuleType::Concentration),
        "liquidity" => Ok(RuleType::Liquidity),
        "time_window" => Ok(RuleType::TimeWindow),
        _ => Ok(RuleType::Custom(old_type.to_string())),
    }
}

/// 解析旧的参数格式
fn parse_old_parameters(rule_type: &str, old_params: &serde_json::Value) -> Result<RuleParameters, String> {
    match rule_type {
        "position_limit" => {
            let max_ratio = old_params.get("max_position_ratio")
                .and_then(|v| v.as_f64())
                .unwrap_or(0.8);
            let max_amount = old_params.get("max_absolute_amount")
                .and_then(|v| v.as_f64())
                .map(rust_decimal::Decimal::from_f64_retain)
                .flatten();
            
            Ok(RuleParameters::PositionLimit {
                max_position_ratio: max_ratio,
                max_absolute_amount: max_amount,
            })
        }
        "order_size" => {
            let max_ratio = old_params.get("max_order_ratio")
                .and_then(|v| v.as_f64())
                .unwrap_or(0.1);
            let max_size = old_params.get("max_absolute_size")
                .and_then(|v| v.as_f64())
                .map(rust_decimal::Decimal::from_f64_retain)
                .flatten();
            let min_size = old_params.get("min_order_size")
                .and_then(|v| v.as_f64())
                .map(rust_decimal::Decimal::from_f64_retain)
                .flatten();
            
            Ok(RuleParameters::OrderSize {
                max_order_ratio: max_ratio,
                max_absolute_size: max_size,
                min_order_size: min_size,
            })
        }
        "daily_loss" => {
            let max_ratio = old_params.get("max_loss_ratio")
                .and_then(|v| v.as_f64())
                .unwrap_or(0.05);
            let max_loss = old_params.get("max_absolute_loss")
                .and_then(|v| v.as_f64())
                .map(rust_decimal::Decimal::from_f64_retain)
                .flatten();
            let reset_time = old_params.get("reset_time")
                .and_then(|v| v.as_str())
                .unwrap_or("00:00:00")
                .to_string();
            
            Ok(RuleParameters::DailyLoss {
                max_loss_ratio: max_ratio,
                max_absolute_loss: max_loss,
                reset_time,
            })
        }
        "volatility" => {
            let max_vol = old_params.get("max_volatility")
                .and_then(|v| v.as_f64())
                .unwrap_or(0.3);
            let lookback = old_params.get("lookback_period_days")
                .and_then(|v| v.as_u64())
                .unwrap_or(30) as u32;
            
            Ok(RuleParameters::Volatility {
                max_volatility: max_vol,
                lookback_period_days: lookback,
            })
        }
        _ => {
            // 对于未知类型，使用自定义参数
            let mut params = std::collections::HashMap::new();
            if let serde_json::Value::Object(map) = old_params {
                for (k, v) in map {
                    params.insert(k.clone(), v.clone());
                }
            }
            Ok(RuleParameters::Custom { parameters: params })
        }
    }
}

/// 双写适配器 - 同时写入新旧系统
pub struct DualWriteAdapter {
    pub old_engine: std::sync::Arc<sigmax_risk::UnifiedRiskEngine>,
    pub new_engine: std::sync::Arc<dyn crate::engine::RiskEngine>,
}

impl DualWriteAdapter {
    pub fn new(
        old_engine: std::sync::Arc<sigmax_risk::UnifiedRiskEngine>,
        new_engine: std::sync::Arc<dyn crate::engine::RiskEngine>,
    ) -> Self {
        Self { old_engine, new_engine }
    }
    
    /// 并行执行新旧系统的风险检查，比较结果
    pub async fn check_order_risk_dual(
        &self,
        order: &sigmax_core::Order,
        context: &RiskContext,
    ) -> sigmax_core::SigmaXResult<(RiskCheckResult, bool)> {
        // 执行新系统检查
        let new_result = self.new_engine.check_order_risk(order, context).await?;
        
        // 执行旧系统检查
        let old_result = self.old_engine.check_order_risk(order, context.strategy_type.as_deref()).await?;
        
        // 比较结果一致性
        let consistent = new_result.passed == old_result.passed;
        
        if !consistent {
            tracing::warn!(
                "新旧系统风险检查结果不一致: 新系统={}, 旧系统={}, 订单={}",
                new_result.passed,
                old_result.passed,
                order.id
            );
        }
        
        Ok((new_result, consistent))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use uuid::Uuid;
    use chrono::Utc;
    
    #[test]
    fn test_convert_rule_type() {
        assert_eq!(parse_rule_type("position_limit").unwrap(), RuleType::PositionLimit);
        assert_eq!(parse_rule_type("order_size").unwrap(), RuleType::OrderSize);
        assert_eq!(parse_rule_type("unknown"), Ok(RuleType::Custom("unknown".to_string())));
    }
    
    #[test]
    fn test_parse_position_limit_parameters() {
        let params = serde_json::json!({
            "max_position_ratio": 0.3,
            "max_absolute_amount": 50000.0
        });
        
        let result = parse_old_parameters("position_limit", &params).unwrap();
        
        match result {
            RuleParameters::PositionLimit { max_position_ratio, max_absolute_amount } => {
                assert_eq!(max_position_ratio, 0.3);
                assert!(max_absolute_amount.is_some());
            }
            _ => panic!("Expected PositionLimit parameters"),
        }
    }
}
