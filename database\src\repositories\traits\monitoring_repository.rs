//! 监控配置仓储接口定义
//!
//! 定义监控配置数据访问的标准接口

use async_trait::async_trait;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use sigmax_core::{SigmaXResult, MonitoringConfig};

/// 监控配置记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonitoringConfigRecord {
    /// 配置ID
    pub id: Uuid,
    /// 配置名称
    pub name: String,
    /// 配置描述
    pub description: Option<String>,
    /// 是否启用
    pub enabled: bool,
    /// 监控参数 (JSONB存储)
    pub monitoring_parameters: serde_json::Value,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 更新时间
    pub updated_at: DateTime<Utc>,
    /// 创建者
    pub created_by: Option<String>,
}

/// 监控配置统计信息
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct MonitoringConfigStatistics {
    /// 总配置数量
    pub total_configs: u64,
    /// 启用的配置数量
    pub enabled_configs: u64,
    /// 最后更新时间
    pub last_updated: Option<DateTime<Utc>>,
    /// 平均配置大小(字节)
    pub average_config_size: f64,
}

/// 监控指标记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonitoringMetricsRecord {
    /// 记录ID
    pub id: Uuid,
    /// 指标类型
    pub metric_type: String,
    /// 指标名称
    pub metric_name: String,
    /// 指标值
    pub metric_value: f64,
    /// 指标单位
    pub unit: Option<String>,
    /// 标签
    pub labels: serde_json::Value,
    /// 记录时间
    pub timestamp: DateTime<Utc>,
    /// 数据来源
    pub source: String,
}

/// 告警记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertRecord {
    /// 告警ID
    pub id: Uuid,
    /// 告警规则ID
    pub rule_id: String,
    /// 告警级别
    pub severity: String,
    /// 告警消息
    pub message: String,
    /// 触发值
    pub triggered_value: f64,
    /// 阈值
    pub threshold_value: f64,
    /// 告警状态
    pub status: AlertStatus,
    /// 触发时间
    pub triggered_at: DateTime<Utc>,
    /// 确认时间
    pub acknowledged_at: Option<DateTime<Utc>>,
    /// 解决时间
    pub resolved_at: Option<DateTime<Utc>>,
    /// 确认者
    pub acknowledged_by: Option<String>,
}

/// 告警状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum AlertStatus {
    /// 触发
    Triggered,
    /// 已确认
    Acknowledged,
    /// 已解决
    Resolved,
    /// 已抑制
    Suppressed,
}

/// 监控配置仓储接口
#[async_trait]
pub trait MonitoringRepository: Send + Sync {
    // ============================================================================
    // 配置管理
    // ============================================================================
    
    /// 获取监控配置
    async fn get_config(&self) -> SigmaXResult<MonitoringConfig>;
    
    /// 保存监控配置
    async fn save_config(&self, config: &MonitoringConfig) -> SigmaXResult<Uuid>;
    
    /// 更新监控配置
    async fn update_config(&self, id: Uuid, config: &MonitoringConfig) -> SigmaXResult<()>;
    
    /// 删除监控配置
    async fn delete_config(&self, id: Uuid) -> SigmaXResult<()>;
    
    /// 获取配置记录
    async fn get_config_record(&self, id: Uuid) -> SigmaXResult<Option<MonitoringConfigRecord>>;
    
    /// 获取所有配置记录
    async fn get_all_config_records(&self) -> SigmaXResult<Vec<MonitoringConfigRecord>>;
    
    /// 重置为默认配置
    async fn reset_to_default(&self) -> SigmaXResult<()>;
    
    // ============================================================================
    // 指标数据管理
    // ============================================================================
    
    /// 保存监控指标
    async fn save_metrics(&self, metrics: &[MonitoringMetricsRecord]) -> SigmaXResult<()>;
    
    /// 获取指标数据
    async fn get_metrics(
        &self,
        metric_type: Option<&str>,
        start_time: Option<DateTime<Utc>>,
        end_time: Option<DateTime<Utc>>,
        limit: Option<u32>,
    ) -> SigmaXResult<Vec<MonitoringMetricsRecord>>;
    
    /// 清理过期指标数据
    async fn cleanup_expired_metrics(&self, retention_days: u32) -> SigmaXResult<u64>;
    
    // ============================================================================
    // 告警管理
    // ============================================================================
    
    /// 创建告警
    async fn create_alert(&self, alert: &AlertRecord) -> SigmaXResult<Uuid>;
    
    /// 更新告警状态
    async fn update_alert_status(
        &self,
        alert_id: Uuid,
        status: AlertStatus,
        acknowledged_by: Option<&str>,
    ) -> SigmaXResult<()>;
    
    /// 获取活跃告警
    async fn get_active_alerts(&self) -> SigmaXResult<Vec<AlertRecord>>;
    
    /// 获取告警历史
    async fn get_alert_history(
        &self,
        start_time: Option<DateTime<Utc>>,
        end_time: Option<DateTime<Utc>>,
        limit: Option<u32>,
    ) -> SigmaXResult<Vec<AlertRecord>>;
    
    // ============================================================================
    // 统计信息
    // ============================================================================
    
    /// 获取配置统计信息
    async fn get_config_statistics(&self) -> SigmaXResult<MonitoringConfigStatistics>;
    
    /// 获取告警统计信息
    async fn get_alert_statistics(
        &self,
        start_time: Option<DateTime<Utc>>,
        end_time: Option<DateTime<Utc>>,
    ) -> SigmaXResult<serde_json::Value>;
    
    // ============================================================================
    // 健康检查
    // ============================================================================
    
    /// 检查仓储健康状态
    async fn health_check(&self) -> SigmaXResult<bool>;
    
    /// 获取仓储信息
    async fn get_repository_info(&self) -> SigmaXResult<serde_json::Value>;
}
