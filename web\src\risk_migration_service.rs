//! 风险管理迁移服务
//!
//! 负责协调新旧风险管理系统的迁移过程

use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{info, warn, error};
use serde::{Deserialize, Serialize};

use sigmax_core::{Order, SigmaXResult, SigmaXError};
use sigmax_risk_new::{
    UnifiedRiskEngine, DefaultRuleEngine, DefaultRiskMetricsCalculator,
    InMemoryRuleRepository, test_utils::MockHistoricalDataProvider,
    RiskContext, RiskCheckResult, config::templates,
};

use crate::migration_controller::{MigrationController, MigrationConfig, MigrationStats};

/// 风险管理迁移服务
pub struct RiskMigrationService {
    /// 迁移控制器
    migration_controller: Arc<MigrationController>,
    /// 新风险引擎
    new_risk_engine: Arc<UnifiedRiskEngine>,
    /// 服务配置
    config: Arc<RwLock<ServiceConfig>>,
}

/// 服务配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceConfig {
    /// 是否启用迁移服务
    pub enabled: bool,
    /// 自动迁移模式
    pub auto_migration_enabled: bool,
    /// 迁移阶段
    pub current_phase: MigrationPhase,
    /// 监控间隔（秒）
    pub monitoring_interval_seconds: u64,
}

/// 迁移阶段
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum MigrationPhase {
    /// 准备阶段
    Preparation,
    /// 双写测试阶段
    DualWriteTesting,
    /// 小流量测试阶段
    SmallTrafficTesting,
    /// 灰度发布阶段
    GradualRollout,
    /// 大流量测试阶段
    HighTrafficTesting,
    /// 完全迁移阶段
    FullMigration,
    /// 清理阶段
    Cleanup,
}

impl Default for ServiceConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            auto_migration_enabled: false,
            current_phase: MigrationPhase::Preparation,
            monitoring_interval_seconds: 60,
        }
    }
}

impl RiskMigrationService {
    /// 创建新的迁移服务
    pub async fn new(config: Option<ServiceConfig>) -> SigmaXResult<Self> {
        info!("初始化风险管理迁移服务");

        // 创建新风险引擎
        let new_risk_engine = Self::create_new_risk_engine().await?;

        // 创建迁移控制器
        let migration_controller = Arc::new(MigrationController::new(
            new_risk_engine.clone(),
            Some(MigrationConfig::default()),
        ));

        let service = Self {
            migration_controller,
            new_risk_engine,
            config: Arc::new(RwLock::new(config.unwrap_or_default())),
        };

        info!("风险管理迁移服务初始化完成");
        Ok(service)
    }

    /// 创建新的风险引擎
    async fn create_new_risk_engine() -> SigmaXResult<Arc<UnifiedRiskEngine>> {
        info!("创建新的风险引擎");

        // 创建内存仓储（生产环境中应该使用数据库仓储）
        let repository = Arc::new(InMemoryRuleRepository::new());
        
        // 添加一些默认规则
        Self::setup_default_rules(&repository).await?;

        // 创建模拟数据提供者（生产环境中应该使用真实数据提供者）
        let data_provider = MockHistoricalDataProvider::new();

        // 使用平衡配置
        let config = templates::create_balanced_config();

        // 创建规则引擎
        let rule_engine = Arc::new(DefaultRuleEngine::new(
            repository,
            Some(config.rule_engine),
        ));

        // 创建指标计算器
        let metrics_calculator = Arc::new(DefaultRiskMetricsCalculator::new(
            Box::new(data_provider),
            Some(config.metrics_calculator),
        ));

        // 创建风险引擎
        let risk_engine = Arc::new(UnifiedRiskEngine::new(
            rule_engine,
            metrics_calculator,
            Some(config.risk_engine),
        ));

        info!("新风险引擎创建完成");
        Ok(risk_engine)
    }

    /// 设置默认规则
    async fn setup_default_rules(repository: &InMemoryRuleRepository) -> SigmaXResult<()> {
        use sigmax_risk_new::{RiskRule, RuleType, RuleParameters, RuleConditions};
        use uuid::Uuid;
        use chrono::Utc;

        // 持仓限制规则
        let position_limit_rule = RiskRule {
            id: Uuid::new_v4(),
            name: "默认持仓限制".to_string(),
            description: Some("限制单个资产的最大持仓比例".to_string()),
            rule_type: RuleType::PositionLimit,
            parameters: RuleParameters::PositionLimit {
                max_position_ratio: 0.3, // 最大30%
                max_absolute_amount: Some(rust_decimal::Decimal::from(100000)), // 最大10万
            },
            conditions: RuleConditions {
                market_conditions: None,
                strategy_conditions: None,
                time_conditions: None,
                custom_conditions: None,
            },
            enabled: true,
            priority: 100,
            strategy_types: vec![],
            trading_pairs: vec![],
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        // 订单大小限制规则
        let order_size_rule = RiskRule {
            id: Uuid::new_v4(),
            name: "默认订单大小限制".to_string(),
            description: Some("限制单笔订单的最大金额".to_string()),
            rule_type: RuleType::OrderSize,
            parameters: RuleParameters::OrderSize {
                max_order_ratio: 0.1, // 最大10%
                max_absolute_size: Some(rust_decimal::Decimal::from(20000)), // 最大2万
                min_order_size: Some(rust_decimal::Decimal::from(10)), // 最小10元
            },
            conditions: RuleConditions {
                market_conditions: None,
                strategy_conditions: None,
                time_conditions: None,
                custom_conditions: None,
            },
            enabled: true,
            priority: 90,
            strategy_types: vec![],
            trading_pairs: vec![],
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        repository.add_rule(position_limit_rule);
        repository.add_rule(order_size_rule);

        info!("默认风险规则设置完成");
        Ok(())
    }

    /// 检查订单风险（迁移入口点）
    pub async fn check_order_risk(
        &self,
        order: &Order,
        context: &RiskContext,
    ) -> SigmaXResult<RiskCheckResult> {
        let config = self.config.read().await;
        
        if !config.enabled {
            return Err(SigmaXError::ServiceUnavailable(
                "风险管理迁移服务已禁用".to_string()
            ));
        }

        // 通过迁移控制器进行风险检查
        self.migration_controller.check_order_risk(order, context).await
    }

    /// 获取迁移状态
    pub async fn get_migration_status(&self) -> SigmaXResult<MigrationStatusResponse> {
        let config = self.config.read().await;
        let migration_config = self.migration_controller.get_config().await;
        let stats = self.migration_controller.get_stats().await;

        Ok(MigrationStatusResponse {
            service_enabled: config.enabled,
            current_phase: config.current_phase.clone(),
            migration_config,
            stats,
            auto_migration_enabled: config.auto_migration_enabled,
        })
    }

    /// 更新迁移配置
    pub async fn update_migration_config(&self, new_config: MigrationConfig) -> SigmaXResult<()> {
        info!("更新迁移配置: {:?}", new_config);
        self.migration_controller.update_config(new_config).await;
        Ok(())
    }

    /// 控制流量比例
    pub async fn control_traffic(&self, percentage: u8, reason: Option<String>) -> SigmaXResult<()> {
        info!("调整流量比例到 {}%，原因: {:?}", percentage, reason);

        let mut migration_config = self.migration_controller.get_config().await;
        migration_config.new_engine_traffic_percentage = percentage;
        
        // 根据流量比例自动调整其他设置
        if percentage == 0 {
            migration_config.enable_new_engine = false;
        } else {
            migration_config.enable_new_engine = true;
        }

        self.migration_controller.update_config(migration_config).await;

        // 更新服务阶段
        let mut config = self.config.write().await;
        config.current_phase = match percentage {
            0 => MigrationPhase::Preparation,
            1..=10 => MigrationPhase::SmallTrafficTesting,
            11..=30 => MigrationPhase::GradualRollout,
            31..=80 => MigrationPhase::HighTrafficTesting,
            81..=100 => MigrationPhase::FullMigration,
            _ => config.current_phase.clone(),
        };

        Ok(())
    }

    /// 执行紧急回滚
    pub async fn emergency_rollback(&self, reason: String) -> SigmaXResult<()> {
        warn!("执行紧急回滚: {}", reason);

        // 立即停止新引擎
        let mut migration_config = self.migration_controller.get_config().await;
        migration_config.enable_new_engine = false;
        migration_config.enable_dual_write = false;
        migration_config.new_engine_traffic_percentage = 0;

        self.migration_controller.update_config(migration_config).await;

        // 更新服务状态
        let mut config = self.config.write().await;
        config.current_phase = MigrationPhase::Preparation;

        info!("紧急回滚完成");
        Ok(())
    }

    /// 获取迁移统计信息
    pub async fn get_migration_stats(&self) -> MigrationStats {
        self.migration_controller.get_stats().await
    }

    /// 重置统计信息
    pub async fn reset_stats(&self) -> SigmaXResult<()> {
        self.migration_controller.reset_stats().await;
        Ok(())
    }

    /// 启动自动监控
    pub async fn start_monitoring(&self) -> SigmaXResult<()> {
        let config = self.config.read().await;
        if !config.auto_migration_enabled {
            return Ok(());
        }

        info!("启动自动迁移监控");
        // TODO: 实现自动监控逻辑
        Ok(())
    }
}

/// 迁移状态响应
#[derive(Debug, Serialize)]
pub struct MigrationStatusResponse {
    pub service_enabled: bool,
    pub current_phase: MigrationPhase,
    pub migration_config: MigrationConfig,
    pub stats: MigrationStats,
    pub auto_migration_enabled: bool,
}
