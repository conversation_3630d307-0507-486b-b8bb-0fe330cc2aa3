{"test_info": {"start_time": "2025-06-14T02:20:51.019305", "end_time": "2025-06-14T02:21:01.852318", "duration_seconds": 10.833013, "total_requests": 48}, "summary": {"total_requests": 48, "successful_requests": 48, "failed_requests": 0, "success_rate": 100.0}, "detailed_results": [{"endpoint": "GET /api/health", "status_code": 200, "response_time_ms": 4.82177734375, "success": true, "content_length": 2, "timestamp": "2025-06-14T02:20:51.024555"}, {"endpoint": "GET /api/backtest/files", "status_code": 200, "response_time_ms": 2.0952224731445312, "success": true, "content_length": 270, "timestamp": "2025-06-14T02:20:51.027072"}, {"endpoint": "GET /api/v2/exchanges", "status_code": 200, "response_time_ms": 1.333475112915039, "success": true, "content_length": 5329, "timestamp": "2025-06-14T02:20:51.028441"}, {"endpoint": "GET /api/v2/market/ticker/BTCUSDT", "status_code": 200, "response_time_ms": 1.2769699096679688, "success": true, "content_length": 369, "timestamp": "2025-06-14T02:20:51.029749"}, {"endpoint": "GET /api/v2/market/orderbook/BTCUSDT", "status_code": 200, "response_time_ms": 20.441055297851562, "success": true, "content_length": 1572, "timestamp": "2025-06-14T02:20:51.050221"}, {"endpoint": "GET /api/v2/reports/performance", "status_code": 200, "response_time_ms": 1.5141963958740234, "success": true, "content_length": 1425, "timestamp": "2025-06-14T02:20:51.051778"}, {"endpoint": "GET /api/v2/config", "status_code": 200, "response_time_ms": 16.183853149414062, "success": true, "content_length": 2999, "timestamp": "2025-06-14T02:20:51.067998"}, {"endpoint": "GET /api/v2/risk/parameters", "status_code": 200, "response_time_ms": 1.6121864318847656, "success": true, "content_length": 424, "timestamp": "2025-06-14T02:20:51.069654"}, {"endpoint": "GET /api/v2/portfolios", "status_code": 200, "response_time_ms": 1.4274120330810547, "success": true, "content_length": 2073, "timestamp": "2025-06-14T02:20:51.071118"}, {"endpoint": "GET /api/v2/monitoring/health", "status_code": 200, "response_time_ms": 13.773679733276367, "success": true, "content_length": 566, "timestamp": "2025-06-14T02:20:51.084926"}, {"endpoint": "GET /api/v2/cache/stats", "status_code": 200, "response_time_ms": 15.761137008666992, "success": true, "content_length": 741, "timestamp": "2025-06-14T02:20:51.100755"}, {"endpoint": "GET /api/v2/database/status", "status_code": 200, "response_time_ms": 1.592874526977539, "success": true, "content_length": 1006, "timestamp": "2025-06-14T02:20:51.102387"}, {"endpoint": "GET /api/trades", "status_code": 200, "response_time_ms": 415.36498069763184, "success": true, "content_length": 68, "timestamp": "2025-06-14T02:20:51.435771"}, {"endpoint": "GET /api/orders", "status_code": 200, "response_time_ms": 424.61085319519043, "success": true, "content_length": 65, "timestamp": "2025-06-14T02:20:51.444900"}, {"endpoint": "GET /api/strategies", "status_code": 200, "response_time_ms": 2543.6441898345947, "success": true, "content_length": 3172, "timestamp": "2025-06-14T02:20:53.563796"}, {"endpoint": "GET /api/status", "status_code": 200, "response_time_ms": 4501.325607299805, "success": true, "content_length": 233, "timestamp": "2025-06-14T02:20:55.521311"}, {"endpoint": "GET /api/health", "status_code": 200, "response_time_ms": 13.184070587158203, "success": true, "content_length": 2, "timestamp": "2025-06-14T02:20:56.537148"}, {"endpoint": "GET /api/backtest/files", "status_code": 200, "response_time_ms": 5.945920944213867, "success": true, "content_length": 270, "timestamp": "2025-06-14T02:20:56.543245"}, {"endpoint": "GET /api/v2/exchanges", "status_code": 200, "response_time_ms": 1.7397403717041016, "success": true, "content_length": 5338, "timestamp": "2025-06-14T02:20:56.545030"}, {"endpoint": "GET /api/v2/market/ticker/BTCUSDT", "status_code": 200, "response_time_ms": 1.252889633178711, "success": true, "content_length": 369, "timestamp": "2025-06-14T02:20:56.546316"}, {"endpoint": "GET /api/v2/market/orderbook/BTCUSDT", "status_code": 200, "response_time_ms": 1.194000244140625, "success": true, "content_length": 1572, "timestamp": "2025-06-14T02:20:56.547540"}, {"endpoint": "GET /api/v2/reports/performance", "status_code": 200, "response_time_ms": 1.1138916015625, "success": true, "content_length": 1425, "timestamp": "2025-06-14T02:20:56.548682"}, {"endpoint": "GET /api/v2/config", "status_code": 200, "response_time_ms": 1.3120174407958984, "success": true, "content_length": 2999, "timestamp": "2025-06-14T02:20:56.550038"}, {"endpoint": "GET /api/v2/risk/parameters", "status_code": 200, "response_time_ms": 1.218557357788086, "success": true, "content_length": 424, "timestamp": "2025-06-14T02:20:56.551289"}, {"endpoint": "GET /api/v2/portfolios", "status_code": 200, "response_time_ms": 1.4858245849609375, "success": true, "content_length": 2079, "timestamp": "2025-06-14T02:20:56.552806"}, {"endpoint": "GET /api/v2/monitoring/health", "status_code": 200, "response_time_ms": 1.2197494506835938, "success": true, "content_length": 566, "timestamp": "2025-06-14T02:20:56.554057"}, {"endpoint": "GET /api/v2/cache/stats", "status_code": 200, "response_time_ms": 13.020038604736328, "success": true, "content_length": 741, "timestamp": "2025-06-14T02:20:56.567107"}, {"endpoint": "GET /api/v2/database/status", "status_code": 200, "response_time_ms": 1.8208026885986328, "success": true, "content_length": 1003, "timestamp": "2025-06-14T02:20:56.568972"}, {"endpoint": "GET /api/orders", "status_code": 200, "response_time_ms": 1428.5657405853271, "success": true, "content_length": 68, "timestamp": "2025-06-14T02:20:57.959280"}, {"endpoint": "GET /api/trades", "status_code": 200, "response_time_ms": 1463.0382061004639, "success": true, "content_length": 68, "timestamp": "2025-06-14T02:20:57.997898"}, {"endpoint": "GET /api/strategies", "status_code": 200, "response_time_ms": 2067.4641132354736, "success": true, "content_length": 3172, "timestamp": "2025-06-14T02:20:58.595732"}, {"endpoint": "GET /api/status", "status_code": 200, "response_time_ms": 2110.9588146209717, "success": true, "content_length": 230, "timestamp": "2025-06-14T02:20:58.637215"}, {"endpoint": "GET /api/health", "status_code": 200, "response_time_ms": 15.007972717285156, "success": true, "content_length": 2, "timestamp": "2025-06-14T02:20:59.655340"}, {"endpoint": "GET /api/backtest/files", "status_code": 200, "response_time_ms": 14.684677124023438, "success": true, "content_length": 270, "timestamp": "2025-06-14T02:20:59.670069"}, {"endpoint": "GET /api/v2/exchanges", "status_code": 200, "response_time_ms": 15.72418212890625, "success": true, "content_length": 5335, "timestamp": "2025-06-14T02:20:59.685849"}, {"endpoint": "GET /api/v2/market/ticker/BTCUSDT", "status_code": 200, "response_time_ms": 17.018556594848633, "success": true, "content_length": 369, "timestamp": "2025-06-14T02:20:59.702934"}, {"endpoint": "GET /api/v2/market/orderbook/BTCUSDT", "status_code": 200, "response_time_ms": 17.648696899414062, "success": true, "content_length": 1569, "timestamp": "2025-06-14T02:20:59.720666"}, {"endpoint": "GET /api/v2/reports/performance", "status_code": 200, "response_time_ms": 2.685070037841797, "success": true, "content_length": 1425, "timestamp": "2025-06-14T02:20:59.723431"}, {"endpoint": "GET /api/v2/config", "status_code": 200, "response_time_ms": 2.042531967163086, "success": true, "content_length": 2999, "timestamp": "2025-06-14T02:20:59.725521"}, {"endpoint": "GET /api/v2/risk/parameters", "status_code": 200, "response_time_ms": 28.313159942626953, "success": true, "content_length": 424, "timestamp": "2025-06-14T02:20:59.753872"}, {"endpoint": "GET /api/v2/portfolios", "status_code": 200, "response_time_ms": 4.137516021728516, "success": true, "content_length": 2079, "timestamp": "2025-06-14T02:20:59.758103"}, {"endpoint": "GET /api/v2/monitoring/health", "status_code": 200, "response_time_ms": 2.3827552795410156, "success": true, "content_length": 563, "timestamp": "2025-06-14T02:20:59.760542"}, {"endpoint": "GET /api/v2/cache/stats", "status_code": 200, "response_time_ms": 2.6047229766845703, "success": true, "content_length": 741, "timestamp": "2025-06-14T02:20:59.763200"}, {"endpoint": "GET /api/v2/database/status", "status_code": 200, "response_time_ms": 2.3856163024902344, "success": true, "content_length": 1006, "timestamp": "2025-06-14T02:20:59.765644"}, {"endpoint": "GET /api/trades", "status_code": 200, "response_time_ms": 1415.8620834350586, "success": true, "content_length": 68, "timestamp": "2025-06-14T02:21:01.064154"}, {"endpoint": "GET /api/orders", "status_code": 200, "response_time_ms": 1419.921636581421, "success": true, "content_length": 68, "timestamp": "2025-06-14T02:21:01.066550"}, {"endpoint": "GET /api/status", "status_code": 200, "response_time_ms": 1861.0260486602783, "success": true, "content_length": 233, "timestamp": "2025-06-14T02:21:01.503644"}, {"endpoint": "GET /api/strategies", "status_code": 200, "response_time_ms": 2206.9571018218994, "success": true, "content_length": 3172, "timestamp": "2025-06-14T02:21:01.851932"}]}