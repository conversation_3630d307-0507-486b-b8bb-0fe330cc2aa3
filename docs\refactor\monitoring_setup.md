# 重构过程监控设置

## 关键指标监控

### 1. 性能指标
- 风险检查响应时间
- 规则执行时间
- 内存使用情况
- CPU 使用率

### 2. 业务指标
- 风险检查通过率
- 规则违反次数
- 系统错误率
- API 调用量

### 3. 迁移指标
- 新旧系统结果一致性
- 数据迁移进度
- 功能迁移完成度

## 日志配置

```rust
// 在 main.rs 中添加
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

fn init_logging() {
    tracing_subscriber::registry()
        .with(tracing_subscriber::EnvFilter::new(
            std::env::var("RUST_LOG").unwrap_or_else(|_| "info".into()),
        ))
        .with(tracing_subscriber::fmt::layer())
        .init();
}
```

## 告警规则

1. **性能告警**
   - 响应时间超过 100ms
   - 内存使用超过 80%
   - 错误率超过 1%

2. **一致性告警**
   - 新旧系统结果不一致率超过 0.1%
   - 数据迁移失败

3. **业务告警**
   - 风险检查失败率异常
   - 关键规则执行失败
