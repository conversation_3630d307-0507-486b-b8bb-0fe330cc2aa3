//! 数据存储实现

use sigmax_core::{Candle, SigmaXResult, TradingPair, TimeFrame, SigmaXError, DatabaseConnection};
use chrono::{DateTime, Utc};
use std::sync::Arc;
use std::collections::HashMap;
use std::str::FromStr;

/// 数据存储接口
pub trait DataStorage: Send + Sync {
    /// 保存K线数据
    async fn save_candles(&self, trading_pair: &TradingPair, timeframe: TimeFrame, candles: &[Candle]) -> SigmaXResult<()>;

    /// 获取K线数据
    async fn get_candles(
        &self,
        trading_pair: &TradingPair,
        timeframe: TimeFrame,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> SigmaXResult<Vec<Candle>>;

    /// 删除K线数据
    async fn delete_candles(
        &self,
        trading_pair: &TradingPair,
        timeframe: TimeFrame,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> SigmaXResult<()>;
}

/// 数据库存储实现
pub struct DatabaseStorage {
    db: Arc<dyn DatabaseConnection>,
}

impl DatabaseStorage {
    pub fn new(db: Arc<dyn DatabaseConnection>) -> Self {
        Self { db }
    }
}

impl DataStorage for DatabaseStorage {
    async fn save_candles(&self, trading_pair: &TradingPair, timeframe: TimeFrame, candles: &[Candle]) -> SigmaXResult<()> {
        if candles.is_empty() {
            return Ok(());
        }

        // 批量插入K线数据
        let query = r#"
            INSERT INTO candles (trading_pair_base, trading_pair_quote, timeframe, timestamp, open, high, low, close, volume)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            ON CONFLICT (trading_pair_base, trading_pair_quote, timeframe, timestamp)
            DO UPDATE SET
                open = EXCLUDED.open,
                high = EXCLUDED.high,
                low = EXCLUDED.low,
                close = EXCLUDED.close,
                volume = EXCLUDED.volume
        "#;

        let timeframe_str = format!("{:?}", timeframe);

        for candle in candles {
            let params = vec![
                trading_pair.base.clone(),
                trading_pair.quote.clone(),
                timeframe_str.clone(),
                candle.timestamp.to_rfc3339(),
                candle.open.to_string(),
                candle.high.to_string(),
                candle.low.to_string(),
                candle.close.to_string(),
                candle.volume.to_string(),
            ];

            self.db.execute_query(query, &params).await?;
        }

        Ok(())
    }

    async fn get_candles(
        &self,
        trading_pair: &TradingPair,
        timeframe: TimeFrame,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> SigmaXResult<Vec<Candle>> {
        let query = r#"
            SELECT timestamp, open, high, low, close, volume
            FROM candles
            WHERE trading_pair_base = $1
                AND trading_pair_quote = $2
                AND timeframe = $3
                AND timestamp >= $4
                AND timestamp <= $5
            ORDER BY timestamp ASC
        "#;

        let timeframe_str = format!("{:?}", timeframe);
        let params = vec![
            trading_pair.base.clone(),
            trading_pair.quote.clone(),
            timeframe_str,
            start_time.to_rfc3339(),
            end_time.to_rfc3339(),
        ];

        let rows = self.db.query_many(query, &params).await?;
        let mut candles = Vec::new();

        for row in rows {
            let candle = Candle {
                timestamp: chrono::DateTime::parse_from_rfc3339(row.get("timestamp").unwrap().as_str().unwrap())
                    .map_err(|e| SigmaXError::InvalidParameter(format!("Invalid timestamp: {}", e)))?
                    .with_timezone(&Utc),
                open: rust_decimal::Decimal::from_str(row.get("open").unwrap().as_str().unwrap())
                    .map_err(|e| SigmaXError::InvalidParameter(format!("Invalid open price: {}", e)))?,
                high: rust_decimal::Decimal::from_str(row.get("high").unwrap().as_str().unwrap())
                    .map_err(|e| SigmaXError::InvalidParameter(format!("Invalid high price: {}", e)))?,
                low: rust_decimal::Decimal::from_str(row.get("low").unwrap().as_str().unwrap())
                    .map_err(|e| SigmaXError::InvalidParameter(format!("Invalid low price: {}", e)))?,
                close: rust_decimal::Decimal::from_str(row.get("close").unwrap().as_str().unwrap())
                    .map_err(|e| SigmaXError::InvalidParameter(format!("Invalid close price: {}", e)))?,
                volume: rust_decimal::Decimal::from_str(row.get("volume").unwrap().as_str().unwrap())
                    .map_err(|e| SigmaXError::InvalidParameter(format!("Invalid volume: {}", e)))?,
            };
            candles.push(candle);
        }

        Ok(candles)
    }

    async fn delete_candles(
        &self,
        trading_pair: &TradingPair,
        timeframe: TimeFrame,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> SigmaXResult<()> {
        let query = r#"
            DELETE FROM candles
            WHERE trading_pair_base = $1
                AND trading_pair_quote = $2
                AND timeframe = $3
                AND timestamp >= $4
                AND timestamp <= $5
        "#;

        let timeframe_str = format!("{:?}", timeframe);
        let params = vec![
            trading_pair.base.clone(),
            trading_pair.quote.clone(),
            timeframe_str,
            start_time.to_rfc3339(),
            end_time.to_rfc3339(),
        ];

        self.db.execute_query(query, &params).await?;
        Ok(())
    }
}

/// 内存存储实现（用于测试和缓存）
pub struct MemoryStorage {
    data: tokio::sync::RwLock<HashMap<String, Vec<Candle>>>,
}

impl MemoryStorage {
    pub fn new() -> Self {
        Self {
            data: tokio::sync::RwLock::new(HashMap::new()),
        }
    }

    fn make_key(trading_pair: &TradingPair, timeframe: TimeFrame) -> String {
        format!("{}_{:?}", trading_pair.symbol(), timeframe)
    }
}

impl DataStorage for MemoryStorage {
    async fn save_candles(&self, trading_pair: &TradingPair, timeframe: TimeFrame, candles: &[Candle]) -> SigmaXResult<()> {
        let key = Self::make_key(trading_pair, timeframe);
        let mut data = self.data.write().await;

        let stored_candles = data.entry(key).or_insert_with(Vec::new);

        // 合并新数据，避免重复
        for new_candle in candles {
            // 检查是否已存在相同时间戳的数据
            if let Some(existing) = stored_candles.iter_mut().find(|c| c.timestamp == new_candle.timestamp) {
                // 更新现有数据
                *existing = new_candle.clone();
            } else {
                // 添加新数据
                stored_candles.push(new_candle.clone());
            }
        }

        // 按时间戳排序
        stored_candles.sort_by(|a, b| a.timestamp.cmp(&b.timestamp));

        Ok(())
    }

    async fn get_candles(
        &self,
        trading_pair: &TradingPair,
        timeframe: TimeFrame,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> SigmaXResult<Vec<Candle>> {
        let key = Self::make_key(trading_pair, timeframe);
        let data = self.data.read().await;

        if let Some(candles) = data.get(&key) {
            let filtered_candles: Vec<Candle> = candles
                .iter()
                .filter(|candle| candle.timestamp >= start_time && candle.timestamp <= end_time)
                .cloned()
                .collect();
            Ok(filtered_candles)
        } else {
            Ok(Vec::new())
        }
    }

    async fn delete_candles(
        &self,
        trading_pair: &TradingPair,
        timeframe: TimeFrame,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> SigmaXResult<()> {
        let key = Self::make_key(trading_pair, timeframe);
        let mut data = self.data.write().await;

        if let Some(candles) = data.get_mut(&key) {
            candles.retain(|candle| candle.timestamp < start_time || candle.timestamp > end_time);
        }

        Ok(())
    }
}

impl Default for MemoryStorage {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests;
