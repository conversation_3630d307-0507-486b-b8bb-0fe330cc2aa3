//! 配置管理器
//!
//! 统一管理所有配置服务的生命周期和依赖关系

use std::sync::Arc;
use std::sync::atomic::{AtomicBool, Ordering};
use tokio::sync::RwLock;
use tracing::{info, warn, error};
use futures::future::try_join_all;

use crate::error::{ApiError, ApiResult};
use super::traits::ConfigService;
use super::{
    SystemConfigService, TradingConfigService, RiskConfigService, StrategyConfigService,
    NotificationConfigService, ApiConfigService, CacheConfigService, MonitoringConfigService,
};
use sigmax_database::DatabaseManager;

/// 配置管理器
///
/// 负责统一管理所有配置服务，提供：
/// - 并行初始化所有配置服务
/// - 统一的生命周期管理
/// - 依赖关系协调
/// - 健康检查和监控
pub struct ConfigManager {
    /// 系统配置服务
    pub system_config: Arc<SystemConfigService>,
    /// 交易配置服务
    pub trading_config: Arc<TradingConfigService>,
    /// 风险配置服务
    pub risk_config: Arc<RiskConfigService>,
    /// 策略配置服务
    pub strategy_config: Arc<StrategyConfigService>,
    /// 通知配置服务
    pub notification_config: Arc<NotificationConfigService>,
    /// API配置服务
    pub api_config: Arc<ApiConfigService>,
    /// 缓存配置服务
    pub cache_config: Arc<CacheConfigService>,
    /// 监控配置服务
    pub monitoring_config: Arc<MonitoringConfigService>,
    
    /// 初始化状态
    is_initialized: AtomicBool,
    /// 初始化锁
    init_lock: RwLock<()>,
}

impl ConfigManager {
    /// 创建新的配置管理器
    pub async fn new(database: Arc<DatabaseManager>) -> ApiResult<Self> {
        info!("🎛️ Creating ConfigManager...");

        // 创建所有配置服务
        let system_config = Arc::new(SystemConfigService::new(database.clone()).await?);
        let trading_config = Arc::new(TradingConfigService::new(database.clone()).await?);
        let risk_config = Arc::new(RiskConfigService::new(database.clone()).await?);
        let strategy_config = Arc::new(StrategyConfigService::new(database.clone()).await?);
        let notification_config = Arc::new(NotificationConfigService::new(database.clone()).await?);
        let api_config = Arc::new(ApiConfigService::new(database.clone()).await?);
        let cache_config = Arc::new(CacheConfigService::new(database.clone()).await?);
        let monitoring_config = Arc::new(MonitoringConfigService::new(database.clone()).await?);

        Ok(Self {
            system_config,
            trading_config,
            risk_config,
            strategy_config,
            notification_config,
            api_config,
            cache_config,
            monitoring_config,
            is_initialized: AtomicBool::new(false),
            init_lock: RwLock::new(()),
        })
    }

    /// 并行初始化所有配置服务
    ///
    /// 这是系统启动时的关键方法，会：
    /// 1. 并行初始化所有配置服务
    /// 2. 处理初始化失败的情况
    /// 3. 设置初始化状态
    pub async fn initialize_all(&self) -> ApiResult<()> {
        let _lock = self.init_lock.write().await;
        
        if self.is_initialized.load(Ordering::Relaxed) {
            info!("ConfigManager already initialized, skipping");
            return Ok(());
        }

        info!("🚀 Initializing all configuration services...");

        // 获取所有配置服务
        let services: Vec<(Arc<dyn ConfigService>, &str)> = vec![
            (self.system_config.clone(), "SystemConfig"),
            (self.trading_config.clone(), "TradingConfig"),
            (self.risk_config.clone(), "RiskConfig"),
            (self.strategy_config.clone(), "StrategyConfig"),
            (self.notification_config.clone(), "NotificationConfig"),
            (self.api_config.clone(), "ApiConfig"),
            (self.cache_config.clone(), "CacheConfig"),
            (self.monitoring_config.clone(), "MonitoringConfig"),
        ];

        // 并行初始化所有服务
        let init_futures = services.iter().map(|(service, name)| {
            let service = service.clone();
            let name = *name;
            async move {
                match service.initialize().await {
                    Ok(_) => {
                        info!("✅ {} initialized successfully", name);
                        Ok(())
                    }
                    Err(e) => {
                        error!("❌ {} initialization failed: {}", name, e);
                        Err(ApiError::from(e))
                    }
                }
            }
        });

        // 等待所有初始化完成
        match try_join_all(init_futures).await {
            Ok(_) => {
                self.is_initialized.store(true, Ordering::Relaxed);
                info!("🎉 All configuration services initialized successfully");
                Ok(())
            }
            Err(e) => {
                error!("❌ Configuration services initialization failed: {}", e);
                Err(e)
            }
        }
    }

    /// 重新加载所有配置
    pub async fn reload_all(&self) -> ApiResult<()> {
        info!("🔄 Reloading all configuration services...");

        let services: Vec<Arc<dyn ConfigService>> = vec![
            self.system_config.clone(),
            self.trading_config.clone(),
            self.risk_config.clone(),
            self.strategy_config.clone(),
            self.notification_config.clone(),
            self.api_config.clone(),
            self.cache_config.clone(),
            self.monitoring_config.clone(),
        ];

        let reload_futures = services.iter().map(|service| service.reload());
        
        match try_join_all(reload_futures).await {
            Ok(_) => {
                info!("✅ All configuration services reloaded successfully");
                Ok(())
            }
            Err(e) => {
                error!("❌ Configuration services reload failed: {}", e);
                Err(ApiError::from(e))
            }
        }
    }

    /// 检查是否已初始化
    pub fn is_initialized(&self) -> bool {
        self.is_initialized.load(Ordering::Relaxed)
    }

    /// 健康检查
    pub async fn health_check(&self) -> ApiResult<ConfigManagerHealth> {
        let services: Vec<(&str, Arc<dyn ConfigService>)> = vec![
            ("system", self.system_config.clone()),
            ("trading", self.trading_config.clone()),
            ("risk", self.risk_config.clone()),
            ("strategy", self.strategy_config.clone()),
            ("notification", self.notification_config.clone()),
            ("api", self.api_config.clone()),
            ("cache", self.cache_config.clone()),
            ("monitoring", self.monitoring_config.clone()),
        ];

        let mut health = ConfigManagerHealth::default();
        health.overall_initialized = self.is_initialized();

        for (name, service) in services {
            match service.health_check().await {
                Ok(is_healthy) => {
                    health.service_status.insert(name.to_string(), is_healthy);
                    if is_healthy {
                        health.healthy_services += 1;
                    }
                }
                Err(e) => {
                    warn!("Health check failed for {}: {}", name, e);
                    health.service_status.insert(name.to_string(), false);
                }
            }
        }

        health.total_services = health.service_status.len() as u32;
        health.overall_healthy = health.healthy_services == health.total_services;

        Ok(health)
    }
}

/// 配置管理器健康状态
#[derive(Debug, Clone, serde::Serialize)]
pub struct ConfigManagerHealth {
    /// 整体是否已初始化
    pub overall_initialized: bool,
    /// 整体是否健康
    pub overall_healthy: bool,
    /// 总服务数
    pub total_services: u32,
    /// 健康服务数
    pub healthy_services: u32,
    /// 各服务状态
    pub service_status: std::collections::HashMap<String, bool>,
}

impl Default for ConfigManagerHealth {
    fn default() -> Self {
        Self {
            overall_initialized: false,
            overall_healthy: false,
            total_services: 0,
            healthy_services: 0,
            service_status: std::collections::HashMap::new(),
        }
    }
}
