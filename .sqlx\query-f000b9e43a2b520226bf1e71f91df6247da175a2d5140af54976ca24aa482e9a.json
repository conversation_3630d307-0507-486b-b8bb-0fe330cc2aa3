{"db_name": "PostgreSQL", "query": "\n            INSERT INTO orders (\n                id, strategy_id, trading_pair_id, exchange_id,\n                order_type, side, quantity, price, stop_price, status,\n                filled_quantity, average_price, created_at, updated_at\n            ) VALUES (\n                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14\n            )\n            ON CONFLICT (id) DO UPDATE SET\n                status = EXCLUDED.status,\n                filled_quantity = EXCLUDED.filled_quantity,\n                average_price = EXCLUDED.average_price,\n                updated_at = EXCLUDED.updated_at\n            ", "describe": {"columns": [], "parameters": {"Left": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Int4", "Int4", {"Custom": {"name": "order_type", "kind": {"Enum": ["Market", "Limit", "StopLoss", "StopLimit", "TakeProfit", "TakeProfitLimit"]}}}, {"Custom": {"name": "order_side", "kind": {"Enum": ["Buy", "<PERSON>ll"]}}}, "Numeric", "Numeric", "Numeric", {"Custom": {"name": "order_status", "kind": {"Enum": ["Pending", "Open", "PartiallyFilled", "Filled", "Cancelled", "Rejected", "Expired"]}}}, "Numeric", "Numeric", "Timestamptz", "Timestamptz"]}, "nullable": []}, "hash": "f000b9e43a2b520226bf1e71f91df6247da175a2d5140af54976ca24aa482e9a"}