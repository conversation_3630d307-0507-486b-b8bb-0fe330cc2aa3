#!/bin/bash

# SigmaX-R 风险管理模块重构 - 阶段3部署脚本
# 部署新的风险引擎，与旧系统并行运行

set -e

echo "🚀 开始阶段3部署：新引擎实现"

# 检查环境
echo "📋 检查部署环境..."
if [ ! -f "Cargo.toml" ]; then
    echo "❌ 错误：请在项目根目录运行此脚本"
    exit 1
fi

# 备份当前代码
echo "💾 备份当前代码..."
BACKUP_DIR="backups/phase3_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"
cp -r risk/ "$BACKUP_DIR/"
cp -r web/src/ "$BACKUP_DIR/"
echo "✅ 代码已备份到 $BACKUP_DIR"

# 编译新的风险模块
echo "🔨 编译新的风险管理模块..."
cd risk_new
cargo check
cargo test --lib
cd ..

# 更新 Cargo.toml 依赖
echo "📦 更新项目依赖..."
if ! grep -q "sigmax-risk-new" Cargo.toml; then
    cat >> Cargo.toml << EOF

# 新的风险管理模块（重构版本）
sigmax-risk-new = { path = "risk_new" }
EOF
fi

# 创建特性开关配置
echo "⚙️ 创建特性开关配置..."
cat > config/risk_feature_flags.toml << EOF
# 风险管理模块特性开关配置

[risk_engine]
# 是否启用新的风险引擎
enable_new_engine = false

# 是否启用双写模式（新旧系统并行）
enable_dual_write = true

# 是否启用结果对比
enable_result_comparison = true

# 新系统流量比例 (0-100)
new_engine_traffic_percentage = 0

[monitoring]
# 是否启用详细监控
enable_detailed_monitoring = true

# 是否记录性能指标
enable_performance_metrics = true

# 是否记录一致性检查
enable_consistency_checks = true

[rollback]
# 自动回滚阈值
auto_rollback_error_rate = 5.0

# 自动回滚响应时间阈值（毫秒）
auto_rollback_response_time_ms = 1000
EOF

# 创建监控配置
echo "📊 创建监控配置..."
mkdir -p monitoring/dashboards
cat > monitoring/dashboards/risk_engine_comparison.json << EOF
{
  "dashboard": {
    "title": "风险引擎对比监控",
    "panels": [
      {
        "title": "响应时间对比",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(risk_check_duration_seconds_bucket{engine=\"old\"}[5m]))",
            "legendFormat": "旧引擎 P95"
          },
          {
            "expr": "histogram_quantile(0.95, rate(risk_check_duration_seconds_bucket{engine=\"new\"}[5m]))",
            "legendFormat": "新引擎 P95"
          }
        ]
      },
      {
        "title": "错误率对比",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(risk_check_errors_total{engine=\"old\"}[5m])",
            "legendFormat": "旧引擎错误率"
          },
          {
            "expr": "rate(risk_check_errors_total{engine=\"new\"}[5m])",
            "legendFormat": "新引擎错误率"
          }
        ]
      },
      {
        "title": "结果一致性",
        "type": "stat",
        "targets": [
          {
            "expr": "rate(risk_check_consistency_total[5m]) * 100",
            "legendFormat": "一致性百分比"
          }
        ]
      }
    ]
  }
}
EOF

# 创建数据库迁移脚本
echo "🗄️ 创建数据库迁移脚本..."
mkdir -p database/migrations/phase3
cat > database/migrations/phase3/001_create_new_risk_tables.sql << EOF
-- 新的统一风险规则表
CREATE TABLE IF NOT EXISTS unified_risk_rules_new (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    rule_type VARCHAR(50) NOT NULL,
    parameters JSONB NOT NULL,
    conditions JSONB,
    enabled BOOLEAN NOT NULL DEFAULT true,
    priority INTEGER NOT NULL DEFAULT 100,
    strategy_types TEXT[] DEFAULT '{}',
    trading_pairs TEXT[] DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 规则执行历史表
CREATE TABLE IF NOT EXISTS rule_execution_history_new (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    rule_id UUID NOT NULL REFERENCES unified_risk_rules_new(id),
    execution_time TIMESTAMPTZ NOT NULL,
    result VARCHAR(20) NOT NULL,
    execution_time_ms BIGINT NOT NULL,
    context_summary TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 风险检查结果对比表
CREATE TABLE IF NOT EXISTS risk_check_comparison (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id UUID,
    old_engine_result BOOLEAN,
    new_engine_result BOOLEAN,
    consistent BOOLEAN,
    old_engine_time_ms BIGINT,
    new_engine_time_ms BIGINT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_unified_risk_rules_new_enabled ON unified_risk_rules_new(enabled);
CREATE INDEX IF NOT EXISTS idx_unified_risk_rules_new_strategy_types ON unified_risk_rules_new USING GIN(strategy_types);
CREATE INDEX IF NOT EXISTS idx_unified_risk_rules_new_trading_pairs ON unified_risk_rules_new USING GIN(trading_pairs);
CREATE INDEX IF NOT EXISTS idx_rule_execution_history_new_rule_id ON rule_execution_history_new(rule_id);
CREATE INDEX IF NOT EXISTS idx_rule_execution_history_new_execution_time ON rule_execution_history_new(execution_time);
CREATE INDEX IF NOT EXISTS idx_risk_check_comparison_created_at ON risk_check_comparison(created_at);
CREATE INDEX IF NOT EXISTS idx_risk_check_comparison_consistent ON risk_check_comparison(consistent);
EOF

# 运行数据库迁移
echo "🔄 运行数据库迁移..."
if command -v sqlx &> /dev/null; then
    sqlx migrate run --source database/migrations/phase3
else
    echo "⚠️  警告：sqlx 未安装，请手动运行数据库迁移"
    echo "   文件位置：database/migrations/phase3/001_create_new_risk_tables.sql"
fi

# 创建测试脚本
echo "🧪 创建测试脚本..."
cat > scripts/test_phase3.sh << 'EOF'
#!/bin/bash

echo "🧪 运行阶段3测试..."

# 运行新模块的单元测试
echo "📋 运行新模块单元测试..."
cd risk_new
cargo test --lib

# 运行集成测试
echo "📋 运行集成测试..."
cargo test --test integration_tests

# 运行性能基准测试
echo "📊 运行性能基准测试..."
cargo bench

cd ..

echo "✅ 阶段3测试完成"
EOF

chmod +x scripts/test_phase3.sh

# 创建回滚脚本
echo "🔙 创建回滚脚本..."
cat > scripts/rollback_phase3.sh << EOF
#!/bin/bash

echo "🔙 执行阶段3回滚..."

# 恢复备份
if [ -d "$BACKUP_DIR" ]; then
    echo "📁 恢复代码备份..."
    cp -r "$BACKUP_DIR/risk/" ./
    cp -r "$BACKUP_DIR/web/src/" ./web/
    echo "✅ 代码已恢复"
else
    echo "❌ 错误：找不到备份目录"
    exit 1
fi

# 禁用新功能
echo "⚙️ 禁用新功能..."
sed -i 's/enable_new_engine = true/enable_new_engine = false/' config/risk_feature_flags.toml
sed -i 's/enable_dual_write = true/enable_dual_write = false/' config/risk_feature_flags.toml

# 重启服务
echo "🔄 重启服务..."
# 这里添加具体的服务重启命令

echo "✅ 阶段3回滚完成"
EOF

chmod +x scripts/rollback_phase3.sh

# 编译整个项目
echo "🔨 编译整个项目..."
cargo check

# 运行测试
echo "🧪 运行测试..."
./scripts/test_phase3.sh

echo "✅ 阶段3部署完成！"
echo ""
echo "📋 下一步操作："
echo "1. 监控新旧系统的性能对比"
echo "2. 逐步增加新系统的流量比例"
echo "3. 验证结果一致性"
echo "4. 准备阶段4的功能迁移"
echo ""
echo "📊 监控地址："
echo "- 风险引擎对比监控：http://localhost:3000/d/risk-engine-comparison"
echo "- 系统日志：tail -f logs/sigmax-risk.log"
echo ""
echo "🔙 如需回滚，请运行：./scripts/rollback_phase3.sh"
EOF

chmod +x scripts/deploy_phase3.sh
