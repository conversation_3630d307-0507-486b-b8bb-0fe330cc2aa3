#!/usr/bin/env python3
"""
WebSocket性能监控脚本
实时监控WebSocket服务器性能指标
"""

import requests
import time
import json
from datetime import datetime
import threading
import sys

BASE_URL = "http://127.0.0.1:8080"

class WebSocketPerformanceMonitor:
    def __init__(self):
        self.running = False
        self.stats_history = []
        self.max_history = 60  # 保留60个数据点
        
    def get_websocket_stats(self):
        """获取WebSocket统计信息"""
        try:
            response = requests.get(f"{BASE_URL}/api/v2/websocket/stats", timeout=5)
            if response.status_code == 200:
                return response.json().get('data', {})
            else:
                return None
        except Exception as e:
            print(f"❌ 获取统计信息失败: {e}")
            return None
    
    def get_websocket_config(self):
        """获取WebSocket配置信息"""
        try:
            response = requests.get(f"{BASE_URL}/api/v2/websocket/config", timeout=5)
            if response.status_code == 200:
                return response.json().get('data', {})
            else:
                return None
        except Exception as e:
            print(f"❌ 获取配置信息失败: {e}")
            return None
    
    def get_connections_info(self):
        """获取连接信息"""
        try:
            response = requests.get(f"{BASE_URL}/api/v2/websocket/connections?detailed=true", timeout=5)
            if response.status_code == 200:
                return response.json().get('data', {})
            else:
                return None
        except Exception as e:
            print(f"❌ 获取连接信息失败: {e}")
            return None
    
    def clear_screen(self):
        """清屏"""
        import os
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def format_bytes(self, bytes_value):
        """格式化字节数"""
        if bytes_value is None:
            return "N/A"
        
        for unit in ['B', 'KB', 'MB', 'GB']:
            if bytes_value < 1024.0:
                return f"{bytes_value:.1f} {unit}"
            bytes_value /= 1024.0
        return f"{bytes_value:.1f} TB"
    
    def format_duration(self, seconds):
        """格式化时长"""
        if seconds is None:
            return "N/A"
        
        if seconds < 60:
            return f"{seconds:.1f}s"
        elif seconds < 3600:
            return f"{seconds/60:.1f}m"
        else:
            return f"{seconds/3600:.1f}h"
    
    def display_dashboard(self):
        """显示性能仪表板"""
        self.clear_screen()
        
        # 获取数据
        stats = self.get_websocket_stats()
        config = self.get_websocket_config()
        connections = self.get_connections_info()
        
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        print("🚀 SigmaX-R WebSocket性能监控仪表板")
        print("=" * 80)
        print(f"📅 更新时间: {current_time}")
        print()
        
        if stats:
            # 服务器统计
            print("📊 服务器统计")
            print("-" * 40)
            print(f"  🔗 总连接数: {stats.get('total_connections', 0)}")
            print(f"  📨 总消息数: {stats.get('total_messages_sent', 0)}")
            print(f"  ⏱️ 服务器运行时间: {stats.get('uptime', 'unknown')}")
            print()
            
            # 连接类型统计
            connections_by_type = stats.get('connections_by_type', {})
            print("👥 连接类型分布")
            print("-" * 40)
            for conn_type, count in connections_by_type.items():
                print(f"  {conn_type}: {count} 连接")
            print()
            
            # 频道统计
            channels = stats.get('channels', [])
            if channels:
                print("📺 频道统计")
                print("-" * 40)
                for channel in channels[:5]:  # 显示前5个频道
                    name = channel.get('name', 'unknown')
                    subscribers = channel.get('subscriber_count', 0)
                    messages = channel.get('message_count', 0)
                    print(f"  {name}: {subscribers} 订阅者, {messages} 消息")
                if len(channels) > 5:
                    print(f"  ... 还有 {len(channels) - 5} 个频道")
                print()
        
        if config:
            # 服务器配置
            server_config = config.get('server_config', {})
            print("⚙️ 服务器配置")
            print("-" * 40)
            print(f"  🔢 最大连接数: {server_config.get('max_connections', 'N/A')}")
            print(f"  💓 心跳间隔: {server_config.get('heartbeat_interval', 'N/A')}秒")
            print(f"  ⏱️ 连接超时: {server_config.get('connection_timeout', 'N/A')}秒")
            print(f"  🗜️ 压缩启用: {'是' if server_config.get('compression_enabled', False) else '否'}")
            print(f"  📦 消息队列大小: {server_config.get('message_queue_size', 'N/A')}")
            print()
            
            # 性能配置
            batch_config = server_config.get('batch_config', {})
            if batch_config:
                print("🚀 性能配置")
                print("-" * 40)
                print(f"  📦 批量处理: {'启用' if batch_config.get('enabled', False) else '禁用'}")
                print(f"  📊 批量大小: {batch_config.get('batch_size', 'N/A')}")
                print(f"  ⏱️ 批量超时: {batch_config.get('batch_timeout_ms', 'N/A')}ms")
                print()
        
        if connections:
            # 连接详情
            connection_list = connections.get('connections', [])
            print("🔗 活跃连接详情")
            print("-" * 40)
            if connection_list:
                for i, conn in enumerate(connection_list[:3]):  # 显示前3个连接
                    conn_id = conn.get('connection_id', 'unknown')
                    user_id = conn.get('user_id', 'anonymous')
                    subscriptions = conn.get('subscriptions', [])
                    message_count = conn.get('message_count', 0)
                    print(f"  连接 {i+1}: ID={conn_id[:8]}..., 用户={user_id}")
                    print(f"    📺 订阅: {len(subscriptions)} 个频道")
                    print(f"    📨 消息: {message_count} 条")
                if len(connection_list) > 3:
                    print(f"  ... 还有 {len(connection_list) - 3} 个连接")
            else:
                print("  暂无活跃连接")
            print()
        
        # 性能指标趋势
        if len(self.stats_history) > 1:
            print("📈 性能趋势 (最近10个数据点)")
            print("-" * 40)
            recent_stats = self.stats_history[-10:]
            
            # 连接数趋势
            connections_trend = [s.get('total_connections', 0) for s in recent_stats]
            print(f"  🔗 连接数: {' → '.join(map(str, connections_trend))}")
            
            # 消息数趋势
            messages_trend = [s.get('total_messages_sent', 0) for s in recent_stats]
            if len(set(messages_trend)) > 1:  # 如果有变化
                print(f"  📨 消息数: {' → '.join(map(str, messages_trend))}")
            print()
        
        # 系统状态
        print("🎯 系统状态")
        print("-" * 40)
        if stats:
            total_connections = stats.get('total_connections', 0)
            max_connections = config.get('server_config', {}).get('max_connections', 10000) if config else 10000
            
            connection_usage = (total_connections / max_connections) * 100
            
            if connection_usage < 50:
                status = "🟢 良好"
            elif connection_usage < 80:
                status = "🟡 正常"
            else:
                status = "🔴 繁忙"
            
            print(f"  📊 连接使用率: {connection_usage:.1f}% ({total_connections}/{max_connections})")
            print(f"  🎯 系统状态: {status}")
            
            # 性能评级
            if connection_usage < 30:
                performance = "🌟 优秀"
            elif connection_usage < 60:
                performance = "👍 良好"
            elif connection_usage < 80:
                performance = "⚠️ 一般"
            else:
                performance = "❌ 需要优化"
            
            print(f"  🏆 性能评级: {performance}")
        else:
            print("  ❌ 无法获取系统状态")
        
        print()
        print("💡 按 Ctrl+C 退出监控")
        print("🔄 每5秒自动刷新...")
    
    def run_monitor(self):
        """运行监控"""
        self.running = True
        print("🚀 启动WebSocket性能监控...")
        print("📊 正在收集初始数据...")
        
        try:
            while self.running:
                # 获取统计数据
                stats = self.get_websocket_stats()
                if stats:
                    # 添加时间戳
                    stats['timestamp'] = datetime.now().isoformat()
                    self.stats_history.append(stats)
                    
                    # 保持历史记录大小
                    if len(self.stats_history) > self.max_history:
                        self.stats_history.pop(0)
                
                # 显示仪表板
                self.display_dashboard()
                
                # 等待5秒
                time.sleep(5)
                
        except KeyboardInterrupt:
            print("\n\n🛑 监控已停止")
            self.running = False
        except Exception as e:
            print(f"\n\n❌ 监控出错: {e}")
            self.running = False

def main():
    print("🚀 SigmaX-R WebSocket性能监控器")
    print("=" * 50)
    
    # 检查服务器连接
    try:
        response = requests.get(f"{BASE_URL}/api/health", timeout=5)
        if response.status_code != 200:
            print("❌ 无法连接到SigmaX-R服务器")
            print(f"请确保服务器运行在 {BASE_URL}")
            return
    except Exception as e:
        print(f"❌ 服务器连接失败: {e}")
        print(f"请确保服务器运行在 {BASE_URL}")
        return
    
    print("✅ 服务器连接正常")
    print("🔄 开始性能监控...")
    print()
    
    monitor = WebSocketPerformanceMonitor()
    monitor.run_monitor()

if __name__ == "__main__":
    main()
