//! 通知配置服务
//!
//! 专门负责通知相关配置的管理

use std::sync::Arc;
use std::sync::atomic::{AtomicBool, Ordering};
use async_trait::async_trait;
use tracing::{info, error};

use crate::error::{ApiError, ApiResult};
use super::traits::{ConfigService, ConfigStatistics};
use sigmax_core::{NotificationConfig, ConfigCache};
use sigmax_database::DatabaseManager;
use sigmax_database::repositories::sqlx::SqlNotificationRepository;
use sigmax_database::repositories::traits::NotificationRepository;

/// 通知配置服务
///
/// 负责管理通知相关配置，包括：
/// - 邮件通知设置
/// - 短信通知设置
/// - Webhook通知设置
/// - 通知级别和频率控制
#[derive(Clone)]
pub struct NotificationConfigService {
    /// 通知仓储 - 使用专门的通知仓储
    repository: Arc<SqlNotificationRepository>,
    /// 配置缓存
    cache: Arc<ConfigCache>,
    /// 初始化状态
    is_initialized: Arc<AtomicBool>,
}

impl NotificationConfigService {
    /// 创建新的通知配置服务
    pub async fn new(database: Arc<DatabaseManager>) -> ApiResult<Self> {
        let repository = Arc::new(SqlNotificationRepository::new(database));
        let cache = Arc::new(ConfigCache::new(std::time::Duration::from_secs(300))); // 5分钟缓存

        Ok(Self {
            repository,
            cache,
            is_initialized: Arc::new(AtomicBool::new(false)),
        })
    }

    /// 获取通知配置
    pub async fn get_config(&self) -> ApiResult<NotificationConfig> {
        if !self.is_initialized() {
            return Err(ApiError::BadRequest("NotificationConfigService not initialized".to_string()));
        }

        self.cache.get_or_load_notification_config(|| {
            self.repository.get_notification_config()
        })
        .await
        .map_err(|e| {
            error!("Failed to get notification config: {}", e);
            ApiError::from(e)
        })
    }

    /// 保存通知配置
    pub async fn save_config(&self, config: &NotificationConfig) -> ApiResult<()> {
        if !self.is_initialized() {
            return Err(ApiError::BadRequest("NotificationConfigService not initialized".to_string()));
        }

        // 验证配置
        self.validate_config(config)?;

        // 保存到数据库
        self.repository
            .save_notification_config(config)
            .await
            .map_err(|e| {
                error!("Failed to save notification config: {}", e);
                ApiError::from(e)
            })?;

        // 清除缓存
        self.cache.clear_config_cache("notifications").await;
        info!("Notification config saved successfully and cache cleared");
        
        Ok(())
    }

    /// 重置通知配置为默认值
    pub async fn reset_config(&self) -> ApiResult<()> {
        if !self.is_initialized() {
            return Err(ApiError::BadRequest("NotificationConfigService not initialized".to_string()));
        }

        // 重置到默认配置
        self.repository
            .reset_notification_config()
            .await
            .map_err(|e| {
                error!("Failed to reset notification config: {}", e);
                ApiError::from(e)
            })?;

        // 清除缓存
        self.cache.clear_config_cache("notifications").await;
        info!("Notification config reset to default and cache cleared");

        Ok(())
    }

    /// 验证通知配置
    fn validate_config(&self, config: &NotificationConfig) -> ApiResult<()> {
        // 验证Webhook URL格式（如果提供）
        if let Some(webhook_url) = &config.webhook_url {
            if !webhook_url.trim().is_empty() {
                // 简单的URL格式验证
                if !webhook_url.starts_with("http://") && !webhook_url.starts_with("https://") {
                    return Err(ApiError::BadRequest("Webhook URL must start with http:// or https://".to_string()));
                }
            }
        }

        // 验证Slack Webhook URL格式（如果提供）
        if let Some(slack_webhook) = &config.slack_webhook {
            if !slack_webhook.trim().is_empty() {
                if !slack_webhook.starts_with("https://hooks.slack.com/") {
                    return Err(ApiError::BadRequest("Slack webhook URL must start with https://hooks.slack.com/".to_string()));
                }
            }
        }

        // 验证Telegram配置的一致性
        let has_telegram_token = config.telegram_bot_token.as_ref().map_or(false, |t| !t.trim().is_empty());
        let has_telegram_chat = config.telegram_chat_id.as_ref().map_or(false, |c| !c.trim().is_empty());

        if has_telegram_token && !has_telegram_chat {
            return Err(ApiError::BadRequest("Telegram chat ID is required when bot token is provided".to_string()));
        }

        if has_telegram_chat && !has_telegram_token {
            return Err(ApiError::BadRequest("Telegram bot token is required when chat ID is provided".to_string()));
        }

        Ok(())
    }
}

#[async_trait]
impl ConfigService for NotificationConfigService {
    async fn initialize(&self) -> ApiResult<()> {
        info!("🔧 Initializing NotificationConfigService...");

        // 预加载通知配置到缓存
        match self.cache.get_or_load_notification_config(|| {
            self.repository.get_notification_config()
        }).await {
            Ok(_) => {
                self.is_initialized.store(true, Ordering::Relaxed);
                info!("✅ NotificationConfigService initialized successfully");
                Ok(())
            }
            Err(e) => {
                error!("❌ NotificationConfigService initialization failed: {}", e);
                Err(ApiError::from(e))
            }
        }
    }

    async fn reload(&self) -> ApiResult<()> {
        info!("🔄 Reloading NotificationConfigService...");
        
        // 清除缓存
        self.cache.clear_config_cache("notifications").await;
        
        // 重新加载配置
        self.cache.get_or_load_notification_config(|| {
            self.repository.get_notification_config()
        })
        .await
        .map_err(|e| {
            error!("Failed to reload notification config: {}", e);
            ApiError::from(e)
        })?;

        info!("✅ NotificationConfigService reloaded successfully");
        Ok(())
    }

    fn is_initialized(&self) -> bool {
        self.is_initialized.load(Ordering::Relaxed)
    }

    fn service_name(&self) -> &'static str {
        "NotificationConfigService"
    }

    async fn clear_cache(&self) -> ApiResult<()> {
        self.cache.clear_config_cache("notifications").await;
        Ok(())
    }

    async fn get_statistics(&self) -> ApiResult<ConfigStatistics> {
        Ok(ConfigStatistics {
            config_count: 1,
            cache_hit_rate: 0.0, // TODO: 实现缓存命中率统计
            last_updated: None, // TODO: 实现最后更新时间跟踪
            is_initialized: self.is_initialized(),
        })
    }
}
