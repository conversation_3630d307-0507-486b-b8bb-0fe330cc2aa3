//! 熔断器模块 - 实盘交易保护机制

use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use sigmax_core::{SigmaXResult, SigmaXError};
use serde::{Serialize, Deserialize};
use tracing::{debug, warn, error};

/// 熔断器状态
#[derive(Debug, <PERSON>lone, PartialEq, Serialize)]
pub enum CircuitBreakerState {
    /// 关闭状态 - 正常工作
    Closed,
    /// 打开状态 - 拒绝所有请求
    Open,
    /// 半开状态 - 允许少量请求测试
    HalfOpen,
}

/// 熔断器统计信息
#[derive(Debug, Clone, Serialize)]
pub struct CircuitBreakerStats {
    pub state: CircuitBreakerState,
    pub failure_count: u64,
    pub success_count: u64,
    pub total_requests: u64,
    #[serde(skip)]
    pub last_failure_time: Option<Instant>,
    #[serde(skip)]
    pub last_state_change: Instant,
    pub trip_count: u64,
}

impl CircuitBreakerStats {
    pub fn new() -> Self {
        Self {
            state: CircuitBreakerState::Closed,
            failure_count: 0,
            success_count: 0,
            total_requests: 0,
            last_failure_time: None,
            last_state_change: Instant::now(),
            trip_count: 0,
        }
    }
    
    /// 计算失败率
    pub fn failure_rate(&self) -> f64 {
        if self.total_requests > 0 {
            self.failure_count as f64 / self.total_requests as f64
        } else {
            0.0
        }
    }
    
    /// 计算成功率
    pub fn success_rate(&self) -> f64 {
        if self.total_requests > 0 {
            self.success_count as f64 / self.total_requests as f64
        } else {
            0.0
        }
    }
}

/// 熔断器实现
pub struct CircuitBreaker {
    /// 内部状态
    stats: Arc<RwLock<CircuitBreakerStats>>,
    /// 失败率阈值
    failure_threshold: f64,
    /// 恢复超时时间
    recovery_timeout: Duration,
    /// 最小请求数量 (开始统计失败率的最小请求数)
    min_requests: u64,
    /// 半开状态下的测试请求数
    half_open_max_calls: u64,
}

impl CircuitBreaker {
    /// 创建新的熔断器
    pub fn new(
        failure_threshold: f64,
        recovery_timeout: Duration,
        min_requests: u64,
    ) -> Self {
        Self {
            stats: Arc::new(RwLock::new(CircuitBreakerStats::new())),
            failure_threshold,
            recovery_timeout,
            min_requests,
            half_open_max_calls: 3, // 半开状态下允许3个测试请求
        }
    }
    
    /// 检查是否可以执行请求
    pub async fn can_execute(&self) -> bool {
        let mut stats = self.stats.write().await;
        
        match stats.state {
            CircuitBreakerState::Closed => true,
            CircuitBreakerState::Open => {
                // 检查是否到了恢复时间
                if let Some(last_failure) = stats.last_failure_time {
                    if last_failure.elapsed() >= self.recovery_timeout {
                        // 转换到半开状态
                        stats.state = CircuitBreakerState::HalfOpen;
                        stats.last_state_change = Instant::now();
                        stats.failure_count = 0;
                        stats.success_count = 0;
                        stats.total_requests = 0;
                        
                        debug!("Circuit breaker transitioned to HalfOpen state");
                        true
                    } else {
                        false
                    }
                } else {
                    false
                }
            }
            CircuitBreakerState::HalfOpen => {
                // 半开状态下限制请求数量
                stats.total_requests < self.half_open_max_calls
            }
        }
    }
    
    /// 执行带熔断器保护的操作
    pub async fn execute<F, Fut, T>(&self, operation: F) -> SigmaXResult<T>
    where
        F: FnOnce() -> Fut,
        Fut: std::future::Future<Output = SigmaXResult<T>>,
    {
        if !self.can_execute().await {
            return Err(SigmaXError::CircuitBreakerOpen(
                "Circuit breaker is open, request rejected".to_string()
            ));
        }
        
        let result = operation().await;
        
        match &result {
            Ok(_) => self.record_success().await,
            Err(_) => self.record_failure().await,
        }
        
        result
    }
    
    /// 记录成功
    pub async fn record_success(&self) {
        let mut stats = self.stats.write().await;
        stats.success_count += 1;
        stats.total_requests += 1;
        
        match stats.state {
            CircuitBreakerState::HalfOpen => {
                // 半开状态下，如果成功请求达到阈值，转换到关闭状态
                if stats.success_count >= self.half_open_max_calls {
                    stats.state = CircuitBreakerState::Closed;
                    stats.last_state_change = Instant::now();
                    stats.failure_count = 0;
                    stats.success_count = 0;
                    stats.total_requests = 0;
                    
                    debug!("Circuit breaker transitioned to Closed state after successful recovery");
                }
            }
            _ => {}
        }
    }
    
    /// 记录失败
    pub async fn record_failure(&self) {
        let mut stats = self.stats.write().await;
        stats.failure_count += 1;
        stats.total_requests += 1;
        stats.last_failure_time = Some(Instant::now());
        
        // 检查是否需要打开熔断器
        if stats.total_requests >= self.min_requests {
            let failure_rate = stats.failure_rate();
            
            if failure_rate >= self.failure_threshold {
                match stats.state {
                    CircuitBreakerState::Closed => {
                        stats.state = CircuitBreakerState::Open;
                        stats.last_state_change = Instant::now();
                        stats.trip_count += 1;
                        
                        warn!(
                            "Circuit breaker opened due to high failure rate: {:.2}% (threshold: {:.2}%)",
                            failure_rate * 100.0,
                            self.failure_threshold * 100.0
                        );
                    }
                    CircuitBreakerState::HalfOpen => {
                        // 半开状态下失败，立即回到打开状态
                        stats.state = CircuitBreakerState::Open;
                        stats.last_state_change = Instant::now();
                        stats.trip_count += 1;
                        
                        warn!("Circuit breaker reopened due to failure in HalfOpen state");
                    }
                    _ => {}
                }
            }
        }
    }
    
    /// 强制打开熔断器
    pub async fn force_open(&self) {
        let mut stats = self.stats.write().await;
        if stats.state != CircuitBreakerState::Open {
            stats.state = CircuitBreakerState::Open;
            stats.last_state_change = Instant::now();
            stats.last_failure_time = Some(Instant::now());
            stats.trip_count += 1;
            
            error!("Circuit breaker force opened");
        }
    }
    
    /// 强制关闭熔断器
    pub async fn force_close(&self) {
        let mut stats = self.stats.write().await;
        stats.state = CircuitBreakerState::Closed;
        stats.last_state_change = Instant::now();
        stats.failure_count = 0;
        stats.success_count = 0;
        stats.total_requests = 0;
        
        debug!("Circuit breaker force closed");
    }
    
    /// 重置熔断器
    pub async fn reset(&self) {
        let mut stats = self.stats.write().await;
        *stats = CircuitBreakerStats::new();
        
        debug!("Circuit breaker reset");
    }
    
    /// 检查熔断器是否打开
    pub async fn is_open(&self) -> bool {
        let stats = self.stats.read().await;
        stats.state == CircuitBreakerState::Open
    }
    
    /// 检查熔断器是否关闭
    pub async fn is_closed(&self) -> bool {
        let stats = self.stats.read().await;
        stats.state == CircuitBreakerState::Closed
    }
    
    /// 检查熔断器是否半开
    pub async fn is_half_open(&self) -> bool {
        let stats = self.stats.read().await;
        stats.state == CircuitBreakerState::HalfOpen
    }
    
    /// 获取当前状态
    pub async fn get_state(&self) -> CircuitBreakerState {
        let stats = self.stats.read().await;
        stats.state.clone()
    }
    
    /// 获取统计信息
    pub async fn get_stats(&self) -> CircuitBreakerStats {
        let stats = self.stats.read().await;
        stats.clone()
    }
    
    /// 获取配置信息
    pub fn get_config(&self) -> CircuitBreakerConfig {
        CircuitBreakerConfig {
            failure_threshold: self.failure_threshold,
            recovery_timeout: self.recovery_timeout,
            min_requests: self.min_requests,
            half_open_max_calls: self.half_open_max_calls,
        }
    }
}

/// 熔断器配置
#[derive(Debug, Clone)]
pub struct CircuitBreakerConfig {
    pub failure_threshold: f64,
    pub recovery_timeout: Duration,
    pub min_requests: u64,
    pub half_open_max_calls: u64,
}

impl Default for CircuitBreakerConfig {
    fn default() -> Self {
        Self {
            failure_threshold: 0.5, // 50%失败率
            recovery_timeout: Duration::from_secs(60), // 1分钟恢复时间
            min_requests: 10, // 最少10个请求
            half_open_max_calls: 3, // 半开状态3个测试请求
        }
    }
}

/// 熔断器构建器
pub struct CircuitBreakerBuilder {
    config: CircuitBreakerConfig,
}

impl CircuitBreakerBuilder {
    pub fn new() -> Self {
        Self {
            config: CircuitBreakerConfig::default(),
        }
    }
    
    pub fn failure_threshold(mut self, threshold: f64) -> Self {
        self.config.failure_threshold = threshold;
        self
    }
    
    pub fn recovery_timeout(mut self, timeout: Duration) -> Self {
        self.config.recovery_timeout = timeout;
        self
    }
    
    pub fn min_requests(mut self, min_requests: u64) -> Self {
        self.config.min_requests = min_requests;
        self
    }
    
    pub fn half_open_max_calls(mut self, max_calls: u64) -> Self {
        self.config.half_open_max_calls = max_calls;
        self
    }
    
    pub fn build(self) -> CircuitBreaker {
        let mut breaker = CircuitBreaker::new(
            self.config.failure_threshold,
            self.config.recovery_timeout,
            self.config.min_requests,
        );
        breaker.half_open_max_calls = self.config.half_open_max_calls;
        breaker
    }
}

impl Default for CircuitBreakerBuilder {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::time::{sleep, Duration};

    #[tokio::test]
    async fn test_circuit_breaker_basic_flow() {
        let breaker = CircuitBreaker::new(0.5, Duration::from_millis(100), 2);
        
        // 初始状态应该是关闭的
        assert!(breaker.is_closed().await);
        assert!(breaker.can_execute().await);
        
        // 记录一些成功
        breaker.record_success().await;
        assert!(breaker.is_closed().await);
        
        // 记录失败，但还没达到阈值
        breaker.record_failure().await;
        assert!(breaker.is_closed().await);
        
        // 再记录一个失败，达到50%失败率，应该打开
        breaker.record_failure().await;
        assert!(breaker.is_open().await);
        assert!(!breaker.can_execute().await);
    }
    
    #[tokio::test]
    async fn test_circuit_breaker_recovery() {
        let breaker = CircuitBreaker::new(0.5, Duration::from_millis(50), 2);
        
        // 触发熔断器打开
        breaker.record_failure().await;
        breaker.record_failure().await;
        assert!(breaker.is_open().await);
        
        // 等待恢复时间
        sleep(Duration::from_millis(60)).await;
        
        // 现在应该可以执行了（转换到半开状态）
        assert!(breaker.can_execute().await);
        assert!(breaker.is_half_open().await);
        
        // 记录成功，应该关闭熔断器
        for _ in 0..3 {
            breaker.record_success().await;
        }
        assert!(breaker.is_closed().await);
    }
    
    #[tokio::test]
    async fn test_circuit_breaker_execute() {
        let breaker = CircuitBreaker::new(0.5, Duration::from_millis(100), 1);
        
        // 成功的操作
        let result = breaker.execute(|| async {
            Ok::<i32, sigmax_core::SigmaXError>(42)
        }).await;
        assert!(result.is_ok());
        assert_eq!(result.expect("Circuit breaker should allow successful operation"), 42);
        
        // 失败的操作
        let result = breaker.execute(|| async {
            Err::<i32, sigmax_core::SigmaXError>(sigmax_core::SigmaXError::Generic("test error".to_string()))
        }).await;
        assert!(result.is_err());
        
        // 熔断器应该打开
        assert!(breaker.is_open().await);
        
        // 下一个请求应该被拒绝
        let result = breaker.execute(|| async {
            Ok::<i32, sigmax_core::SigmaXError>(42)
        }).await;
        assert!(result.is_err());
        
        // 检查错误类型
        match result.expect_err("Circuit breaker should reject when open") {
            sigmax_core::SigmaXError::CircuitBreakerOpen(_) => {},
            _ => panic!("Expected CircuitBreakerOpen error"),
        }
    }
    
    #[test]
    fn test_circuit_breaker_stats() {
        let mut stats = CircuitBreakerStats::new();
        stats.success_count = 8;
        stats.failure_count = 2;
        stats.total_requests = 10;
        
        assert_eq!(stats.success_rate(), 0.8);
        assert_eq!(stats.failure_rate(), 0.2);
    }
    
    #[test]
    fn test_circuit_breaker_builder() {
        let breaker = CircuitBreakerBuilder::new()
            .failure_threshold(0.3)
            .recovery_timeout(Duration::from_secs(30))
            .min_requests(5)
            .half_open_max_calls(2)
            .build();
        
        let config = breaker.get_config();
        assert_eq!(config.failure_threshold, 0.3);
        assert_eq!(config.recovery_timeout, Duration::from_secs(30));
        assert_eq!(config.min_requests, 5);
        assert_eq!(config.half_open_max_calls, 2);
    }
}
