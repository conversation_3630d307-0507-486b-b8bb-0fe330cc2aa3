//! 订单验证服务
//!
//! 负责验证订单是否符合风控要求

use sigmax_core::Order;
use sigmax_interfaces::risk::RiskResult;
use sigmax_interfaces::RiskContext;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use std::collections::HashMap;

/// 订单验证配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrderValidatorConfig {
    /// 最大单笔订单金额
    pub max_order_amount: Decimal,
    /// 最小订单金额
    pub min_order_amount: Decimal,
    /// 最大杠杆倍数
    pub max_leverage: Decimal,
    /// 是否启用价格偏离检查
    pub enable_price_deviation_check: bool,
    /// 最大价格偏离百分比
    pub max_price_deviation_percent: Decimal,
    /// 是否启用流动性检查
    pub enable_liquidity_check: bool,
}

impl Default for OrderValidatorConfig {
    fn default() -> Self {
        Self {
            max_order_amount: Decimal::from(1_000_000),
            min_order_amount: Decimal::from(1),
            max_leverage: Decimal::from(10),
            enable_price_deviation_check: true,
            max_price_deviation_percent: Decimal::from(5),
            enable_liquidity_check: true,
        }
    }
}

/// 订单验证服务
pub struct OrderValidator {
    config: OrderValidatorConfig,
}

impl OrderValidator {
    /// 创建新的订单验证器
    pub fn new(config: OrderValidatorConfig) -> Self {
        Self { config }
    }

    /// 使用默认配置创建订单验证器
    pub fn with_default_config() -> Self {
        Self::new(OrderValidatorConfig::default())
    }

    /// 验证订单基本信息
    pub async fn validate_order(&self, order: &Order, context: &sigmax_interfaces::risk::RiskContext) -> OrderValidationResult {
        let mut validation_result = OrderValidationResult {
            is_valid: true,
            message: "Order validation passed".to_string(),
            details: HashMap::new(),
            validated_at: Utc::now(),
        };

        // 1. 验证订单金额
        if let Err(error) = self.validate_order_amount(order) {
            validation_result.is_valid = false;
            validation_result.message = error.to_string();
            validation_result.details.insert(
                "amount_check".to_string(),
                serde_json::json!({
                    "passed": false,
                    "reason": error.to_string()
                })
            );
            return validation_result;
        }

        // 2. 验证价格偏离
        if self.config.enable_price_deviation_check {
            if let Err(error) = self.validate_price_deviation(order, context).await {
                validation_result.is_valid = false;
                validation_result.message = error.to_string();
                validation_result.details.insert(
                    "price_deviation_check".to_string(),
                    serde_json::json!({
                        "passed": false,
                        "reason": error.to_string()
                    })
                );
                return validation_result;
            }
        }

        // 3. 验证账户余额
        if let Err(error) = self.validate_account_balance(order, &context.balances) {
            validation_result.is_valid = false;
            validation_result.message = error.to_string();
            validation_result.details.insert(
                "balance_check".to_string(),
                serde_json::json!({
                    "passed": false,
                    "reason": error.to_string()
                })
            );
            return validation_result;
        }

        // 4. 验证流动性
        if self.config.enable_liquidity_check {
            if let Err(error) = self.validate_liquidity(order, context).await {
                validation_result.is_valid = false;
                validation_result.message = error.to_string();
                validation_result.details.insert(
                    "liquidity_check".to_string(),
                    serde_json::json!({
                        "passed": false,
                        "reason": error.to_string()
                    })
                );
                return validation_result;
            }
        }

        // 添加成功的验证详情
        validation_result.details.insert(
            "amount_check".to_string(),
            serde_json::json!({"passed": true})
        );
        validation_result.details.insert(
            "balance_check".to_string(),
            serde_json::json!({"passed": true})
        );

        validation_result
    }

    /// 验证订单金额
    fn validate_order_amount(&self, order: &Order) -> Result<(), OrderValidationError> {
        let order_value = if let Some(price) = order.price {
            order.quantity * price
        } else {
            // 对于市价单，使用数量作为近似值
            order.quantity
        };

        if order_value > self.config.max_order_amount {
            return Err(OrderValidationError::ValidationFailed {
                reason: format!(
                    "Order amount {} exceeds maximum allowed {}",
                    order_value, self.config.max_order_amount
                ),
            });
        }

        if order_value < self.config.min_order_amount {
            return Err(OrderValidationError::ValidationFailed {
                reason: format!(
                    "Order amount {} below minimum required {}",
                    order_value, self.config.min_order_amount
                ),
            });
        }

        Ok(())
    }

    /// 验证价格偏离
    async fn validate_price_deviation(&self, order: &Order, context: &RiskContext) -> Result<(), OrderValidationError> {
        if let Some(order_price) = order.price {
            if let Some(market_data) = &context.market_data {
                let current_price = market_data.price;
                let deviation = ((order_price - current_price).abs() / current_price) * Decimal::from(100);

                if deviation > self.config.max_price_deviation_percent {
                    return Err(OrderValidationError::ValidationFailed {
                        reason: format!(
                            "Price deviation {}% exceeds maximum allowed {}%",
                            deviation, self.config.max_price_deviation_percent
                        ),
                    });
                }
            }
        }
        Ok(())
    }

    /// 验证账户余额
    fn validate_account_balance(&self, order: &Order, balances: &[sigmax_core::Balance]) -> Result<(), OrderValidationError> {
        let required_asset = match order.side {
            sigmax_core::OrderSide::Buy => &order.trading_pair.quote,
            sigmax_core::OrderSide::Sell => &order.trading_pair.base,
        };

        let required_amount = match order.side {
            sigmax_core::OrderSide::Buy => {
                if let Some(price) = order.price {
                    order.quantity * price
                } else {
                    // 对于市价买单，需要更复杂的计算
                    order.quantity * Decimal::from(50000) // 假设一个保守的价格
                }
            },
            sigmax_core::OrderSide::Sell => order.quantity,
        };

        let available_balance = balances
            .iter()
            .find(|b| b.asset == *required_asset)
            .map(|b| b.free) // 使用 free 字段而不是 available
            .unwrap_or_default();

        if available_balance < required_amount {
            return Err(OrderValidationError::ValidationFailed {
                reason: format!(
                    "Insufficient balance. Required: {} {}, Available: {} {}",
                    required_amount, required_asset, available_balance, required_asset
                ),
            });
        }

        Ok(())
    }

    /// 验证流动性
    async fn validate_liquidity(&self, order: &Order, context: &RiskContext) -> Result<(), OrderValidationError> {
        if let Some(market_data) = &context.market_data {
            // 简单的流动性检查：订单量不应超过当前买卖盘深度的一定比例
            let max_order_ratio = Decimal::from_str_exact("0.1").unwrap(); // 10%

            let order_size = order.quantity;
            let market_depth = match order.side {
                sigmax_core::OrderSide::Buy => market_data.ask_size.unwrap_or_default(),
                sigmax_core::OrderSide::Sell => market_data.bid_size.unwrap_or_default(),
            };

            if market_depth > Decimal::ZERO && order_size > market_depth * max_order_ratio {
                return Err(OrderValidationError::ValidationFailed {
                    reason: format!(
                        "Order size {} exceeds {}% of market depth {}",
                        order_size, max_order_ratio * Decimal::from(100), market_depth
                    ),
                });
            }
        }
        Ok(())
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrderValidationResult {
    pub is_valid: bool,
    pub message: String,
    pub details: HashMap<String, serde_json::Value>,
    pub validated_at: DateTime<Utc>,
}

impl OrderValidationResult {
    /// 转换为风控结果
    pub fn to_risk_result(&self) -> RiskResult {
        if self.is_valid {
            RiskResult::Approved
        } else {
            RiskResult::Rejected(self.message.clone())
        }
    }
}

#[derive(Debug, thiserror::Error)]
pub enum OrderValidationError {
    #[error("Validation failed: {reason}")]
    ValidationFailed { reason: String },

    #[error("Market data unavailable")]
    MarketDataUnavailable,

    #[error("Configuration error: {message}")]
    ConfigurationError { message: String },
}