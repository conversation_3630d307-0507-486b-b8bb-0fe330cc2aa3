<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SigmaX Trading System - Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/custom.css">
</head>
<body class="bg-gray-100">
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <!-- Logo和标题 -->
            <div class="text-center">
                <div class="mx-auto h-20 w-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center mb-4">
                    <i class="fas fa-chart-line text-white text-3xl"></i>
                </div>
                <h2 class="text-3xl font-bold gradient-text">SigmaX Trading System</h2>
                <p class="mt-2 text-gray-600">现代化交易系统前端演示</p>
            </div>

            <!-- 功能展示卡片 -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-lg font-semibold mb-4">前端架构特性</h3>
                <div class="space-y-3">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-check text-green-600"></i>
                        </div>
                        <span class="text-gray-700">响应式设计</span>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-check text-blue-600"></i>
                        </div>
                        <span class="text-gray-700">模块化组件</span>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-check text-purple-600"></i>
                        </div>
                        <span class="text-gray-700">路由管理</span>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-check text-yellow-600"></i>
                        </div>
                        <span class="text-gray-700">工具函数库</span>
                    </div>
                </div>
            </div>

            <!-- 技术栈 -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-lg font-semibold mb-4">技术栈</h3>
                <div class="grid grid-cols-2 gap-4">
                    <div class="text-center p-3 bg-gray-50 rounded-lg">
                        <i class="fab fa-html5 text-orange-500 text-2xl mb-2"></i>
                        <div class="text-sm font-medium">HTML5</div>
                    </div>
                    <div class="text-center p-3 bg-gray-50 rounded-lg">
                        <i class="fab fa-css3-alt text-blue-500 text-2xl mb-2"></i>
                        <div class="text-sm font-medium">CSS3</div>
                    </div>
                    <div class="text-center p-3 bg-gray-50 rounded-lg">
                        <i class="fab fa-js-square text-yellow-500 text-2xl mb-2"></i>
                        <div class="text-sm font-medium">JavaScript</div>
                    </div>
                    <div class="text-center p-3 bg-gray-50 rounded-lg">
                        <i class="fas fa-wind text-cyan-500 text-2xl mb-2"></i>
                        <div class="text-sm font-medium">Tailwind</div>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="space-y-4">
                <button onclick="enterMainApp()" class="w-full btn btn-primary py-3 text-lg">
                    <i class="fas fa-rocket mr-2"></i>
                    进入主应用
                </button>

                <div class="grid grid-cols-2 gap-4">
                    <button onclick="showDemo('notification')" class="btn btn-success py-2">
                        <i class="fas fa-bell mr-2"></i>
                        通知演示
                    </button>
                    <button onclick="showDemo('confirm')" class="btn btn-warning py-2">
                        <i class="fas fa-question-circle mr-2"></i>
                        确认框演示
                    </button>
                </div>
            </div>

            <!-- 系统状态 -->
            <div class="bg-white rounded-lg shadow-lg p-4">
                <div class="flex items-center justify-between">
                    <span class="text-gray-600">系统状态</span>
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                        <span class="text-sm text-green-600">正常运行</span>
                    </div>
                </div>
            </div>

            <!-- 版本信息 -->
            <div class="text-center text-sm text-gray-500">
                <p>SigmaX Trading System v1.0.0</p>
                <p>前端架构演示版本</p>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/utils.js"></script>
    <script>
        // 进入主应用
        function enterMainApp() {
            // 显示加载动画
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>加载中...';
            button.disabled = true;

            // 模拟加载过程
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 1500);
        }

        // 演示功能
        function showDemo(type) {
            switch(type) {
                case 'notification':
                    Utils.showNotification('这是一个成功通知！', 'success');
                    setTimeout(() => {
                        Utils.showNotification('这是一个警告通知！', 'warning');
                    }, 1000);
                    setTimeout(() => {
                        Utils.showNotification('这是一个错误通知！', 'error');
                    }, 2000);
                    break;

                case 'confirm':
                    Utils.confirm('您确定要执行此操作吗？', '确认操作').then(result => {
                        if (result) {
                            Utils.showNotification('操作已确认！', 'success');
                        } else {
                            Utils.showNotification('操作已取消！', 'info');
                        }
                    });
                    break;
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 添加一些动画效果
            const cards = document.querySelectorAll('.bg-white');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 200);
            });

            // 显示欢迎通知
            setTimeout(() => {
                Utils.showNotification('欢迎使用 SigmaX Trading System！', 'info', 5000);
            }, 2000);
        });

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            // 按 Enter 键进入主应用
            if (e.key === 'Enter') {
                enterMainApp();
            }
            // 按 Escape 键显示帮助
            if (e.key === 'Escape') {
                Utils.showNotification('按 Enter 键进入主应用', 'info');
            }
        });
    </script>
</body>
</html>
