//! 系统配置仓储接口定义
//!
//! 提供强类型的系统配置管理接口

use sigmax_core::{
    SigmaXResult, SystemConfigRecord, SystemConfigFilter, SystemConfigBatchResult,
    TradingConfig, RiskManagementConfig, SystemGeneralConfig, NotificationConfig,
    ApiConfig, DatabaseSystemConfig, CacheConfig, MonitoringConfig, StrategySystemConfig,
    StrategyTemplate, ExchangeSystemConfig, UserRoleConfig
};
use chrono::{DateTime, Utc};

/// 系统配置仓储接口
/// 
/// 提供强类型的配置管理功能，支持按命名空间组织配置
#[async_trait::async_trait]
pub trait SystemConfigRepository: Send + Sync {
    // ============================================================================
    // 基础CRUD操作
    // ============================================================================

    /// 获取单个配置记录
    async fn get_config(&self, key: &str) -> SigmaXResult<Option<SystemConfigRecord>>;

    /// 获取多个配置记录
    async fn get_configs(&self, keys: &[String]) -> SigmaXResult<Vec<SystemConfigRecord>>;

    /// 获取所有配置记录
    async fn get_all_configs(&self) -> SigmaXResult<Vec<SystemConfigRecord>>;

    /// 按过滤器查询配置
    async fn find_configs(&self, filter: &SystemConfigFilter) -> SigmaXResult<Vec<SystemConfigRecord>>;

    /// 保存配置记录
    async fn save_config(&self, config: &SystemConfigRecord) -> SigmaXResult<()>;

    /// 更新配置记录
    async fn update_config(&self, config: &SystemConfigRecord) -> SigmaXResult<()>;

    /// 删除配置记录
    async fn delete_config(&self, key: &str) -> SigmaXResult<()>;

    /// 批量保存配置
    async fn batch_save_configs(&self, configs: &[SystemConfigRecord]) -> SigmaXResult<SystemConfigBatchResult>;

    /// 批量更新配置
    async fn batch_update_configs(&self, configs: &[SystemConfigRecord]) -> SigmaXResult<SystemConfigBatchResult>;

    /// 批量删除配置
    async fn batch_delete_configs(&self, keys: &[String]) -> SigmaXResult<SystemConfigBatchResult>;

    // ============================================================================
    // 强类型配置访问
    // ============================================================================

    /// 获取交易配置
    async fn get_trading_config(&self) -> SigmaXResult<TradingConfig>;

    /// 保存交易配置
    async fn save_trading_config(&self, config: &TradingConfig) -> SigmaXResult<()>;

    /// 获取风险管理配置
    async fn get_risk_config(&self) -> SigmaXResult<RiskManagementConfig>;

    /// 保存风险管理配置
    async fn save_risk_config(&self, config: &RiskManagementConfig) -> SigmaXResult<()>;

    /// 获取系统配置
    async fn get_system_config(&self) -> SigmaXResult<SystemGeneralConfig>;

    /// 保存系统配置
    async fn save_system_config(&self, config: &SystemGeneralConfig) -> SigmaXResult<()>;

    /// 获取通知配置
    async fn get_notification_config(&self) -> SigmaXResult<NotificationConfig>;

    /// 保存通知配置
    async fn save_notification_config(&self, config: &NotificationConfig) -> SigmaXResult<()>;

    /// 获取API配置
    async fn get_api_config(&self) -> SigmaXResult<ApiConfig>;

    /// 保存API配置
    async fn save_api_config(&self, config: &ApiConfig) -> SigmaXResult<()>;

    /// 获取数据库配置
    async fn get_database_config(&self) -> SigmaXResult<DatabaseSystemConfig>;

    /// 保存数据库配置
    async fn save_database_config(&self, config: &DatabaseSystemConfig) -> SigmaXResult<()>;

    /// 获取缓存配置
    async fn get_cache_config(&self) -> SigmaXResult<CacheConfig>;

    /// 保存缓存配置
    async fn save_cache_config(&self, config: &CacheConfig) -> SigmaXResult<()>;

    /// 获取监控配置
    async fn get_monitoring_config(&self) -> SigmaXResult<MonitoringConfig>;

    /// 保存监控配置
    async fn save_monitoring_config(&self, config: &MonitoringConfig) -> SigmaXResult<()>;

    /// 获取策略配置
    async fn get_strategy_config(&self) -> SigmaXResult<StrategySystemConfig>;

    /// 保存策略配置
    async fn save_strategy_config(&self, config: &StrategySystemConfig) -> SigmaXResult<()>;

    // ============================================================================
    // 复杂配置管理
    // ============================================================================

    /// 获取策略模板
    async fn get_strategy_template(&self, template_name: &str) -> SigmaXResult<Option<StrategyTemplate>>;

    /// 获取所有策略模板
    async fn get_all_strategy_templates(&self) -> SigmaXResult<Vec<(String, StrategyTemplate)>>;

    /// 保存策略模板
    async fn save_strategy_template(&self, template_name: &str, template: &StrategyTemplate) -> SigmaXResult<()>;

    /// 删除策略模板
    async fn delete_strategy_template(&self, template_name: &str) -> SigmaXResult<()>;

    /// 获取交易所配置
    async fn get_exchange_config(&self, exchange_name: &str) -> SigmaXResult<Option<ExchangeSystemConfig>>;

    /// 获取所有交易所配置
    async fn get_all_exchange_configs(&self) -> SigmaXResult<Vec<(String, ExchangeSystemConfig)>>;

    /// 保存交易所配置
    async fn save_exchange_config(&self, exchange_name: &str, config: &ExchangeSystemConfig) -> SigmaXResult<()>;

    /// 删除交易所配置
    async fn delete_exchange_config(&self, exchange_name: &str) -> SigmaXResult<()>;

    /// 获取用户角色配置
    async fn get_user_role_config(&self, role_name: &str) -> SigmaXResult<Option<UserRoleConfig>>;

    /// 获取所有用户角色配置
    async fn get_all_user_role_configs(&self) -> SigmaXResult<Vec<(String, UserRoleConfig)>>;

    /// 保存用户角色配置
    async fn save_user_role_config(&self, role_name: &str, config: &UserRoleConfig) -> SigmaXResult<()>;

    /// 删除用户角色配置
    async fn delete_user_role_config(&self, role_name: &str) -> SigmaXResult<()>;

    // ============================================================================
    // 命名空间操作
    // ============================================================================

    /// 获取指定命名空间的所有配置
    async fn get_configs_by_namespace(&self, namespace: &str) -> SigmaXResult<Vec<SystemConfigRecord>>;

    /// 删除指定命名空间的所有配置
    async fn delete_configs_by_namespace(&self, namespace: &str) -> SigmaXResult<SystemConfigBatchResult>;

    /// 获取所有命名空间列表
    async fn get_all_namespaces(&self) -> SigmaXResult<Vec<String>>;

    // ============================================================================
    // 配置历史和备份
    // ============================================================================

    /// 获取配置的更新历史
    async fn get_config_history(&self, key: &str, limit: Option<u32>) -> SigmaXResult<Vec<SystemConfigRecord>>;

    /// 创建配置备份
    async fn create_config_backup(&self, backup_name: &str) -> SigmaXResult<()>;

    /// 从备份恢复配置
    async fn restore_from_backup(&self, backup_name: &str) -> SigmaXResult<SystemConfigBatchResult>;

    /// 获取配置统计信息
    async fn get_config_statistics(&self) -> SigmaXResult<ConfigStatistics>;
}

/// 配置统计信息
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct ConfigStatistics {
    /// 总配置数量
    pub total_configs: u64,
    /// 按命名空间分组的配置数量
    pub configs_by_namespace: std::collections::HashMap<String, u64>,
    /// 加密配置数量
    pub encrypted_configs: u64,
    /// 最近更新时间
    pub last_updated: Option<DateTime<Utc>>,
    /// 配置大小统计（字节）
    pub total_size_bytes: u64,
}

impl Default for ConfigStatistics {
    fn default() -> Self {
        Self {
            total_configs: 0,
            configs_by_namespace: std::collections::HashMap::new(),
            encrypted_configs: 0,
            last_updated: None,
            total_size_bytes: 0,
        }
    }
}
