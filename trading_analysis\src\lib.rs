//! # SigmaX Trading Analysis
//!
//! 一个综合性的交易分析库，提供FIFO盈亏分析、绩效指标、风险评估等功能。
//! 
//! ## 功能特性
//! 
//! - **精确的FIFO会计**: 严格按照先进先出原则配对买卖交易
//! - **多交易对支持**: 自动按交易对分组处理
//! - **完整的PnL分析**: 计算已实现和未实现盈亏
//! - **丰富的统计指标**: 胜率、平均盈利/亏损、盈亏比等
//! - **边界情况处理**: 处理空交易、卖出超买入等异常情况
//! - **高性能**: 优化的算法实现，支持大量交易数据
//! 
//! ## 快速开始
//! 
//! ```rust
//! use sigmax_trading_analysis::{analyze_trades_with_fifo, FifoAnalysisResult};
//! use sigmax_core::Trade;
//! use rust_decimal::Decimal;
//!
//! # fn main() -> Result<(), Box<dyn std::error::Error>> {
//! // 假设你有一个交易列表
//! let trades: Vec<Trade> = vec![/* 你的交易数据 */];
//!
//! // 执行FIFO分析
//! let result: FifoAnalysisResult = analyze_trades_with_fifo(&trades)?;
//!
//! // 获取分析结果
//! println!("总已实现盈亏: {}", result.total_realized_pnl());
//! println!("胜率: {:.2}%", result.win_rate() * Decimal::from(100));
//! println!("已实现交易对数: {}", result.realized_pnl_pairs.len());
//! println!("未平仓头寸数: {}", result.unrealized_positions.len());
//! # Ok(())
//! # }
//! ```
//! 
//! ## 核心概念
//! 
//! ### FIFO原则
//! 
//! FIFO（First-In-First-Out）是一种会计方法，确保：
//! - 最早买入的资产最先被卖出
//! - 每笔卖出交易与最早的未平仓买入交易配对
//! - 提供准确的成本基础计算
//! 
//! ### 数据结构
//! 
//! - [`RealizedTradePair`]: 已完成的买卖配对，包含完整的盈亏信息
//! - [`UnrealizedPosition`]: 未平仓头寸，等待后续卖出交易
//! - [`FifoAnalysisResult`]: 完整的分析结果，包含所有配对和头寸
//! 
//! ## 使用场景
//! 
//! - **回测分析**: 评估交易策略的历史表现
//! - **实时监控**: 跟踪当前投资组合的盈亏状况
//! - **税务计算**: 为税务申报提供准确的成本基础
//! - **风险管理**: 分析持仓风险和收益分布
//! 
//! ## 性能特点
//! 
//! - **时间复杂度**: O(n log n)，其中n是交易数量
//! - **空间复杂度**: O(n)
//! - **内存效率**: 使用VecDeque优化队列操作
//! - **并发安全**: 所有公共API都是线程安全的

#![deny(missing_docs)]
#![warn(clippy::all)]
#![warn(clippy::pedantic)]
#![warn(clippy::nursery)]

pub mod analysis;
pub mod types;
pub mod error;
pub mod performance;
pub mod risk;
pub mod technical;

// 重新导出主要类型和函数
pub use analysis::{analyze_trades_with_fifo, analyze_multiple_pairs, merge_analysis_results};
pub use types::{RealizedTradePair, UnrealizedPosition, FifoAnalysisResult};
pub use error::FifoAnalysisError;

// 重新导出绩效分析模块
pub use performance::{PerformanceMetrics, RiskLevel, calculate_performance_metrics};

// 重新导出风险分析模块
pub use risk::{RiskMetrics, RiskAssessment, calculate_risk_metrics};

// 重新导出技术分析模块
pub use technical::{
    TechnicalIndicators,
    calculate_technical_indicators,
    calculate_sma,
    calculate_ema,
    calculate_rsi,
    calculate_bollinger_bands,
    calculate_macd,
    calculate_williams_r,
    calculate_stochastic,
};

// 重新导出Result类型以便使用
/// FIFO分析操作的结果类型
pub type Result<T> = std::result::Result<T, FifoAnalysisError>;
