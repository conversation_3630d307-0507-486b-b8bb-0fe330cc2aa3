-- ============================================================================
-- 策略配置数据迁移脚本
-- ============================================================================
-- 
-- 将现有的 system_config 表中的策略配置迁移到新的 strategy_config 表
-- 使用 JSONB 格式存储完整的策略参数，提升性能和查询效率
--
-- 创建时间: 2025-06-30
-- 作者: Claude 4.0 sonnet
-- 版本: v1.0
-- ============================================================================

-- 开始事务
BEGIN;

-- 1. 创建临时表来存储迁移数据
CREATE TEMP TABLE temp_strategy_migration AS
SELECT 
    CASE 
        WHEN key = 'strategy.default_grid_levels' THEN 'default_grid_levels'
        WHEN key = 'strategy.default_grid_spacing' THEN 'default_grid_spacing'
        WHEN key = 'strategy.max_active_orders' THEN 'max_active_orders'
        WHEN key = 'strategy.rebalance_interval' THEN 'rebalance_interval'
    END as param_name,
    CASE 
        WHEN key IN ('strategy.default_grid_levels', 'strategy.max_active_orders', 'strategy.rebalance_interval') 
        THEN (value::text)::integer
        WHEN key = 'strategy.default_grid_spacing' 
        THEN (value::text)::numeric
    END as param_value
FROM system_config 
WHERE key LIKE 'strategy.%'
AND key IN (
    'strategy.default_grid_levels',
    'strategy.default_grid_spacing', 
    'strategy.max_active_orders',
    'strategy.rebalance_interval'
);

-- 2. 构建策略配置JSON
WITH strategy_json AS (
    SELECT jsonb_object_agg(param_name, param_value) as strategy_parameters
    FROM temp_strategy_migration
    WHERE param_name IS NOT NULL
)
-- 3. 插入到新的 strategy_config 表
INSERT INTO strategy_config (
    id,
    name,
    description,
    enabled,
    strategy_parameters,
    created_at,
    updated_at,
    created_by
)
SELECT 
    gen_random_uuid(),
    'migrated_default',
    '从 system_config 迁移的默认策略配置',
    true,
    COALESCE(strategy_parameters, '{
        "default_grid_levels": 10,
        "default_grid_spacing": 1.0,
        "max_active_orders": 20,
        "rebalance_interval": 300
    }'::jsonb),
    NOW(),
    NOW(),
    'migration_script'
FROM strategy_json
-- 如果没有找到任何配置，插入默认配置
UNION ALL
SELECT 
    gen_random_uuid(),
    'default',
    '系统默认策略配置',
    true,
    '{
        "default_grid_levels": 10,
        "default_grid_spacing": 1.0,
        "max_active_orders": 20,
        "rebalance_interval": 300
    }'::jsonb,
    NOW(),
    NOW(),
    'migration_script'
WHERE NOT EXISTS (SELECT 1 FROM strategy_json WHERE strategy_parameters IS NOT NULL);

-- 4. 验证迁移结果
DO $$
DECLARE
    config_count INTEGER;
    sample_config JSONB;
BEGIN
    -- 检查迁移的配置数量
    SELECT COUNT(*) INTO config_count FROM strategy_config;
    RAISE NOTICE '迁移完成，共有 % 个策略配置', config_count;
    
    -- 显示示例配置
    SELECT strategy_parameters INTO sample_config 
    FROM strategy_config 
    WHERE enabled = true 
    LIMIT 1;
    
    RAISE NOTICE '示例配置: %', sample_config;
END $$;

-- 5. 备份原始数据（可选）
-- 创建备份表保存原始的 system_config 策略数据
CREATE TABLE IF NOT EXISTS strategy_config_backup AS
SELECT 
    key,
    value,
    description,
    created_at,
    updated_at
FROM system_config 
WHERE key LIKE 'strategy.%';

-- 6. 清理 system_config 中的策略配置（可选，谨慎执行）
-- 注释掉以下语句，需要手动确认后再执行
-- DELETE FROM system_config WHERE key LIKE 'strategy.%';

-- 提交事务
COMMIT;

-- 验证查询
SELECT 
    'Migration Summary' as info,
    COUNT(*) as total_configs,
    COUNT(*) FILTER (WHERE enabled = true) as enabled_configs
FROM strategy_config;

SELECT 
    'Sample Configuration' as info,
    name,
    jsonb_pretty(strategy_parameters) as config
FROM strategy_config 
WHERE enabled = true 
LIMIT 1;
