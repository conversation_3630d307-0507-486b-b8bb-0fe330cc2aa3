//! 缓存配置服务
//!
//! 专门负责缓存相关配置的管理

use std::sync::Arc;
use std::sync::atomic::{AtomicBool, Ordering};
use async_trait::async_trait;
use tracing::{info, error};

use crate::error::{ApiError, ApiResult};
use super::traits::{ConfigService, ConfigStatistics};
use sigmax_core::{CacheConfig, ConfigCache};
use sigmax_database::DatabaseManager;
use sigmax_database::repositories::sqlx::SqlCacheRepository;
use sigmax_database::repositories::traits::CacheRepository;

/// 缓存配置服务
///
/// 负责管理缓存相关配置，包括：
/// - 基础缓存配置管理
/// - 缓存性能监控配置
/// - 缓存健康检查配置
/// - 缓存告警配置
/// - 缓存统计和分析
#[derive(Clone)]
pub struct CacheConfigService {
    /// 缓存配置仓储 - 使用专门的缓存仓储
    repository: Arc<SqlCacheRepository>,
    /// 配置缓存
    cache: Arc<ConfigCache>,
    /// 初始化状态
    is_initialized: Arc<AtomicBool>,
}

impl CacheConfigService {
    /// 创建新的缓存配置服务
    pub async fn new(database: Arc<DatabaseManager>) -> ApiResult<Self> {
        let repository = Arc::new(SqlCacheRepository::new(database));
        let cache = Arc::new(ConfigCache::new(std::time::Duration::from_secs(300))); // 5分钟缓存

        Ok(Self {
            repository,
            cache,
            is_initialized: Arc::new(AtomicBool::new(false)),
        })
    }

    /// 获取缓存配置
    pub async fn get_config(&self) -> ApiResult<CacheConfig> {
        if !self.is_initialized() {
            return Err(ApiError::BadRequest("CacheConfigService not initialized".to_string()));
        }

        self.cache.get_or_load_cache_config(|| {
            self.repository.get_config()
        })
        .await
        .map_err(|e| {
            error!("Failed to get cache config: {}", e);
            ApiError::from(e)
        })
    }

    /// 保存缓存配置
    pub async fn save_config(&self, config: &CacheConfig) -> ApiResult<()> {
        if !self.is_initialized() {
            return Err(ApiError::BadRequest("CacheConfigService not initialized".to_string()));
        }

        self.validate_config(config)?;

        self.repository
            .save_config(config)
            .await
            .map_err(|e| {
                error!("Failed to save cache config: {}", e);
                ApiError::from(e)
            })?;

        self.cache.clear_config_cache("cache").await;
        info!("Cache config saved successfully and cache cleared");

        Ok(())
    }

    /// 验证缓存配置
    fn validate_config(&self, config: &CacheConfig) -> ApiResult<()> {
        // 基础配置验证
        if config.default_ttl_seconds == 0 {
            return Err(ApiError::BadRequest("Default TTL must be greater than 0".to_string()));
        }

        if config.default_ttl_seconds > 86400 { // 24小时
            return Err(ApiError::BadRequest("Default TTL cannot exceed 24 hours".to_string()));
        }

        if config.max_memory_mb == 0 {
            return Err(ApiError::BadRequest("Max memory must be greater than 0".to_string()));
        }

        if config.max_memory_mb > 10240 { // 10GB
            return Err(ApiError::BadRequest("Max memory cannot exceed 10GB".to_string()));
        }

        if config.max_entries == 0 {
            return Err(ApiError::BadRequest("Max entries must be greater than 0".to_string()));
        }

        if config.memory_threshold_percentage > 100 {
            return Err(ApiError::BadRequest("Memory threshold percentage cannot exceed 100".to_string()));
        }

        // 性能监控配置验证
        if config.performance_monitoring.enabled {
            if config.performance_monitoring.sampling_rate < 0.0 || config.performance_monitoring.sampling_rate > 1.0 {
                return Err(ApiError::BadRequest("Sampling rate must be between 0.0 and 1.0".to_string()));
            }
        }

        // 健康检查配置验证
        if config.health_check.enabled {
            if config.health_check.check_interval == 0 {
                return Err(ApiError::BadRequest("Health check interval must be greater than 0".to_string()));
            }
        }

        Ok(())
    }

    // ============================================================================
    // 扩展的缓存管理功能
    // ============================================================================

    /// 更新缓存配置
    pub async fn update_config(&self, config: &CacheConfig) -> ApiResult<()> {
        if !self.is_initialized() {
            return Err(ApiError::BadRequest("CacheConfigService not initialized".to_string()));
        }

        self.validate_config(config)?;

        self.repository
            .update_config(config)
            .await
            .map_err(|e| {
                error!("Failed to update cache config: {}", e);
                ApiError::from(e)
            })?;

        self.cache.clear_config_cache("cache").await;
        info!("Cache config updated successfully and cache cleared");

        Ok(())
    }

    /// 删除缓存配置
    pub async fn delete_config(&self) -> ApiResult<()> {
        if !self.is_initialized() {
            return Err(ApiError::BadRequest("CacheConfigService not initialized".to_string()));
        }

        self.repository
            .delete_config()
            .await
            .map_err(|e| {
                error!("Failed to delete cache config: {}", e);
                ApiError::from(e)
            })?;

        self.cache.clear_config_cache("cache").await;
        info!("Cache config deleted successfully and cache cleared");

        Ok(())
    }

    /// 清理缓存数据
    pub async fn clear_cache_data(&self, cache_types: Option<Vec<String>>) -> ApiResult<u64> {
        if !self.is_initialized() {
            return Err(ApiError::BadRequest("CacheConfigService not initialized".to_string()));
        }

        self.repository
            .clear_cache_data(cache_types)
            .await
            .map_err(|e| {
                error!("Failed to clear cache data: {}", e);
                ApiError::from(e)
            })
    }

    /// 预热缓存
    pub async fn warm_cache(
        &self,
        cache_types: Vec<String>,
        priority_keys: Option<Vec<String>>
    ) -> ApiResult<()> {
        if !self.is_initialized() {
            return Err(ApiError::BadRequest("CacheConfigService not initialized".to_string()));
        }

        self.repository
            .warm_cache(cache_types, priority_keys)
            .await
            .map_err(|e| {
                error!("Failed to warm cache: {}", e);
                ApiError::from(e)
            })
    }

    /// 获取缓存键信息
    pub async fn get_cache_keys(
        &self,
        cache_type: Option<&str>,
        pattern: Option<&str>,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> ApiResult<Vec<serde_json::Value>> {
        if !self.is_initialized() {
            return Err(ApiError::BadRequest("CacheConfigService not initialized".to_string()));
        }

        self.repository
            .get_cache_keys(cache_type, pattern, limit, offset)
            .await
            .map_err(|e| {
                error!("Failed to get cache keys: {}", e);
                ApiError::from(e)
            })
    }
}

#[async_trait]
impl ConfigService for CacheConfigService {
    async fn initialize(&self) -> ApiResult<()> {
        info!("🔧 Initializing CacheConfigService...");

        match self.cache.get_or_load_cache_config(|| {
            self.repository.get_config()
        }).await {
            Ok(_) => {
                self.is_initialized.store(true, Ordering::Relaxed);
                info!("✅ CacheConfigService initialized successfully");
                Ok(())
            }
            Err(e) => {
                error!("❌ CacheConfigService initialization failed: {}", e);
                Err(ApiError::from(e))
            }
        }
    }

    async fn reload(&self) -> ApiResult<()> {
        info!("🔄 Reloading CacheConfigService...");

        self.cache.clear_config_cache("cache").await;

        self.cache.get_or_load_cache_config(|| {
            self.repository.get_config()
        })
        .await
        .map_err(|e| {
            error!("Failed to reload cache config: {}", e);
            ApiError::from(e)
        })?;

        info!("✅ CacheConfigService reloaded successfully");
        Ok(())
    }

    fn is_initialized(&self) -> bool {
        self.is_initialized.load(Ordering::Relaxed)
    }

    fn service_name(&self) -> &'static str {
        "CacheConfigService"
    }

    async fn clear_cache(&self) -> ApiResult<()> {
        self.cache.clear_config_cache("cache").await;
        Ok(())
    }

    async fn get_statistics(&self) -> ApiResult<ConfigStatistics> {
        Ok(ConfigStatistics {
            config_count: 1,
            cache_hit_rate: 0.0,
            last_updated: None,
            is_initialized: self.is_initialized(),
        })
    }
}
