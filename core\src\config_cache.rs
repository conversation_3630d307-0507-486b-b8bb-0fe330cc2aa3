//! 系统配置缓存工具类
//! 
//! 提供高性能的配置访问，支持缓存和便捷的配置管理
//! 
//! 使用示例：
//! ```rust
//! use once_cell::sync::Lazy;
//! use std::time::Duration;
//! 
//! // 创建全局配置缓存
//! static CONFIG_CACHE: Lazy<ConfigCache> = Lazy::new(|| {
//!     ConfigCache::new(Duration::from_secs(300)) // 5分钟缓存
//! });
//! 
//! // 在业务代码中使用
//! async fn get_trading_settings(repo: &impl SystemConfigRepository) -> SigmaXResult<TradingConfig> {
//!     CONFIG_CACHE.get_or_load_trading_config(|| {
//!         repo.get_trading_config()
//!     }).await
//! }
//! 
//! // 清除缓存
//! CONFIG_CACHE.clear_cache().await;
//! ```

use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use std::collections::HashMap;
use std::future::Future;
use serde_json;
use crate::{
    SigmaXResult,
    // 实际存在的配置类型
    RiskManagementConfig, CacheConfig, MonitoringConfig,
    // 从 config/mod.rs 导入的基础配置类型
    AppConfig, WebConfig, LoggingConfig,
    // 🔥 修复：使用系统级配置类型
    DatabaseSystemConfig, ExchangeSystemConfig,
    TradingConfig, SystemGeneralConfig, NotificationConfig, ApiConfig,
    StrategySystemConfig, StrategyTemplate,
};

/// 配置缓存项
#[derive(Debug, Clone)]
struct ConfigCacheItem<T> {
    /// 配置值
    value: T,
    /// 缓存时间
    cached_at: Instant,
    /// 缓存TTL
    ttl: Duration,
}

impl<T> ConfigCacheItem<T> {
    fn new(value: T, ttl: Duration) -> Self {
        Self {
            value,
            cached_at: Instant::now(),
            ttl,
        }
    }

    fn is_expired(&self) -> bool {
        self.cached_at.elapsed() > self.ttl
    }
}

/// 系统配置缓存工具类
///
/// 提供高性能的配置访问，内置缓存机制
pub struct ConfigCache {
    /// 强类型配置缓存
    app_config: RwLock<Option<ConfigCacheItem<AppConfig>>>,
    risk_config: RwLock<Option<ConfigCacheItem<RiskManagementConfig>>>,
    cache_config: RwLock<Option<ConfigCacheItem<CacheConfig>>>,
    monitoring_config: RwLock<Option<ConfigCacheItem<MonitoringConfig>>>,
    database_config: RwLock<Option<ConfigCacheItem<DatabaseSystemConfig>>>,
    web_config: RwLock<Option<ConfigCacheItem<WebConfig>>>,
    logging_config: RwLock<Option<ConfigCacheItem<LoggingConfig>>>,

    /// 🔥 新增：系统级配置缓存
    trading_config: RwLock<Option<ConfigCacheItem<TradingConfig>>>,
    system_config: RwLock<Option<ConfigCacheItem<SystemGeneralConfig>>>,
    notification_config: RwLock<Option<ConfigCacheItem<NotificationConfig>>>,
    api_config: RwLock<Option<ConfigCacheItem<ApiConfig>>>,
    strategy_config: RwLock<Option<ConfigCacheItem<StrategySystemConfig>>>,

    /// 复杂配置缓存
    exchange_configs: RwLock<HashMap<String, ConfigCacheItem<ExchangeSystemConfig>>>,
    strategy_templates: RwLock<HashMap<String, ConfigCacheItem<StrategyTemplate>>>,

    /// 原始配置缓存（键值对）
    raw_configs: RwLock<HashMap<String, ConfigCacheItem<serde_json::Value>>>,

    /// 配置选项
    default_ttl: Duration,
    enable_cache: bool,
}

impl ConfigCache {
    /// 创建新的配置缓存
    pub fn new(default_ttl: Duration) -> Self {
        Self {
            app_config: RwLock::new(None),
            risk_config: RwLock::new(None),
            cache_config: RwLock::new(None),
            monitoring_config: RwLock::new(None),
            database_config: RwLock::new(None),
            web_config: RwLock::new(None),
            logging_config: RwLock::new(None),

            // 🔥 新增：系统级配置初始化
            trading_config: RwLock::new(None),
            system_config: RwLock::new(None),
            notification_config: RwLock::new(None),
            api_config: RwLock::new(None),
            strategy_config: RwLock::new(None),

            exchange_configs: RwLock::new(HashMap::new()),
            strategy_templates: RwLock::new(HashMap::new()),
            raw_configs: RwLock::new(HashMap::new()),
            default_ttl,
            enable_cache: true,
        }
    }

    /// 创建默认配置缓存（5分钟TTL）
    pub fn with_defaults() -> Self {
        Self::new(Duration::from_secs(300))
    }

    /// 禁用缓存的配置管理器
    pub fn without_cache() -> Self {
        let mut cache = Self::new(Duration::from_secs(0));
        cache.enable_cache = false;
        cache
    }

    // ============================================================================
    // 强类型配置访问方法
    // ============================================================================

    /// 获取或加载应用配置
    pub async fn get_or_load_app_config<F, Fut>(&self, loader: F) -> SigmaXResult<AppConfig>
    where
        F: FnOnce() -> Fut,
        Fut: Future<Output = SigmaXResult<AppConfig>>,
    {
        self.get_cached_config(&self.app_config, loader).await
    }

    /// 获取或加载风险管理配置
    pub async fn get_or_load_risk_config<F, Fut>(&self, loader: F) -> SigmaXResult<RiskManagementConfig>
    where
        F: FnOnce() -> Fut,
        Fut: Future<Output = SigmaXResult<RiskManagementConfig>>,
    {
        self.get_cached_config(&self.risk_config, loader).await
    }

    /// 获取或加载数据库配置
    pub async fn get_or_load_database_config<F, Fut>(&self, loader: F) -> SigmaXResult<DatabaseSystemConfig>
    where
        F: FnOnce() -> Fut,
        Fut: Future<Output = SigmaXResult<DatabaseSystemConfig>>,
    {
        self.get_cached_config(&self.database_config, loader).await
    }

    /// 获取或加载缓存配置
    pub async fn get_or_load_cache_config<F, Fut>(&self, loader: F) -> SigmaXResult<CacheConfig>
    where
        F: FnOnce() -> Fut,
        Fut: Future<Output = SigmaXResult<CacheConfig>>,
    {
        self.get_cached_config(&self.cache_config, loader).await
    }

    /// 获取或加载监控配置
    pub async fn get_or_load_monitoring_config<F, Fut>(&self, loader: F) -> SigmaXResult<MonitoringConfig>
    where
        F: FnOnce() -> Fut,
        Fut: Future<Output = SigmaXResult<MonitoringConfig>>,
    {
        self.get_cached_config(&self.monitoring_config, loader).await
    }

    /// 🔥 新增：获取或加载交易配置
    pub async fn get_or_load_trading_config<F, Fut>(&self, loader: F) -> SigmaXResult<TradingConfig>
    where
        F: FnOnce() -> Fut,
        Fut: Future<Output = SigmaXResult<TradingConfig>>,
    {
        self.get_cached_config(&self.trading_config, loader).await
    }

    /// 🔥 新增：获取或加载系统配置
    pub async fn get_or_load_system_config<F, Fut>(&self, loader: F) -> SigmaXResult<SystemGeneralConfig>
    where
        F: FnOnce() -> Fut,
        Fut: Future<Output = SigmaXResult<SystemGeneralConfig>>,
    {
        self.get_cached_config(&self.system_config, loader).await
    }

    /// 🔥 新增：获取或加载通知配置
    pub async fn get_or_load_notification_config<F, Fut>(&self, loader: F) -> SigmaXResult<NotificationConfig>
    where
        F: FnOnce() -> Fut,
        Fut: Future<Output = SigmaXResult<NotificationConfig>>,
    {
        self.get_cached_config(&self.notification_config, loader).await
    }

    /// 🔥 新增：获取或加载API配置
    pub async fn get_or_load_api_config<F, Fut>(&self, loader: F) -> SigmaXResult<ApiConfig>
    where
        F: FnOnce() -> Fut,
        Fut: Future<Output = SigmaXResult<ApiConfig>>,
    {
        self.get_cached_config(&self.api_config, loader).await
    }

    /// 🔥 新增：获取或加载策略配置
    pub async fn get_or_load_strategy_config<F, Fut>(&self, loader: F) -> SigmaXResult<StrategySystemConfig>
    where
        F: FnOnce() -> Fut,
        Fut: Future<Output = SigmaXResult<StrategySystemConfig>>,
    {
        self.get_cached_config(&self.strategy_config, loader).await
    }

    // ============================================================================
    // 复杂配置访问方法
    // ============================================================================

    /// 获取或加载交易所配置
    pub async fn get_or_load_exchange_config<F, Fut>(
        &self,
        exchange_name: &str,
        loader: F
    ) -> SigmaXResult<Option<ExchangeSystemConfig>>
    where
        F: FnOnce() -> Fut,
        Fut: Future<Output = SigmaXResult<Option<ExchangeSystemConfig>>>,
    {
        if !self.enable_cache {
            return loader().await;
        }

        let configs = self.exchange_configs.read().await;
        if let Some(cached) = configs.get(exchange_name) {
            if !cached.is_expired() {
                return Ok(Some(cached.value.clone()));
            }
        }
        drop(configs);

        match loader().await? {
            Some(config) => {
                let mut configs = self.exchange_configs.write().await;
                configs.insert(
                    exchange_name.to_string(),
                    ConfigCacheItem::new(config.clone(), self.default_ttl),
                );
                Ok(Some(config))
            }
            None => Ok(None),
        }
    }

    /// 🔥 新增：获取或加载策略模板
    pub async fn get_or_load_strategy_template<F, Fut>(
        &self,
        template_name: &str,
        loader: F
    ) -> SigmaXResult<Option<StrategyTemplate>>
    where
        F: FnOnce() -> Fut,
        Fut: Future<Output = SigmaXResult<Option<StrategyTemplate>>>,
    {
        if !self.enable_cache {
            return loader().await;
        }

        let templates = self.strategy_templates.read().await;
        if let Some(cached) = templates.get(template_name) {
            if !cached.is_expired() {
                return Ok(Some(cached.value.clone()));
            }
        }
        drop(templates);

        match loader().await? {
            Some(template) => {
                let mut templates = self.strategy_templates.write().await;
                templates.insert(
                    template_name.to_string(),
                    ConfigCacheItem::new(template.clone(), self.default_ttl),
                );
                Ok(Some(template))
            }
            None => Ok(None),
        }
    }

    /// 获取或加载原始配置值
    pub async fn get_or_load_raw_config<F, Fut>(
        &self, 
        key: &str, 
        loader: F
    ) -> SigmaXResult<Option<serde_json::Value>>
    where
        F: FnOnce() -> Fut,
        Fut: Future<Output = SigmaXResult<Option<serde_json::Value>>>,
    {
        if !self.enable_cache {
            return loader().await;
        }

        let configs = self.raw_configs.read().await;
        if let Some(cached) = configs.get(key) {
            if !cached.is_expired() {
                return Ok(Some(cached.value.clone()));
            }
        }
        drop(configs);

        match loader().await? {
            Some(value) => {
                let mut configs = self.raw_configs.write().await;
                configs.insert(
                    key.to_string(),
                    ConfigCacheItem::new(value.clone(), self.default_ttl),
                );
                Ok(Some(value))
            }
            None => Ok(None),
        }
    }

    // ============================================================================
    // 缓存管理方法
    // ============================================================================

    /// 清除所有缓存
    pub async fn clear_cache(&self) {
        *self.app_config.write().await = None;
        *self.risk_config.write().await = None;
        *self.cache_config.write().await = None;
        *self.monitoring_config.write().await = None;
        *self.database_config.write().await = None;
        *self.web_config.write().await = None;
        *self.logging_config.write().await = None;

        // 🔥 新增：清除系统级配置缓存
        *self.trading_config.write().await = None;
        *self.system_config.write().await = None;
        *self.notification_config.write().await = None;
        *self.api_config.write().await = None;
        *self.strategy_config.write().await = None;

        self.exchange_configs.write().await.clear();
        self.strategy_templates.write().await.clear();
        self.raw_configs.write().await.clear();
    }

    /// 清除指定类型的缓存
    pub async fn clear_config_cache(&self, config_type: &str) {
        match config_type {
            "app" => *self.app_config.write().await = None,
            "risk" => *self.risk_config.write().await = None,
            "cache" => *self.cache_config.write().await = None,
            "monitoring" => *self.monitoring_config.write().await = None,
            "database" => *self.database_config.write().await = None,
            "web" => *self.web_config.write().await = None,
            "logging" => *self.logging_config.write().await = None,
            "exchange_configs" => self.exchange_configs.write().await.clear(),
            "raw_configs" => self.raw_configs.write().await.clear(),
            _ => {}
        }
    }

    // ============================================================================
    // 内部辅助方法
    // ============================================================================

    /// 通用缓存配置获取方法
    async fn get_cached_config<T, F, Fut>(
        &self,
        cache: &RwLock<Option<ConfigCacheItem<T>>>,
        loader: F,
    ) -> SigmaXResult<T>
    where
        T: Clone + Send + Sync,
        F: FnOnce() -> Fut,
        Fut: Future<Output = SigmaXResult<T>>,
    {
        if !self.enable_cache {
            return loader().await;
        }

        // 检查缓存
        {
            let cached = cache.read().await;
            if let Some(ref item) = *cached {
                if !item.is_expired() {
                    return Ok(item.value.clone());
                }
            }
        }

        // 缓存过期或不存在，从加载器获取
        let value = loader().await?;
        let mut cached = cache.write().await;
        *cached = Some(ConfigCacheItem::new(value.clone(), self.default_ttl));
        Ok(value)
    }
}
