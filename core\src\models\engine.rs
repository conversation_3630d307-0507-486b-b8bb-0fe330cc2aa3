//! 交易引擎相关的数据模型

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

use crate::{Amount, EngineId, ExchangeId, TradingPair};

/// 引擎配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EngineConfig {
    pub engine_type: crate::EngineType,
    pub trading_pairs: Vec<TradingPair>,
    pub initial_capital: Amount,
    pub exchange_config: Option<EngineExchangeConfig>,
    pub strategy_config: EngineStrategyConfig,
    pub risk_config: EngineRiskConfig,
    pub created_at: DateTime<Utc>,
}

impl EngineConfig {
    pub fn new(
        engine_type: crate::EngineType,
        trading_pairs: Vec<TradingPair>,
        initial_capital: Amount,
    ) -> Self {
        Self {
            engine_type,
            trading_pairs,
            initial_capital,
            exchange_config: None,
            strategy_config: EngineStrategyConfig::default(),
            risk_config: EngineRiskConfig::default(),
            created_at: Utc::now(),
        }
    }
}

impl Default for EngineConfig {
    fn default() -> Self {
        Self::new(
            crate::EngineType::Backtest,
            vec![crate::TradingPair::new("BTC".to_string(), "USDT".to_string())],
            rust_decimal::Decimal::new(10000, 0), // 10000 USDT
        )
    }
}

/// 引擎专用交易所配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EngineExchangeConfig {
    pub exchange_id: ExchangeId,
    pub api_key: Option<String>,
    pub api_secret: Option<String>,
    pub sandbox: bool,
    pub rate_limit: Option<u32>,
    pub timeout: Option<u64>,
}

impl Default for EngineExchangeConfig {
    fn default() -> Self {
        Self {
            exchange_id: ExchangeId::Simulator,
            api_key: None,
            api_secret: None,
            sandbox: true,
            rate_limit: Some(1200),
            timeout: Some(30),
        }
    }
}

/// 引擎专用策略配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EngineStrategyConfig {
    pub strategy_type: String,
    pub parameters: HashMap<String, serde_json::Value>,
    pub enabled: bool,
    pub max_positions: Option<u32>,
    pub position_size: Option<Amount>,
}

impl Default for EngineStrategyConfig {
    fn default() -> Self {
        Self {
            strategy_type: "grid".to_string(),
            parameters: HashMap::new(),
            enabled: true,
            max_positions: Some(10),
            position_size: None,
        }
    }
}

/// 引擎专用风险配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EngineRiskConfig {
    pub max_position_size: Amount,
    pub max_daily_loss: Amount,
    pub max_drawdown: Amount,
    pub stop_loss_percentage: Option<rust_decimal::Decimal>,
    pub take_profit_percentage: Option<rust_decimal::Decimal>,
    pub max_leverage: Option<rust_decimal::Decimal>,
}

impl Default for EngineRiskConfig {
    fn default() -> Self {
        Self {
            max_position_size: Amount::from(1000),
            max_daily_loss: Amount::from(100),
            max_drawdown: Amount::from(500),
            stop_loss_percentage: Some(rust_decimal::Decimal::from(5)),
            take_profit_percentage: Some(rust_decimal::Decimal::from(10)),
            max_leverage: Some(rust_decimal::Decimal::from(1)),
        }
    }
}

/// 引擎信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EngineInfo {
    pub id: crate::EngineId,
    pub engine_type: crate::EngineType,
    pub status: crate::EngineStatus,
    pub config: EngineConfig,
    pub statistics: EngineStatistics,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

impl EngineInfo {
    pub fn new(
        id: crate::EngineId,
        engine_type: crate::EngineType,
        status: crate::EngineStatus,
        config: EngineConfig,
    ) -> Self {
        let now = Utc::now();
        Self {
            id,
            engine_type,
            status,
            config,
            statistics: EngineStatistics::new(id),
            created_at: now,
            updated_at: now,
        }
    }
}

impl Default for EngineInfo {
    fn default() -> Self {
        Self::new(
            uuid::Uuid::new_v4(),
            crate::EngineType::Backtest,
            crate::EngineStatus::Stopped,
            EngineConfig::default(),
        )
    }
}

/// 引擎统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EngineStatistics {
    pub engine_id: EngineId,
    pub uptime: std::time::Duration,
    pub total_orders: u64,
    pub successful_orders: u64,
    pub failed_orders: u64,
    pub total_trades: u64,
    pub total_volume: Amount,
    pub total_pnl: Amount,
    pub win_rate: rust_decimal::Decimal,
    pub sharpe_ratio: Option<rust_decimal::Decimal>,
    pub max_drawdown: Amount,
    pub last_updated: DateTime<Utc>,
}

impl EngineStatistics {
    pub fn new(engine_id: EngineId) -> Self {
        Self {
            engine_id,
            uptime: std::time::Duration::from_secs(0),
            total_orders: 0,
            successful_orders: 0,
            failed_orders: 0,
            total_trades: 0,
            total_volume: Amount::ZERO,
            total_pnl: Amount::ZERO,
            win_rate: rust_decimal::Decimal::ZERO,
            sharpe_ratio: None,
            max_drawdown: Amount::ZERO,
            last_updated: Utc::now(),
        }
    }

    pub fn success_rate(&self) -> rust_decimal::Decimal {
        if self.total_orders == 0 {
            rust_decimal::Decimal::ZERO
        } else {
            rust_decimal::Decimal::from(self.successful_orders)
                / rust_decimal::Decimal::from(self.total_orders)
                * rust_decimal::Decimal::from(100)
        }
    }
}