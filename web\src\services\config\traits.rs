//! 配置服务接口定义
//!
//! 定义了所有配置服务必须实现的通用接口

use async_trait::async_trait;
use crate::error::ApiResult;

/// 配置服务通用接口
///
/// 所有配置服务都必须实现这个接口，提供统一的生命周期管理
#[async_trait]
pub trait ConfigService: Send + Sync {
    /// 初始化配置服务
    ///
    /// 这个方法应该：
    /// 1. 从数据库加载配置
    /// 2. 初始化缓存
    /// 3. 验证配置有效性
    /// 4. 设置默认值（如果需要）
    async fn initialize(&self) -> ApiResult<()>;

    /// 重新加载配置
    ///
    /// 这个方法应该：
    /// 1. 清除现有缓存
    /// 2. 重新从数据库加载配置
    /// 3. 更新缓存
    async fn reload(&self) -> ApiResult<()>;

    /// 检查配置服务是否已初始化
    fn is_initialized(&self) -> bool;

    /// 获取配置服务名称
    fn service_name(&self) -> &'static str;

    /// 健康检查
    ///
    /// 检查配置服务是否正常工作
    async fn health_check(&self) -> ApiResult<bool> {
        Ok(self.is_initialized())
    }

    /// 清除缓存
    ///
    /// 清除配置缓存，强制下次访问时重新加载
    async fn clear_cache(&self) -> ApiResult<()>;

    /// 获取配置统计信息
    ///
    /// 返回配置的统计信息，如缓存命中率、配置项数量等
    async fn get_statistics(&self) -> ApiResult<ConfigStatistics> {
        Ok(ConfigStatistics::default())
    }
}

/// 配置统计信息
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct ConfigStatistics {
    /// 配置项数量
    pub config_count: u32,
    /// 缓存命中率
    pub cache_hit_rate: f64,
    /// 最后更新时间
    pub last_updated: Option<chrono::DateTime<chrono::Utc>>,
    /// 初始化状态
    pub is_initialized: bool,
}

impl Default for ConfigStatistics {
    fn default() -> Self {
        Self {
            config_count: 0,
            cache_hit_rate: 0.0,
            last_updated: None,
            is_initialized: false,
        }
    }
}

/// 配置服务错误类型
#[derive(Debug)]
pub enum ConfigServiceError {
    NotInitialized { service: String },
    ValidationFailed { message: String },
    LoadFailed { message: String },
    CacheFailed { message: String },
    DatabaseFailed { message: String },
}

impl std::fmt::Display for ConfigServiceError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ConfigServiceError::NotInitialized { service } => {
                write!(f, "配置服务未初始化: {}", service)
            }
            ConfigServiceError::ValidationFailed { message } => {
                write!(f, "配置验证失败: {}", message)
            }
            ConfigServiceError::LoadFailed { message } => {
                write!(f, "配置加载失败: {}", message)
            }
            ConfigServiceError::CacheFailed { message } => {
                write!(f, "缓存操作失败: {}", message)
            }
            ConfigServiceError::DatabaseFailed { message } => {
                write!(f, "数据库操作失败: {}", message)
            }
        }
    }
}

impl std::error::Error for ConfigServiceError {}
