//! 订单执行引擎

use sigmax_core::{Exchange, Order, OrderId, SigmaXResult};
use std::sync::Arc;
use tokio::sync::mpsc;
use tracing::{error, info};

/// 订单执行引擎
pub struct ExecutionEngine {
    exchange: Arc<dyn Exchange>,
    order_sender: mpsc::UnboundedSender<Order>,
    order_receiver: Option<mpsc::UnboundedReceiver<Order>>,
}

impl ExecutionEngine {
    pub fn new(exchange: Arc<dyn Exchange>) -> Self {
        let (order_sender, order_receiver) = mpsc::unbounded_channel();
        
        Self {
            exchange,
            order_sender,
            order_receiver: Some(order_receiver),
        }
    }

    /// 获取订单发送器
    pub fn get_order_sender(&self) -> mpsc::UnboundedSender<Order> {
        self.order_sender.clone()
    }

    /// 提交订单
    pub async fn submit_order(&self, order: Order) -> SigmaXResult<()> {
        self.order_sender.send(order)
            .map_err(|e| sigmax_core::SigmaXError::Internal(format!("Failed to send order: {}", e)))?;
        Ok(())
    }

    /// 启动执行引擎
    pub async fn start(&mut self) -> SigmaXResult<()> {
        let mut receiver = self.order_receiver.take()
            .ok_or_else(|| sigmax_core::SigmaXError::Internal("Execution engine already started".to_string()))?;

        info!("启动订单执行引擎");

        while let Some(order) = receiver.recv().await {
            if let Err(e) = self.process_order(order).await {
                error!("处理订单失败: {}", e);
            }
        }

        Ok(())
    }

    /// 处理单个订单
    async fn process_order(&self, order: Order) -> SigmaXResult<OrderId> {
        info!("处理订单: {:?}", order.id);
        
        // 提交订单到交易所
        let order_id = self.exchange.place_order(&order).await?;
        
        info!("订单已提交到交易所: {}", order_id);
        
        Ok(order_id)
    }

    /// 取消订单
    pub async fn cancel_order(&self, order_id: OrderId) -> SigmaXResult<()> {
        info!("取消订单: {}", order_id);
        self.exchange.cancel_order(order_id).await?;
        Ok(())
    }

    /// 获取订单状态
    pub async fn get_order_status(&self, order_id: OrderId) -> SigmaXResult<Order> {
        self.exchange.get_order(order_id).await
    }
}
