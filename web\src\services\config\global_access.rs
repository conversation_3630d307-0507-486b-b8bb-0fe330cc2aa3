//! 全局配置访问函数
//!
//! 提供向后兼容的全局配置访问接口，内部使用新的模块化配置服务

use std::sync::Arc;
use once_cell::sync::OnceCell;
use sigmax_core::{
    SigmaXResult, TradingConfig, RiskManagementConfig, SystemGeneralConfig,
    NotificationConfig, ApiConfig, CacheConfig, MonitoringConfig, StrategySystemConfig
};
use super::{ConfigManager, traits::ConfigService};

/// 全局配置管理器实例
static GLOBAL_CONFIG_MANAGER: OnceCell<Arc<ConfigManager>> = OnceCell::new();

/// 初始化全局配置管理器
/// 
/// 这个方法应该在系统启动时调用一次
pub fn init_global_config_manager(config_manager: Arc<ConfigManager>) -> Result<(), String> {
    GLOBAL_CONFIG_MANAGER.set(config_manager)
        .map_err(|_| "Global config manager already initialized".to_string())
}

/// 获取全局配置管理器
fn get_global_config_manager() -> &'static Arc<ConfigManager> {
    GLOBAL_CONFIG_MANAGER.get()
        .expect("Global config manager not initialized. Call init_global_config_manager first.")
}

// ============================================================================
// 向后兼容的配置访问函数
// ============================================================================

/// 获取系统配置
/// 
/// 直接从专门的系统配置服务获取
pub async fn get_system_config() -> SigmaXResult<SystemGeneralConfig> {
    let config_manager = get_global_config_manager();
    config_manager.system_config.get_config().await
        .map_err(|e| sigmax_core::SigmaXError::Config(e.to_string()))
}

/// 获取交易配置
/// 
/// 直接从专门的交易配置服务获取
pub async fn get_trading_config() -> SigmaXResult<TradingConfig> {
    let config_manager = get_global_config_manager();
    config_manager.trading_config.get_config().await
        .map_err(|e| sigmax_core::SigmaXError::Config(e.to_string()))
}

/// 获取风险管理配置
/// 
/// 直接从专门的风险配置服务获取
pub async fn get_risk_config() -> SigmaXResult<RiskManagementConfig> {
    let config_manager = get_global_config_manager();
    config_manager.risk_config.get_config().await
        .map_err(|e| sigmax_core::SigmaXError::Config(e.to_string()))
}

/// 获取策略配置
/// 
/// 直接从专门的策略配置服务获取
pub async fn get_strategy_config() -> SigmaXResult<StrategySystemConfig> {
    let config_manager = get_global_config_manager();
    config_manager.strategy_config.get_config().await
        .map_err(|e| sigmax_core::SigmaXError::Config(e.to_string()))
}

/// 获取通知配置
/// 
/// 直接从专门的通知配置服务获取
pub async fn get_notification_config() -> SigmaXResult<NotificationConfig> {
    let config_manager = get_global_config_manager();
    config_manager.notification_config.get_config().await
        .map_err(|e| sigmax_core::SigmaXError::Config(e.to_string()))
}

/// 获取API配置
/// 
/// 直接从专门的API配置服务获取
pub async fn get_api_config() -> SigmaXResult<ApiConfig> {
    let config_manager = get_global_config_manager();
    config_manager.api_config.get_config().await
        .map_err(|e| sigmax_core::SigmaXError::Config(e.to_string()))
}

/// 获取缓存配置
/// 
/// 直接从专门的缓存配置服务获取
pub async fn get_cache_config() -> SigmaXResult<CacheConfig> {
    let config_manager = get_global_config_manager();
    config_manager.cache_config.get_config().await
        .map_err(|e| sigmax_core::SigmaXError::Config(e.to_string()))
}

/// 获取监控配置
/// 
/// 直接从专门的监控配置服务获取
pub async fn get_monitoring_config() -> SigmaXResult<MonitoringConfig> {
    let config_manager = get_global_config_manager();
    config_manager.monitoring_config.get_config().await
        .map_err(|e| sigmax_core::SigmaXError::Config(e.to_string()))
}

// ============================================================================
// 缓存管理函数
// ============================================================================

/// 清除所有配置缓存
/// 
/// 强制下次访问时重新从数据库加载
pub async fn clear_all_config_cache() -> SigmaXResult<()> {
    let config_manager = get_global_config_manager();
    
    // 清除所有配置服务的缓存
    let _ = config_manager.system_config.clear_cache().await;
    let _ = config_manager.trading_config.clear_cache().await;
    let _ = config_manager.risk_config.clear_cache().await;
    let _ = config_manager.strategy_config.clear_cache().await;
    let _ = config_manager.notification_config.clear_cache().await;
    let _ = config_manager.api_config.clear_cache().await;
    let _ = config_manager.cache_config.clear_cache().await;
    let _ = config_manager.monitoring_config.clear_cache().await;
    
    Ok(())
}

/// 重新加载所有配置
/// 
/// 重新从数据库加载所有配置并更新缓存
pub async fn reload_all_configs() -> SigmaXResult<()> {
    let config_manager = get_global_config_manager();
    config_manager.reload_all().await
        .map_err(|e| sigmax_core::SigmaXError::Config(e.to_string()))
}

/// 获取配置管理器健康状态
pub async fn get_config_manager_health() -> SigmaXResult<super::manager::ConfigManagerHealth> {
    let config_manager = get_global_config_manager();
    config_manager.health_check().await
        .map_err(|e| sigmax_core::SigmaXError::Config(e.to_string()))
}

// ============================================================================
// 配置管理器访问
// ============================================================================

/// 获取配置管理器实例
/// 
/// 用于需要直接访问配置管理器的场景
pub fn get_config_manager() -> &'static Arc<ConfigManager> {
    get_global_config_manager()
}

/// 检查配置管理器是否已初始化
pub fn is_config_manager_initialized() -> bool {
    GLOBAL_CONFIG_MANAGER.get().is_some()
}

/// 检查所有配置服务是否已初始化
pub fn are_all_configs_initialized() -> bool {
    if let Some(config_manager) = GLOBAL_CONFIG_MANAGER.get() {
        config_manager.is_initialized()
    } else {
        false
    }
}
