//! SigmaX 缓存服务使用示例
//! 
//! 演示如何使用内存缓存和缓存管理器

use sigmax_database::{MemoryCache, CacheManager, CacheConfig};
use std::{sync::Arc, time::Duration};
use tokio::time::sleep;

#[derive(serde::Serialize, serde::Deserialize, Debug, PartialEq)]
struct MarketData {
    symbol: String,
    price: f64,
    volume: f64,
    timestamp: u64,
}

#[derive(serde::Serialize, serde::Deserialize, Debug, PartialEq)]
struct UserProfile {
    user_id: u64,
    username: String,
    email: String,
    preferences: Vec<String>,
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 SigmaX 缓存服务示例");
    println!("========================");

    // 1. 基础内存缓存使用
    basic_cache_example().await?;
    
    // 2. 缓存管理器使用
    cache_manager_example().await?;
    
    // 3. TTL和过期处理
    ttl_example().await?;
    
    // 4. 缓存统计信息
    stats_example().await?;
    
    // 5. 实际应用场景
    real_world_example().await?;

    println!("\n✅ 所有示例执行完成！");
    Ok(())
}

/// 基础缓存操作示例
async fn basic_cache_example() -> Result<(), Box<dyn std::error::Error>> {
    println!("\n📦 1. 基础缓存操作");
    println!("------------------");
    
    let cache = MemoryCache::new();
    
    // 设置缓存
    cache.set("user:123", b"John Doe", None).await?;
    cache.set("session:abc", b"active", Some(Duration::from_secs(3600))).await?;
    
    // 获取缓存
    if let Some(data) = cache.get("user:123").await? {
        println!("✓ 获取用户数据: {}", String::from_utf8_lossy(&data));
    }
    
    // 检查存在性
    println!("✓ user:123 存在: {}", cache.exists("user:123").await?);
    println!("✓ user:456 存在: {}", cache.exists("user:456").await?);
    
    // 删除缓存
    cache.delete("user:123").await?;
    println!("✓ 删除 user:123 后存在: {}", cache.exists("user:123").await?);
    
    Ok(())
}

/// 缓存管理器使用示例
async fn cache_manager_example() -> Result<(), Box<dyn std::error::Error>> {
    println!("\n🎛️ 2. 缓存管理器使用");
    println!("-------------------");
    
    let cache = Arc::new(MemoryCache::new());
    let manager = CacheManager::new(cache);
    
    // 存储复杂数据类型
    let market_data = MarketData {
        symbol: "BTC/USDT".to_string(),
        price: 45000.0,
        volume: 1234.56,
        timestamp: 1640995200,
    };
    
    manager.set("market:btc", &market_data, Some(Duration::from_secs(60))).await?;
    println!("✓ 存储市场数据: {:?}", market_data);
    
    // 获取复杂数据类型
    if let Some(cached_data) = manager.get::<MarketData>("market:btc").await? {
        println!("✓ 获取市场数据: {:?}", cached_data);
        assert_eq!(cached_data, market_data);
    }
    
    // 使用 get_or_set 模式
    let user_profile = manager.get_or_set::<UserProfile, _, _>(
        "user:profile:123",
        || async {
            // 模拟从数据库加载用户资料
            println!("  📊 从数据库加载用户资料...");
            Ok(UserProfile {
                user_id: 123,
                username: "alice".to_string(),
                email: "<EMAIL>".to_string(),
                preferences: vec!["dark_mode".to_string(), "notifications".to_string()],
            })
        },
        Some(Duration::from_secs(300)),
    ).await?;
    
    println!("✓ 用户资料: {:?}", user_profile);
    
    // 第二次调用应该从缓存获取
    let cached_profile = manager.get_or_set::<UserProfile, _, _>(
        "user:profile:123",
        || async {
            println!("  ❌ 这不应该被调用！");
            Err(sigmax_core::SigmaXError::Internal("不应该调用".to_string()))
        },
        Some(Duration::from_secs(300)),
    ).await?;
    
    println!("✓ 缓存的用户资料: {:?}", cached_profile);
    
    Ok(())
}

/// TTL和过期处理示例
async fn ttl_example() -> Result<(), Box<dyn std::error::Error>> {
    println!("\n⏰ 3. TTL和过期处理");
    println!("------------------");
    
    let cache = MemoryCache::new();
    
    // 设置短期缓存
    cache.set("temp:data", b"temporary", Some(Duration::from_millis(500))).await?;
    println!("✓ 设置临时数据，TTL: 500ms");
    
    // 立即检查
    println!("✓ 立即检查存在: {}", cache.exists("temp:data").await?);
    
    // 等待一半时间
    sleep(Duration::from_millis(250)).await;
    println!("✓ 250ms后检查存在: {}", cache.exists("temp:data").await?);
    
    // 等待过期
    sleep(Duration::from_millis(300)).await;
    println!("✓ 550ms后检查存在: {}", cache.exists("temp:data").await?);
    
    // 动态设置过期时间
    cache.set("dynamic:data", b"will expire", None).await?;
    println!("✓ 设置永久数据");
    
    cache.expire("dynamic:data", Duration::from_millis(200)).await?;
    println!("✓ 设置200ms过期时间");
    
    sleep(Duration::from_millis(100)).await;
    println!("✓ 100ms后存在: {}", cache.exists("dynamic:data").await?);
    
    sleep(Duration::from_millis(150)).await;
    println!("✓ 250ms后存在: {}", cache.exists("dynamic:data").await?);
    
    Ok(())
}

/// 缓存统计信息示例
async fn stats_example() -> Result<(), Box<dyn std::error::Error>> {
    println!("\n📊 4. 缓存统计信息");
    println!("------------------");
    
    let cache = MemoryCache::new();
    
    // 初始统计
    let stats = cache.stats().await?;
    println!("✓ 初始统计: {:?}", stats);
    
    // 添加一些数据
    for i in 0..5 {
        cache.set(&format!("key:{}", i), format!("value:{}", i).as_bytes(), None).await?;
    }
    
    // 模拟一些访问
    cache.get("key:0").await?; // 命中
    cache.get("key:1").await?; // 命中
    cache.get("key:999").await?; // 未命中
    cache.get("nonexistent").await?; // 未命中
    
    let stats = cache.stats().await?;
    println!("✓ 操作后统计:");
    println!("  - 总条目: {}", stats.total_entries);
    println!("  - 命中次数: {}", stats.hit_count);
    println!("  - 未命中次数: {}", stats.miss_count);
    println!("  - 命中率: {:.2}%", stats.hit_rate * 100.0);
    println!("  - 内存使用: {} bytes", stats.memory_usage);
    
    Ok(())
}

/// 实际应用场景示例
async fn real_world_example() -> Result<(), Box<dyn std::error::Error>> {
    println!("\n🌍 5. 实际应用场景");
    println!("------------------");
    
    // 配置缓存
    let config = CacheConfig {
        max_entries: 1000,
        default_ttl: Some(Duration::from_secs(300)), // 5分钟默认TTL
        cleanup_interval: Duration::from_secs(60),   // 1分钟清理间隔
    };
    
    let cache = Arc::new(MemoryCache::with_config(config));
    let manager = CacheManager::new(cache);
    
    println!("✓ 创建配置化缓存管理器");
    
    // 模拟市场数据缓存
    let symbols = ["BTC/USDT", "ETH/USDT", "ADA/USDT", "DOT/USDT"];
    
    for symbol in &symbols {
        let market_data = MarketData {
            symbol: symbol.to_string(),
            price: 1000.0 + (symbol.len() as f64 * 100.0),
            volume: 500.0 + (symbol.len() as f64 * 50.0),
            timestamp: 1640995200,
        };
        
        manager.set(
            &format!("market:{}", symbol.replace("/", "_")),
            &market_data,
            Some(Duration::from_secs(30)), // 30秒市场数据TTL
        ).await?;
    }
    
    println!("✓ 缓存了 {} 个市场数据", symbols.len());
    
    // 模拟用户会话缓存
    for user_id in 1..=10 {
        manager.set(
            &format!("session:user:{}", user_id),
            &format!("session_token_{}", user_id),
            Some(Duration::from_secs(3600)), // 1小时会话TTL
        ).await?;
    }
    
    println!("✓ 缓存了 10 个用户会话");
    
    // 获取缓存统计
    let stats = manager.get_stats().await?;
    println!("✓ 最终缓存统计:");
    println!("  - 总条目: {}", stats.total_entries);
    println!("  - 内存使用: {} bytes", stats.memory_usage);
    
    // 模拟缓存命中场景
    if let Some(btc_data) = manager.get::<MarketData>("market:BTC_USDT").await? {
        println!("✓ 快速获取BTC市场数据: {:.2} USDT", btc_data.price);
    }
    
    // 模拟缓存未命中和自动加载
    let expensive_calculation = manager.get_or_set::<String, _, _>(
        "expensive:calculation:result",
        || async {
            println!("  🔄 执行昂贵的计算...");
            sleep(Duration::from_millis(100)).await; // 模拟计算时间
            Ok("计算结果: 42".to_string())
        },
        Some(Duration::from_secs(600)), // 10分钟缓存
    ).await?;
    
    println!("✓ 计算结果: {}", expensive_calculation);
    
    Ok(())
}
