[package]
name = "sigmax-core"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
repository.workspace = true

[dependencies]
serde.workspace = true
serde_json.workspace = true
chrono.workspace = true
uuid.workspace = true
rust_decimal.workspace = true
anyhow.workspace = true
thiserror.workspace = true
async-trait = "0.1"
tokio = { version = "1.0", features = ["full"] }
toml = "0.8"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["json", "env-filter"] }
tracing-appender = "0.2"
validator.workspace = true
# SQLx for compile-time checked queries
sqlx.workspace = true

[dev-dependencies]
tempfile = "3.0"
