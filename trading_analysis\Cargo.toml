[package]
name = "sigmax-trading-analysis"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
repository.workspace = true
description = "Comprehensive trading analysis and performance metrics module for SigmaX trading system"
keywords = ["trading", "analysis", "performance", "metrics", "finance", "fifo"]
categories = ["finance", "algorithms"]

[dependencies]
# Core dependencies
sigmax-core.workspace = true
chrono.workspace = true
rust_decimal = { workspace = true, features = ["maths"] }
serde.workspace = true
tracing.workspace = true

# Standard library
uuid.workspace = true

[dev-dependencies]
# Test dependencies
tokio = { workspace = true, features = ["test-util"] }

[features]
default = []
# 可选特性：支持更多序列化格式
extra-serde = ["serde/derive"]
