//! 核心数据模型
//!
//! 该模块定义了系统中所有核心的领域对象，按照职责划分为不同的子模块。
//!
//! 🔥 移植说明：
//! models_old.rs 中的配置相关内容已移植到 config/ 模块中，
//! 包括 RiskManagementConfig、MonitoringConfig、CacheConfig 等。

// 声明子模块
pub mod account;
pub mod engine;
pub mod market_data;
pub mod order;
pub mod strategy;
pub mod trade;

// 从子模块中重新导出关键的数据模型，方便外部引用
// 例如，其他模块可以直接使用 `crate::models::Order` 而不是 `crate::models::order::Order`
pub use account::{Balance, Position};

pub use engine::{
    EngineConfig, EngineExchangeConfig, EngineInfo, EngineRiskConfig, EngineStatistics,
    EngineStrategyConfig,
};
pub use market_data::{
    AggregatedPrice, Candle, DataProviderConfig, DataProviderType, DataQuality, OrderBook,
    PriceStream,
};
pub use order::Order;
pub use strategy::{
    StrategyConfig, StrategyInfo, StrategyPerformance, StrategyState,
};
pub use trade::Trade;