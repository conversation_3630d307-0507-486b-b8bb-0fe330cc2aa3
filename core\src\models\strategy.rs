//! 策略相关的数据模型
//!
//! 🔥 重构说明：
//! 移除了重复的GridConfig相关结构，这些配置已移至strategies模块中的具体策略实现。
//! 本模块现在专注于策略的核心业务实体模型，而非配置结构。

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

use crate::{
    Amount, ExchangeId, StrategyId, StrategyStatus, TradingPair,
};

/// 策略状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StrategyState {
    pub strategy_id: StrategyId,
    pub status: StrategyStatus,
    pub config: serde_json::Value,
    pub state_data: serde_json::Value,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 策略信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StrategyInfo {
    pub id: StrategyId,
    pub name: String,
    pub strategy_type: String,
    pub status: StrategyStatus,
    pub trading_pair: TradingPair,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 策略配置
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct StrategyConfig {
    pub strategy_type: String,
    pub trading_pair: TradingPair,
    pub exchange_id: ExchangeId,
    pub initial_capital: Amount,
    pub parameters: serde_json::Value,
    pub risk_config: serde_json::Value,
    pub enabled: bool,
}

impl Default for StrategyConfig {
    fn default() -> Self {
        Self {
            strategy_type: "asymmetric_volatility_grid".to_string(),
            trading_pair: crate::TradingPair::new("BTC".to_string(), "USDT".to_string()),
            exchange_id: "binance".to_string().into(),
            initial_capital: rust_decimal::Decimal::new(10000, 0), // 10000 USDT
            parameters: serde_json::json!({}),
            risk_config: serde_json::json!({}),
            enabled: true,
        }
    }
}

/// 策略性能统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StrategyPerformance {
    pub strategy_id: StrategyId,
    pub total_return: rust_decimal::Decimal,
    pub max_drawdown: rust_decimal::Decimal,
    pub sharpe_ratio: rust_decimal::Decimal,
    pub total_trades: u64,
    pub win_rate: rust_decimal::Decimal,
    pub profit_factor: rust_decimal::Decimal,
    pub start_time: DateTime<Utc>,
    pub end_time: DateTime<Utc>,
}