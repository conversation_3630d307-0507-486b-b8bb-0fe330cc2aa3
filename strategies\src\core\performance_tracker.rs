//! 策略性能跟踪模块
//!
//! 为策略适应性评估提供性能数据支持
//! 当前实现为基础架构，为未来的智能切换做准备

use serde::{Deserialize, Serialize};
use std::collections::VecDeque;
use chrono::{DateTime, Utc};
use sigmax_core::{SigmaXResult, StrategyId};
use tracing::debug;

use super::strategy_type::StrategyType;
use super::market_signal::MarketState;

/// 策略性能指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    pub strategy_id: StrategyId,
    pub strategy_type: StrategyType,
    pub start_time: DateTime<Utc>,
    pub end_time: Option<DateTime<Utc>>,
    pub total_return: f64,
    pub annualized_return: f64,
    pub sharpe_ratio: f64,
    pub max_drawdown: f64,
    pub win_rate: f64,
    pub total_trades: u64,
    pub winning_trades: u64,
    pub losing_trades: u64,
    pub average_win: f64,
    pub average_loss: f64,
    pub profit_factor: f64,
    pub volatility: f64,
    pub market_conditions: Vec<MarketState>,
}

impl Default for PerformanceMetrics {
    fn default() -> Self {
        Self {
            strategy_id: uuid::Uuid::new_v4(),
            strategy_type: StrategyType::AsymmetricVolatilityGrid,
            start_time: Utc::now(),
            end_time: None,
            total_return: 0.0,
            annualized_return: 0.0,
            sharpe_ratio: 0.0,
            max_drawdown: 0.0,
            win_rate: 0.0,
            total_trades: 0,
            winning_trades: 0,
            losing_trades: 0,
            average_win: 0.0,
            average_loss: 0.0,
            profit_factor: 0.0,
            volatility: 0.0,
            market_conditions: Vec::new(),
        }
    }
}

/// 交易记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TradeRecord {
    pub trade_id: uuid::Uuid,
    pub strategy_id: StrategyId,
    pub timestamp: DateTime<Utc>,
    pub symbol: String,
    pub side: String, // "Buy" or "Sell"
    pub quantity: f64,
    pub price: f64,
    pub pnl: f64,
    pub commission: f64,
    pub market_state: MarketState,
}

/// 权益曲线点
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EquityPoint {
    pub timestamp: DateTime<Utc>,
    pub equity: f64,
    pub drawdown: f64,
    pub daily_return: f64,
}

/// 策略性能跟踪器
pub struct StrategyPerformanceTracker {
    /// 策略ID
    strategy_id: StrategyId,
    /// 策略类型
    strategy_type: StrategyType,
    /// 初始资金
    initial_capital: f64,
    /// 当前权益
    current_equity: f64,
    /// 权益曲线
    equity_curve: VecDeque<EquityPoint>,
    /// 交易记录
    trade_history: VecDeque<TradeRecord>,
    /// 日收益率
    daily_returns: VecDeque<f64>,
    /// 最大历史长度
    max_history_length: usize,
    /// 开始时间
    start_time: DateTime<Utc>,
}

impl StrategyPerformanceTracker {
    /// 创建新的性能跟踪器
    pub fn new(
        strategy_id: StrategyId,
        strategy_type: StrategyType,
        initial_capital: f64,
    ) -> Self {
        let start_time = Utc::now();
        let mut tracker = Self {
            strategy_id,
            strategy_type,
            initial_capital,
            current_equity: initial_capital,
            equity_curve: VecDeque::new(),
            trade_history: VecDeque::new(),
            daily_returns: VecDeque::new(),
            max_history_length: 1000,
            start_time,
        };
        
        // 添加初始权益点
        tracker.equity_curve.push_back(EquityPoint {
            timestamp: start_time,
            equity: initial_capital,
            drawdown: 0.0,
            daily_return: 0.0,
        });
        
        tracker
    }
    
    /// 记录交易
    pub async fn record_trade(&mut self, trade: TradeRecord) -> SigmaXResult<()> {
        debug!("记录交易: {:?}", trade);
        
        // 更新当前权益
        self.current_equity += trade.pnl - trade.commission;
        
        // 添加交易记录
        self.trade_history.push_back(trade);
        
        // 限制历史长度
        while self.trade_history.len() > self.max_history_length {
            self.trade_history.pop_front();
        }
        
        // 更新权益曲线
        self.update_equity_curve().await?;
        
        Ok(())
    }
    
    /// 更新权益曲线
    async fn update_equity_curve(&mut self) -> SigmaXResult<()> {
        let now = Utc::now();
        
        // 计算当前回撤
        let peak_equity = self.equity_curve.iter()
            .map(|point| point.equity)
            .fold(0.0, f64::max);
        
        let current_drawdown = if peak_equity > 0.0 {
            (peak_equity - self.current_equity) / peak_equity
        } else {
            0.0
        };
        
        // 计算日收益率
        let daily_return = if let Some(last_point) = self.equity_curve.back() {
            if last_point.equity > 0.0 {
                (self.current_equity - last_point.equity) / last_point.equity
            } else {
                0.0
            }
        } else {
            0.0
        };
        
        // 添加新的权益点
        let equity_point = EquityPoint {
            timestamp: now,
            equity: self.current_equity,
            drawdown: current_drawdown,
            daily_return,
        };
        
        self.equity_curve.push_back(equity_point);
        self.daily_returns.push_back(daily_return);
        
        // 限制历史长度
        while self.equity_curve.len() > self.max_history_length {
            self.equity_curve.pop_front();
        }
        while self.daily_returns.len() > self.max_history_length {
            self.daily_returns.pop_front();
        }
        
        Ok(())
    }
    
    /// 计算性能指标
    pub async fn calculate_metrics(&self) -> SigmaXResult<PerformanceMetrics> {
        let total_return = if self.initial_capital > 0.0 {
            (self.current_equity - self.initial_capital) / self.initial_capital
        } else {
            0.0
        };
        
        let duration = Utc::now() - self.start_time;
        let years = duration.num_days() as f64 / 365.25;
        let annualized_return = if years > 0.0 {
            (1.0 + total_return).powf(1.0 / years) - 1.0
        } else {
            0.0
        };
        
        let sharpe_ratio = self.calculate_sharpe_ratio();
        let max_drawdown = self.calculate_max_drawdown();
        let (win_rate, total_trades, winning_trades, losing_trades) = self.calculate_trade_stats();
        let (average_win, average_loss, profit_factor) = self.calculate_trade_metrics();
        let volatility = self.calculate_volatility();
        
        Ok(PerformanceMetrics {
            strategy_id: self.strategy_id,
            strategy_type: self.strategy_type,
            start_time: self.start_time,
            end_time: None,
            total_return,
            annualized_return,
            sharpe_ratio,
            max_drawdown,
            win_rate,
            total_trades,
            winning_trades,
            losing_trades,
            average_win,
            average_loss,
            profit_factor,
            volatility,
            market_conditions: Vec::new(), // 简化实现
        })
    }
    
    /// 计算夏普比率
    fn calculate_sharpe_ratio(&self) -> f64 {
        if self.daily_returns.is_empty() {
            return 0.0;
        }
        
        let mean_return: f64 = self.daily_returns.iter().sum::<f64>() / self.daily_returns.len() as f64;
        let variance: f64 = self.daily_returns.iter()
            .map(|r| (r - mean_return).powi(2))
            .sum::<f64>() / self.daily_returns.len() as f64;
        
        let std_dev = variance.sqrt();
        
        if std_dev > 0.0 {
            mean_return / std_dev * (252.0_f64).sqrt() // 年化
        } else {
            0.0
        }
    }
    
    /// 计算最大回撤
    fn calculate_max_drawdown(&self) -> f64 {
        self.equity_curve.iter()
            .map(|point| point.drawdown)
            .fold(0.0, f64::max)
    }
    
    /// 计算交易统计
    fn calculate_trade_stats(&self) -> (f64, u64, u64, u64) {
        let total_trades = self.trade_history.len() as u64;
        let winning_trades = self.trade_history.iter()
            .filter(|trade| trade.pnl > 0.0)
            .count() as u64;
        let losing_trades = total_trades - winning_trades;
        
        let win_rate = if total_trades > 0 {
            winning_trades as f64 / total_trades as f64
        } else {
            0.0
        };
        
        (win_rate, total_trades, winning_trades, losing_trades)
    }
    
    /// 计算交易指标
    fn calculate_trade_metrics(&self) -> (f64, f64, f64) {
        let winning_trades: Vec<&TradeRecord> = self.trade_history.iter()
            .filter(|trade| trade.pnl > 0.0)
            .collect();
        
        let losing_trades: Vec<&TradeRecord> = self.trade_history.iter()
            .filter(|trade| trade.pnl < 0.0)
            .collect();
        
        let average_win = if !winning_trades.is_empty() {
            winning_trades.iter().map(|trade| trade.pnl).sum::<f64>() / winning_trades.len() as f64
        } else {
            0.0
        };
        
        let average_loss = if !losing_trades.is_empty() {
            losing_trades.iter().map(|trade| trade.pnl.abs()).sum::<f64>() / losing_trades.len() as f64
        } else {
            0.0
        };
        
        let profit_factor = if average_loss > 0.0 {
            average_win / average_loss
        } else {
            0.0
        };
        
        (average_win, average_loss, profit_factor)
    }
    
    /// 计算波动率
    fn calculate_volatility(&self) -> f64 {
        if self.daily_returns.len() < 2 {
            return 0.0;
        }
        
        let mean_return: f64 = self.daily_returns.iter().sum::<f64>() / self.daily_returns.len() as f64;
        let variance: f64 = self.daily_returns.iter()
            .map(|r| (r - mean_return).powi(2))
            .sum::<f64>() / (self.daily_returns.len() - 1) as f64;
        
        variance.sqrt() * (252.0_f64).sqrt() // 年化波动率
    }
    
    /// 获取当前权益
    pub fn get_current_equity(&self) -> f64 {
        self.current_equity
    }
    
    /// 获取总收益率
    pub fn get_total_return(&self) -> f64 {
        if self.initial_capital > 0.0 {
            (self.current_equity - self.initial_capital) / self.initial_capital
        } else {
            0.0
        }
    }
}
