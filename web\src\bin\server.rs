//! Web服务器启动程序

use sigmax_web::{WebServer, WebServerConfig};
use tracing_subscriber::{fmt, layer::SubscriberExt, util::SubscriberInitExt};
use std::fs;
use chrono::Utc;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 加载 .env 文件
    dotenv::dotenv().ok();
    // 创建logs目录
    fs::create_dir_all("logs").unwrap_or_else(|e| {
        eprintln!("无法创建logs目录: {}", e);
    });

    // 生成带时间戳的日志文件名
    let timestamp = Utc::now().format("%Y-%m-%d_%H-%M-%S");
    let log_file = format!("logs/sigmax_server_{}.log", timestamp);

    // 初始化日志 - 同时输出到控制台和文件
    let file_appender = tracing_appender::rolling::never("logs", format!("sigmax_server_{}.log", timestamp));
    let (non_blocking, _guard) = tracing_appender::non_blocking(file_appender);

    tracing_subscriber::registry()
        .with(fmt::layer().with_writer(std::io::stdout))  // 控制台输出
        .with(fmt::layer().with_writer(non_blocking).with_ansi(false))  // 文件输出
        .init();

    println!("📝 日志文件: {}", log_file);
    println!("💡 实时查看日志命令:");
    println!("   PowerShell: Get-Content {} -Wait", log_file);
    println!("   CMD/Git Bash: tail -f {}", log_file);
    println!();

    // 创建服务器配置
    let config = WebServerConfig {
        host: std::env::var("WEB_HOST").unwrap_or_else(|_| "127.0.0.1".to_string()),
        port: std::env::var("WEB_PORT")
            .unwrap_or_else(|_| "8080".to_string())
            .parse()
            .unwrap_or(8080),
        database_url: std::env::var("DATABASE_URL")
            .expect("DATABASE_URL environment variable must be set"),
    };

    // 创建服务器
    let mut server = WebServer::new(config);

    // 🆕 系统启动时初始化配置缓存
    println!("🔧 Initializing system configurations...");
    if let Err(e) = server.initialize_system_configs().await {
        eprintln!("❌ Failed to initialize system configs: {}", e);
        return Err(e.into());
    }
    println!("✅ System configurations initialized successfully");

    println!("🚀 Starting SigmaX Web Server on http://127.0.0.1:8080");
    println!("  http://127.0.0.1:8080/index.html");

    server.start().await?;

    Ok(())
}
