//! 引擎管理器实现
//!
//! 负责管理多个交易引擎实例的生命周期，包括创建、启动、停止和监控

use std::{
    collections::HashMap,
    sync::Arc,
};
use tokio::sync::RwLock;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use serde::{Serialize, Deserialize};

use sigmax_core::{
    SigmaXResult, SigmaXError, EngineId, EngineType, EngineStatus, EngineConfig,
    EngineInfo, EngineStatistics, TradingEngine, ServiceContainer, EventBus, SystemEvent
};

use crate::{
    BacktestEngine, PaperEngine, EngineFactory,
    error_handling::{ErrorContext, BatchErrorHandler, ResultExt},
};

/// 引擎管理器
pub struct EngineManager {
    /// 存储所有引擎实例
    engines: Arc<RwLock<HashMap<EngineId, Arc<dyn TradingEngine>>>>,
    /// 存储引擎配置
    engine_configs: Arc<RwLock<HashMap<EngineId, EngineConfig>>>,
    /// 事件总线
    event_bus: Arc<EventBus>,
    /// 服务容器
    service_container: Arc<dyn ServiceContainer>,
    /// 引擎工厂
    factory: EngineFactory,
}

impl Clone for EngineManager {
    fn clone(&self) -> Self {
        Self {
            engines: self.engines.clone(),
            engine_configs: self.engine_configs.clone(),
            event_bus: self.event_bus.clone(),
            service_container: self.service_container.clone(),
            factory: EngineFactory::new(), // 工厂可以重新创建
        }
    }
}

impl EngineManager {
    /// 创建新的引擎管理器
    pub async fn new(
        event_bus: Arc<EventBus>,
        service_container: Arc<dyn ServiceContainer>,
    ) -> SigmaXResult<Self> {
        Ok(Self {
            engines: Arc::new(RwLock::new(HashMap::new())),
            engine_configs: Arc::new(RwLock::new(HashMap::new())),
            event_bus,
            service_container,
            factory: EngineFactory::new(),
        })
    }

    /// 创建引擎
    pub async fn create_engine(&self, config: EngineConfig) -> SigmaXResult<EngineId> {
        let engine_id = Uuid::new_v4();

        // 使用工厂创建引擎（包含配置验证）
        let engine = self.factory.create_engine(
            config.engine_type,
            config.clone(),
            self.service_container.clone(),
        ).await?;

        // 保存引擎和配置
        {
            let mut engines = self.engines.write().await;
            engines.insert(engine_id, engine);
        }
        {
            let mut configs = self.engine_configs.write().await;
            configs.insert(engine_id, config);
        }

        // 发布引擎创建事件
        self.event_bus.publish(SystemEvent::EngineCreated(engine_id)).await?;

        Ok(engine_id)
    }

    /// 获取引擎实例或返回错误
    async fn get_engine_or_error(&self, engine_id: EngineId) -> SigmaXResult<Arc<dyn TradingEngine>> {
        let engines = self.engines.read().await;
        engines.get(&engine_id)
            .with_context_f(|| format!("Engine {} not found", engine_id))
            .map(|engine| engine.clone())
    }

    /// 启动引擎
    pub async fn start_engine(&self, engine_id: EngineId) -> SigmaXResult<()> {
        let engine = self.get_engine_or_error(engine_id).await?;

        // 统一的引擎启动逻辑
        engine.start().await?;
        self.event_bus.publish(SystemEvent::EngineStarted(engine_id)).await?;

        Ok(())
    }

    /// 停止引擎
    pub async fn stop_engine(&self, engine_id: EngineId) -> SigmaXResult<()> {
        let engine = self.get_engine_or_error(engine_id).await?;

        // 停止引擎
        engine.stop().await?;

        // 发布引擎停止事件
        self.event_bus.publish(SystemEvent::EngineStopped(engine_id)).await?;

        Ok(())
    }

    /// 暂停引擎
    pub async fn pause_engine(&self, engine_id: EngineId) -> SigmaXResult<()> {
        let engine = self.get_engine_or_error(engine_id).await?;

        // 暂停引擎
        engine.pause().await?;

        // 发布引擎暂停事件
        self.event_bus.publish(SystemEvent::EnginePaused(engine_id)).await?;

        Ok(())
    }

    /// 恢复引擎
    pub async fn resume_engine(&self, engine_id: EngineId) -> SigmaXResult<()> {
        let engine = self.get_engine_or_error(engine_id).await?;

        // 恢复引擎
        engine.resume().await?;

        // 发布引擎恢复事件
        self.event_bus.publish(SystemEvent::EngineResumed(engine_id)).await?;

        Ok(())
    }

    /// 删除引擎
    pub async fn remove_engine(&self, engine_id: EngineId) -> SigmaXResult<()> {
        // 首先停止引擎
        if let Ok(_) = self.stop_engine(engine_id).await {
            // 忽略停止错误，可能引擎已经停止
        }

        // 从存储中移除
        {
            let mut engines = self.engines.write().await;
            engines.remove(&engine_id);
        }
        {
            let mut configs = self.engine_configs.write().await;
            configs.remove(&engine_id);
        }

        Ok(())
    }

    /// 获取引擎实例
    pub async fn get_engine(&self, engine_id: EngineId) -> SigmaXResult<Option<Arc<dyn TradingEngine>>> {
        let engines = self.engines.read().await;
        Ok(engines.get(&engine_id).cloned())
    }

    /// 获取引擎状态
    pub async fn get_engine_status(&self, engine_id: EngineId) -> SigmaXResult<EngineStatus> {
        let engine = {
            let engines = self.engines.read().await;
            engines.get(&engine_id)
                .ok_or_else(|| SigmaXError::NotFound(format!("Engine {}", engine_id)))?
                .clone()
        };

        Ok(engine.get_status().await?)
    }

    /// 获取引擎配置
    pub async fn get_engine_config(&self, engine_id: EngineId) -> SigmaXResult<EngineConfig> {
        let configs = self.engine_configs.read().await;
        configs.get(&engine_id)
            .cloned()
            .ok_or_else(|| SigmaXError::NotFound(format!("Engine config {}", engine_id)))
    }

    /// 更新引擎配置
    pub async fn update_engine_config(&self, engine_id: EngineId, config: EngineConfig) -> SigmaXResult<()> {
        // 获取引擎实例
        let engine = {
            let engines = self.engines.read().await;
            engines.get(&engine_id)
                .ok_or_else(|| SigmaXError::NotFound(format!("Engine {}", engine_id)))?
                .clone()
        };

        // 更新引擎配置
        engine.update_config(config.clone()).await?;

        // 更新存储的配置
        {
            let mut configs = self.engine_configs.write().await;
            configs.insert(engine_id, config);
        }

        Ok(())
    }

    /// 获取引擎统计信息
    pub async fn get_engine_statistics(&self, engine_id: EngineId) -> SigmaXResult<EngineStatistics> {
        let engine = {
            let engines = self.engines.read().await;
            engines.get(&engine_id)
                .ok_or_else(|| SigmaXError::NotFound(format!("Engine {}", engine_id)))?
                .clone()
        };

        Ok(engine.get_statistics().await?)
    }

    /// 获取所有引擎信息
    pub async fn get_all_engines(&self) -> SigmaXResult<Vec<EngineInfo>> {
        let engines = self.engines.read().await;
        let configs = self.engine_configs.read().await;

        let mut engine_infos = Vec::new();
        for (engine_id, engine) in engines.iter() {
            if let Some(config) = configs.get(engine_id) {
                let status = engine.get_status().await?;
                let statistics = engine.get_statistics().await?;

                engine_infos.push(EngineInfo {
                    id: *engine_id,
                    engine_type: config.engine_type,
                    status,
                    created_at: config.created_at,
                    updated_at: chrono::Utc::now(),
                    config: config.clone(),
                    statistics,
                });
            }
        }

        Ok(engine_infos)
    }

    /// 获取指定类型的引擎
    pub async fn get_engines_by_type(&self, engine_type: EngineType) -> SigmaXResult<Vec<EngineInfo>> {
        let all_engines = self.get_all_engines().await?;
        Ok(all_engines.into_iter()
            .filter(|info| info.engine_type == engine_type)
            .collect())
    }

    /// 获取运行中的引擎
    pub async fn get_running_engines(&self) -> SigmaXResult<Vec<EngineInfo>> {
        let all_engines = self.get_all_engines().await?;
        Ok(all_engines.into_iter()
            .filter(|info| matches!(info.status, EngineStatus::Running))
            .collect())
    }

    /// 批量启动引擎
    pub async fn start_engines_batch(&self, engine_ids: Vec<EngineId>) -> Vec<SigmaXResult<()>> {
        // 并行启动多个引擎
        use futures::future::join_all;
        
        let futures = engine_ids.into_iter().map(|engine_id| {
            async move {
                self.start_engine(engine_id).await
            }
        });
        
        join_all(futures).await
    }

    /// 停止所有引擎
    pub async fn stop_all_engines(&self) -> SigmaXResult<()> {
        let engine_ids: Vec<EngineId> = {
            let engines = self.engines.read().await;
            engines.keys().cloned().collect()
        };

        // 并行停止所有引擎
        let results = self.stop_engines_batch(engine_ids).await;
        
        // 使用批量错误处理器
        let mut error_handler = BatchErrorHandler::new("停止所有引擎".to_string());
        for result in results {
            error_handler.add_result(result);
        }

        // 记录错误但继续执行
        error_handler.log_and_continue(());
        Ok(())
    }

    /// 批量停止引擎
    pub async fn stop_engines_batch(&self, engine_ids: Vec<EngineId>) -> Vec<SigmaXResult<()>> {
        use futures::future::join_all;
        
        let futures = engine_ids.into_iter().map(|engine_id| {
            async move {
                self.stop_engine(engine_id).await
            }
        });
        
        join_all(futures).await
    }

    /// 获取引擎数量
    pub async fn engine_count(&self) -> usize {
        let engines = self.engines.read().await;
        engines.len()
    }

    /// 获取回测引擎的进度
    pub async fn get_backtest_progress(&self, engine_id: EngineId) -> SigmaXResult<(usize, usize, f64)> {
        let engines = self.engines.read().await;
        let engine = engines.get(&engine_id)
            .ok_or_else(|| SigmaXError::NotFound(format!("Engine {}", engine_id)))?;

        // 尝试将引擎转换为BacktestEngine
        if let Some(backtest_engine) = engine.as_any().downcast_ref::<crate::backtest::BacktestEngine>() {
            let progress = backtest_engine.get_progress().await;
            Ok((progress.current_candle, progress.total_candles, progress.progress_percentage))
        } else {
            Err(SigmaXError::InvalidOperation("Engine is not a backtest engine".to_string()))
        }
    }
}
