//! 策略执行引擎演示
//! 
//! 这个演示展示了如何使用策略执行引擎来：
//! 1. 创建和启动策略
//! 2. 处理市场数据
//! 3. 生成和执行交易信号
//! 4. 监控策略性能
//! 5. 停止策略执行

use std::time::Duration;
use tokio::time::sleep;
use uuid::Uuid;
use rust_decimal::Decimal;
use chrono::Utc;
use serde_json::json;

use sigmax_web::{
    execution_engine::{StrategyExecutionEngine, MarketData, ExecutionStatus},
    services::{StrategyService, RiskService},
    handlers::strategy_handlers::{CreateStrategyRequest, RiskConfigRequest},
};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::init();

    println!("🚀 SigmaX 策略执行引擎演示");
    println!("=" .repeat(50));

    // 1. 创建执行引擎
    println!("\n📦 创建策略执行引擎");
    let execution_engine = create_execution_engine().await?;
    println!("✅ 执行引擎创建成功");

    // 2. 创建测试策略
    println!("\n📝 创建测试策略");
    let strategy_service = create_strategy_service().await?;
    let strategy = create_demo_strategy(&strategy_service).await?;
    let strategy_id = Uuid::parse_str(&strategy.id)?;
    println!("✅ 策略创建成功: {} - {}", strategy_id, strategy.name);

    // 3. 启动策略执行
    println!("\n🚀 启动策略执行");
    execution_engine.start_strategy(strategy_id).await?;
    
    // 检查执行状态
    let status = execution_engine.get_execution_status(strategy_id).await;
    println!("✅ 策略执行状态: {:?}", status);

    // 4. 模拟市场数据流
    println!("\n📊 开始市场数据模拟");
    simulate_market_data(&execution_engine).await?;

    // 5. 监控策略性能
    println!("\n📈 监控策略性能");
    monitor_strategy_performance(&execution_engine, strategy_id).await?;

    // 6. 演示多策略并发执行
    println!("\n🔄 演示多策略并发执行");
    demo_multiple_strategies(&execution_engine, &strategy_service).await?;

    // 7. 停止所有策略
    println!("\n🛑 停止所有策略");
    let running_strategies = execution_engine.get_running_strategies().await;
    for strategy_id in running_strategies {
        execution_engine.stop_strategy(strategy_id).await?;
        println!("✅ 策略 {} 已停止", strategy_id);
    }

    println!("\n🎉 演示完成！");
    println!("=" .repeat(50));

    Ok(())
}

/// 创建策略执行引擎
async fn create_execution_engine() -> Result<StrategyExecutionEngine, Box<dyn std::error::Error>> {
    let database_url = "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require";
    
    let pool = sqlx::PgPool::connect(database_url).await?;
    let strategy_service = std::sync::Arc::new(StrategyService::new(pool.clone()));
    let risk_service = std::sync::Arc::new(RiskService::new(pool));

    Ok(StrategyExecutionEngine::new(strategy_service, risk_service))
}

/// 创建策略服务
async fn create_strategy_service() -> Result<StrategyService, Box<dyn std::error::Error>> {
    let database_url = "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require";
    let pool = sqlx::PgPool::connect(database_url).await?;
    Ok(StrategyService::new(pool))
}

/// 创建演示策略
async fn create_demo_strategy(strategy_service: &StrategyService) -> Result<sigmax_web::handlers::strategy_handlers::StrategyResponse, Box<dyn std::error::Error>> {
    let request = CreateStrategyRequest {
        strategy_type: "grid".to_string(),
        name: "Demo Grid Strategy".to_string(),
        description: Some("演示用的网格策略".to_string()),
        trading_pairs: vec!["BTCUSDT".to_string(), "ETHUSDT".to_string()],
        initial_capital: "50000".to_string(),
        parameters: json!({
            "grid_count": 20,
            "price_range": 0.05,
            "order_size": 500,
            "min_profit": 0.001
        }),
        risk_config: Some(RiskConfigRequest {
            max_position_size: Some("10000".to_string()),
            max_order_size: Some("2000".to_string()),
            stop_loss_percentage: Some("3.0".to_string()),
            take_profit_percentage: Some("15.0".to_string()),
            max_daily_loss: Some("5000".to_string()),
            max_drawdown: Some("10.0".to_string()),
        }),
        enabled: Some(true),
    };

    Ok(strategy_service.create_strategy(request).await?)
}

/// 模拟市场数据
async fn simulate_market_data(execution_engine: &StrategyExecutionEngine) -> Result<(), Box<dyn std::error::Error>> {
    let symbols = vec!["BTCUSDT", "ETHUSDT"];
    let mut prices = vec![50000.0, 3000.0]; // BTC和ETH的初始价格

    for i in 0..10 {
        for (j, symbol) in symbols.iter().enumerate() {
            // 模拟价格波动 (-2% 到 +2%)
            let price_change = (rand::random::<f64>() - 0.5) * 0.04;
            prices[j] *= 1.0 + price_change;

            let market_data = MarketData {
                symbol: symbol.to_string(),
                price: Decimal::from_f64(prices[j]).unwrap_or(Decimal::ZERO),
                volume: Decimal::from_f64(100.0 + rand::random::<f64>() * 500.0).unwrap_or(Decimal::ZERO),
                timestamp: Utc::now(),
            };

            execution_engine.broadcast_market_data(market_data).await?;
            println!("📊 {} 价格更新: ${:.2}", symbol, prices[j]);
        }

        // 等待一段时间模拟实时数据流
        sleep(Duration::from_millis(500)).await;
    }

    Ok(())
}

/// 监控策略性能
async fn monitor_strategy_performance(execution_engine: &StrategyExecutionEngine, strategy_id: Uuid) -> Result<(), Box<dyn std::error::Error>> {
    for i in 1..=5 {
        sleep(Duration::from_secs(2)).await;

        if let Some(performance) = execution_engine.get_strategy_performance(strategy_id).await {
            println!("📈 性能监控 #{}", i);
            println!("   总交易数: {}", performance.total_trades);
            println!("   胜利交易: {}", performance.winning_trades);
            println!("   失败交易: {}", performance.losing_trades);
            println!("   总盈亏: ${}", performance.total_pnl);
            println!("   胜率: {:.2}%", performance.win_rate * Decimal::new(100, 0));
            println!("   当前持仓: {}", performance.current_positions);
            println!("   最后更新: {}", performance.last_updated.format("%H:%M:%S"));
        } else {
            println!("⚠️  无法获取策略性能数据");
        }
    }

    Ok(())
}

/// 演示多策略并发执行
async fn demo_multiple_strategies(execution_engine: &StrategyExecutionEngine, strategy_service: &StrategyService) -> Result<(), Box<dyn std::error::Error>> {
    let mut strategy_ids = Vec::new();

    // 创建多个不同类型的策略
    let strategies = vec![
        ("DCA Strategy 1", "dca", vec!["BTCUSDT".to_string()]),
        ("Grid Strategy 2", "grid", vec!["ETHUSDT".to_string()]),
        ("DCA Strategy 3", "dca", vec!["ADAUSDT".to_string()]),
    ];

    for (name, strategy_type, trading_pairs) in strategies {
        let request = CreateStrategyRequest {
            strategy_type: strategy_type.to_string(),
            name: name.to_string(),
            description: Some(format!("演示用的{}策略", strategy_type)),
            trading_pairs,
            initial_capital: "20000".to_string(),
            parameters: json!({
                "interval_hours": 24,
                "order_size": 200,
                "grid_count": 15
            }),
            risk_config: Some(RiskConfigRequest {
                max_position_size: Some("5000".to_string()),
                max_order_size: Some("1000".to_string()),
                stop_loss_percentage: Some("5.0".to_string()),
                take_profit_percentage: Some("10.0".to_string()),
                max_daily_loss: Some("2000".to_string()),
                max_drawdown: Some("15.0".to_string()),
            }),
            enabled: Some(true),
        };

        let strategy = strategy_service.create_strategy(request).await?;
        let strategy_id = Uuid::parse_str(&strategy.id)?;
        strategy_ids.push(strategy_id);

        // 启动策略
        execution_engine.start_strategy(strategy_id).await?;
        println!("✅ {} 启动成功: {}", name, strategy_id);
    }

    // 等待所有策略运行
    sleep(Duration::from_secs(3)).await;

    // 检查所有策略状态
    let all_status = execution_engine.get_all_execution_status().await;
    println!("📊 所有策略执行状态:");
    for (id, status) in all_status {
        println!("   {}: {:?}", id, status);
    }

    // 模拟更多市场数据
    println!("📊 向所有策略广播市场数据");
    let symbols = vec!["BTCUSDT", "ETHUSDT", "ADAUSDT"];
    for symbol in symbols {
        let market_data = MarketData {
            symbol: symbol.to_string(),
            price: Decimal::from_f64(50000.0 + rand::random::<f64>() * 10000.0).unwrap_or(Decimal::ZERO),
            volume: Decimal::from_f64(200.0).unwrap_or(Decimal::ZERO),
            timestamp: Utc::now(),
        };
        execution_engine.broadcast_market_data(market_data).await?;
    }

    sleep(Duration::from_secs(2)).await;

    // 检查运行中的策略数量
    let running_strategies = execution_engine.get_running_strategies().await;
    println!("🔄 当前运行中的策略数量: {}", running_strategies.len());

    Ok(())
}

// 添加一个简单的随机数生成器模块
mod rand {
    use std::collections::hash_map::DefaultHasher;
    use std::hash::{Hash, Hasher};
    use std::time::{SystemTime, UNIX_EPOCH};

    pub fn random<T: Hash>() -> f64 {
        let mut hasher = DefaultHasher::new();
        SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_nanos().hash(&mut hasher);
        (hasher.finish() % 1000000) as f64 / 1000000.0
    }
}
