use serde::{Serialize, Deserialize};


/// WebSocket服务器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WebSocketServerConfig {
    /// 最大连接数
    pub max_connections: usize,
    /// 心跳间隔（秒）
    pub heartbeat_interval: u64,
    /// 连接超时（秒）
    pub connection_timeout: u64,
    /// 启用压缩
    pub compression_enabled: bool,
    /// 压缩阈值（字节）
    pub compression_threshold: usize,
    /// 消息队列大小
    pub message_queue_size: usize,
    /// 批量处理配置
    pub batch_config: BatchProcessingConfig,
    /// 速率限制配置
    pub rate_limit_config: RateLimitConfig,
}

impl Default for WebSocketServerConfig {
    fn default() -> Self {
        Self {
            max_connections: 10000,
            heartbeat_interval: 30,
            connection_timeout: 300,
            compression_enabled: true,
            compression_threshold: 1024,
            message_queue_size: 1000,
            batch_config: BatchProcessingConfig::default(),
            rate_limit_config: RateLimitConfig::default(),
        }
    }
}

/// 批量处理配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BatchProcessingConfig {
    /// 启用批量处理
    pub enabled: bool,
    /// 批量大小
    pub batch_size: usize,
    /// 批量超时（毫秒）
    pub batch_timeout_ms: u64,
    /// 最大批量延迟（毫秒）
    pub max_batch_delay_ms: u64,
}

impl Default for BatchProcessingConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            batch_size: 100,
            batch_timeout_ms: 10,
            max_batch_delay_ms: 50,
        }
    }
}

/// 速率限制配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimitConfig {
    /// 启用速率限制
    pub enabled: bool,
    /// 全局速率限制（消息/秒）
    pub global_rate_limit: u32,
    /// 每连接速率限制（消息/秒）
    pub per_connection_rate_limit: u32,
    /// 速率限制窗口（秒）
    pub rate_limit_window: u64,
}

impl Default for RateLimitConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            global_rate_limit: 100000,
            per_connection_rate_limit: 100,
            rate_limit_window: 60,
        }
    }
}

/// 频道配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChannelConfig {
    /// 频道名称
    pub name: String,
    /// 是否需要认证
    pub requires_auth: bool,
    /// 最大订阅者数量
    pub max_subscribers: Option<usize>,
    /// 消息缓存大小
    pub buffer_size: usize,
    /// 消息过期时间（秒）
    pub message_ttl: u64,
    /// 当前订阅者数量
    pub current_subscribers: usize,
    /// 总消息数
    pub total_messages: u64,
    /// 最后消息时间
    pub last_message_time: Option<chrono::DateTime<chrono::Utc>>,
}

impl ChannelConfig {
    pub fn new(name: String) -> Self {
        Self {
            name,
            requires_auth: false,
            max_subscribers: None,
            buffer_size: 100,
            message_ttl: 300,
            current_subscribers: 0,
            total_messages: 0,
            last_message_time: None,
        }
    }

    pub fn with_auth(mut self, requires_auth: bool) -> Self {
        self.requires_auth = requires_auth;
        self
    }

    pub fn with_max_subscribers(mut self, max_subscribers: usize) -> Self {
        self.max_subscribers = Some(max_subscribers);
        self
    }

    pub fn with_buffer_size(mut self, buffer_size: usize) -> Self {
        self.buffer_size = buffer_size;
        self
    }

    pub fn with_ttl(mut self, ttl_seconds: u64) -> Self {
        self.message_ttl = ttl_seconds;
        self
    }
}

/// 连接类型配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConnectionTypeConfig {
    /// 连接类型
    pub connection_type: String,
    /// 最大订阅数
    pub max_subscriptions: usize,
    /// 消息速率限制（消息/秒）
    pub rate_limit: u32,
    /// 允许的频道
    pub allowed_channels: Vec<String>,
    /// 当前连接数
    pub current_connections: usize,
}

impl ConnectionTypeConfig {
    pub fn user() -> Self {
        Self {
            connection_type: "User".to_string(),
            max_subscriptions: 5,
            rate_limit: 50,
            allowed_channels: vec!["market_data".to_string(), "system".to_string()],
            current_connections: 0,
        }
    }

    pub fn admin() -> Self {
        Self {
            connection_type: "Admin".to_string(),
            max_subscriptions: 50,
            rate_limit: 1000,
            allowed_channels: vec!["*".to_string()], // 所有频道
            current_connections: 0,
        }
    }

    pub fn api_client() -> Self {
        Self {
            connection_type: "ApiClient".to_string(),
            max_subscriptions: 20,
            rate_limit: 500,
            allowed_channels: vec![
                "orders".to_string(),
                "trades".to_string(),
                "engines".to_string(),
                "market_data".to_string(),
            ],
            current_connections: 0,
        }
    }

    pub fn monitor() -> Self {
        Self {
            connection_type: "Monitor".to_string(),
            max_subscriptions: 10,
            rate_limit: 100,
            allowed_channels: vec!["system".to_string(), "monitoring".to_string()],
            current_connections: 0,
        }
    }
}

/// WebSocket完整配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WebSocketConfig {
    /// 服务器配置
    pub server_config: WebSocketServerConfig,
    /// 频道配置
    pub channels: Vec<ChannelConfig>,
    /// 连接类型配置
    pub connection_types: Vec<ConnectionTypeConfig>,
}

impl Default for WebSocketConfig {
    fn default() -> Self {
        Self {
            server_config: WebSocketServerConfig::default(),
            channels: Self::get_default_channels(),
            connection_types: vec![
                ConnectionTypeConfig::user(),
                ConnectionTypeConfig::admin(),
                ConnectionTypeConfig::api_client(),
                ConnectionTypeConfig::monitor(),
            ],
        }
    }
}

impl WebSocketConfig {
    /// 获取默认频道配置（包含实时数据频道）
    pub fn get_default_channels() -> Vec<ChannelConfig> {
        vec![
            // 基础频道
            ChannelConfig::new("engines".to_string())
                .with_max_subscribers(1000)
                .with_buffer_size(100)
                .with_ttl(300),
            ChannelConfig::new("system".to_string())
                .with_buffer_size(50)
                .with_ttl(120),
            ChannelConfig::new("admin".to_string())
                .with_auth(true)
                .with_max_subscribers(10)
                .with_buffer_size(100)
                .with_ttl(3600),

            // 高频实时数据频道
            ChannelConfig::new("orders".to_string())
                .with_auth(true)
                .with_max_subscribers(1000)
                .with_buffer_size(500)
                .with_ttl(300),
            ChannelConfig::new("trades".to_string())
                .with_auth(true)
                .with_max_subscribers(1000)
                .with_buffer_size(500)
                .with_ttl(300),
            ChannelConfig::new("strategy_execution".to_string())
                .with_auth(true)
                .with_max_subscribers(500)
                .with_buffer_size(200)
                .with_ttl(600),
            ChannelConfig::new("risk_alerts".to_string())
                .with_auth(true)
                .with_max_subscribers(100)
                .with_buffer_size(100)
                .with_ttl(3600),

            // 中频实时数据频道
            ChannelConfig::new("portfolio".to_string())
                .with_auth(true)
                .with_max_subscribers(500)
                .with_buffer_size(200)
                .with_ttl(600),
            ChannelConfig::new("market_data".to_string())
                .with_max_subscribers(2000)
                .with_buffer_size(1000)
                .with_ttl(60),
            ChannelConfig::new("system_status".to_string())
                .with_max_subscribers(200)
                .with_buffer_size(100)
                .with_ttl(300),

            // 低频实时数据频道
            ChannelConfig::new("backtest_progress".to_string())
                .with_auth(true)
                .with_max_subscribers(100)
                .with_buffer_size(50)
                .with_ttl(1800),
            ChannelConfig::new("reports".to_string())
                .with_auth(true)
                .with_max_subscribers(50)
                .with_buffer_size(30)
                .with_ttl(3600),
        ]
    }

    /// 获取频道配置
    pub fn get_channel_config(&self, channel_name: &str) -> Option<&ChannelConfig> {
        self.channels.iter().find(|c| c.name == channel_name)
    }

    /// 获取连接类型配置
    pub fn get_connection_type_config(&self, connection_type: &str) -> Option<&ConnectionTypeConfig> {
        self.connection_types.iter().find(|c| c.connection_type == connection_type)
    }

    /// 验证频道是否存在
    pub fn is_valid_channel(&self, channel_name: &str) -> bool {
        self.channels.iter().any(|c| c.name == channel_name)
    }

    /// 检查连接类型是否可以访问频道
    pub fn can_access_channel(&self, connection_type: &str, channel_name: &str) -> bool {
        if let Some(config) = self.get_connection_type_config(connection_type) {
            config.allowed_channels.contains(&"*".to_string()) ||
            config.allowed_channels.contains(&channel_name.to_string())
        } else {
            false
        }
    }

    /// 获取连接类型的速率限制
    pub fn get_rate_limit(&self, connection_type: &str) -> u32 {
        self.get_connection_type_config(connection_type)
            .map(|c| c.rate_limit)
            .unwrap_or(50) // 默认限制
    }

    /// 获取连接类型的最大订阅数
    pub fn get_max_subscriptions(&self, connection_type: &str) -> usize {
        self.get_connection_type_config(connection_type)
            .map(|c| c.max_subscriptions)
            .unwrap_or(5) // 默认限制
    }
}
