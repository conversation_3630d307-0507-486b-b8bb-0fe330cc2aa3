//! SQL 风险管理仓储实现
//!
//! 基于 SQLx 的风险管理数据访问实现

use std::sync::Arc;
use async_trait::async_trait;
use sqlx::Row;
use sigmax_core::{SigmaXResult, SigmaXError};
use uuid::Uuid;

use crate::DatabaseManager;
use crate::repositories::traits::risk_repository::{
    RiskRepository, RiskCheckRecord, RiskRuleRecord,
    RiskViolationRecord, RiskQueryFilter, RiskRuleFilter, Pagination
};

/// SQL 风险仓储实现
pub struct SqlRiskRepository {
    db: Arc<DatabaseManager>,
}

impl SqlRiskRepository {
    /// 创建新的 SQL 风险仓储实例
    pub fn new(db: Arc<DatabaseManager>) -> Self {
        Self { db }
    }

    /// 将数据库行转换为风险检查记录
    fn row_to_risk_check(&self, row: sqlx::postgres::PgRow) -> SigmaXResult<RiskCheckRecord> {
        Ok(RiskCheckRecord {
            id: row.try_get("id")
                .map_err(|e| SigmaXError::Database(format!("获取id失败: {}", e)))?,
            timestamp: row.try_get("timestamp")
                .map_err(|e| SigmaXError::Database(format!("获取timestamp失败: {}", e)))?,
            trading_pair: row.try_get("trading_pair")
                .map_err(|e| SigmaXError::Database(format!("获取trading_pair失败: {}", e)))?,
            side: row.try_get("side")
                .map_err(|e| SigmaXError::Database(format!("获取side失败: {}", e)))?,
            quantity: row.try_get("quantity")
                .map_err(|e| SigmaXError::Database(format!("获取quantity失败: {}", e)))?,
            price: row.try_get("price")
                .map_err(|e| SigmaXError::Database(format!("获取price失败: {}", e)))?,
            order_type: row.try_get("order_type")
                .map_err(|e| SigmaXError::Database(format!("获取order_type失败: {}", e)))?,
            result: row.try_get("result")
                .map_err(|e| SigmaXError::Database(format!("获取result失败: {}", e)))?,
            risk_score: row.try_get("risk_score")
                .map_err(|e| SigmaXError::Database(format!("获取risk_score失败: {}", e)))?,
            confidence_level: row.try_get("confidence_level")
                .map_err(|e| SigmaXError::Database(format!("获取confidence_level失败: {}", e)))?,
            violations: row.try_get("violations")
                .map_err(|e| SigmaXError::Database(format!("获取violations失败: {}", e)))?,
            warnings: row.try_get("warnings")
                .map_err(|e| SigmaXError::Database(format!("获取warnings失败: {}", e)))?,
            recommendations: row.try_get("recommendations")
                .map_err(|e| SigmaXError::Database(format!("获取recommendations失败: {}", e)))?,
            applied_rules: row.try_get("applied_rules")
                .map_err(|e| SigmaXError::Database(format!("获取applied_rules失败: {}", e)))?,
            max_allowed_quantity: row.try_get("max_allowed_quantity")
                .map_err(|e| SigmaXError::Database(format!("获取max_allowed_quantity失败: {}", e)))?,
            suggested_price_min: row.try_get("suggested_price_min")
                .map_err(|e| SigmaXError::Database(format!("获取suggested_price_min失败: {}", e)))?,
            suggested_price_max: row.try_get("suggested_price_max")
                .map_err(|e| SigmaXError::Database(format!("获取suggested_price_max失败: {}", e)))?,
            alternative_suggestions: row.try_get("alternative_suggestions")
                .map_err(|e| SigmaXError::Database(format!("获取alternative_suggestions失败: {}", e)))?,
            processing_time_ms: row.try_get("processing_time_ms")
                .map_err(|e| SigmaXError::Database(format!("获取processing_time_ms失败: {}", e)))?,
            rules_evaluated: row.try_get("rules_evaluated")
                .map_err(|e| SigmaXError::Database(format!("获取rules_evaluated失败: {}", e)))?,
            cache_hit_rate: row.try_get("cache_hit_rate")
                .map_err(|e| SigmaXError::Database(format!("获取cache_hit_rate失败: {}", e)))?,
            strategy_id: row.try_get("strategy_id")
                .map_err(|e| SigmaXError::Database(format!("获取strategy_id失败: {}", e)))?,
            portfolio_id: row.try_get("portfolio_id")
                .map_err(|e| SigmaXError::Database(format!("获取portfolio_id失败: {}", e)))?,
            engine_id: row.try_get("engine_id")
                .map_err(|e| SigmaXError::Database(format!("获取engine_id失败: {}", e)))?,
            session_id: row.try_get("session_id")
                .map_err(|e| SigmaXError::Database(format!("获取session_id失败: {}", e)))?,
            created_at: row.try_get("created_at")
                .map_err(|e| SigmaXError::Database(format!("获取created_at失败: {}", e)))?,
            check_date: row.try_get("check_date")
                .map_err(|e| SigmaXError::Database(format!("获取check_date失败: {}", e)))?,
        })
    }

    /// 将数据库行转换为风险规则记录
    fn row_to_risk_rule(&self, row: sqlx::postgres::PgRow) -> SigmaXResult<RiskRuleRecord> {
        Ok(RiskRuleRecord {
            id: row.try_get("id")
                .map_err(|e| SigmaXError::Database(format!("获取id失败: {}", e)))?,
            name: row.try_get("name")
                .map_err(|e| SigmaXError::Database(format!("获取name失败: {}", e)))?,
            description: row.try_get("description")
                .map_err(|e| SigmaXError::Database(format!("获取description失败: {}", e)))?,
            category: row.try_get("category")
                .map_err(|e| SigmaXError::Database(format!("获取category失败: {}", e)))?,
            rule_type: row.try_get("rule_type")
                .map_err(|e| SigmaXError::Database(format!("获取rule_type失败: {}", e)))?,
            parameters: row.try_get("parameters")
                .map_err(|e| SigmaXError::Database(format!("获取parameters失败: {}", e)))?,
            conditions: row.try_get("conditions")
                .map_err(|e| SigmaXError::Database(format!("获取conditions失败: {}", e)))?,
            enabled: row.try_get("enabled")
                .map_err(|e| SigmaXError::Database(format!("获取enabled失败: {}", e)))?,
            priority: row.try_get("priority")
                .map_err(|e| SigmaXError::Database(format!("获取priority失败: {}", e)))?,
            strategy_type: row.try_get("strategy_type")
                .map_err(|e| SigmaXError::Database(format!("获取strategy_type失败: {}", e)))?,
            trading_pairs: row.try_get("trading_pairs")
                .map_err(|e| SigmaXError::Database(format!("获取trading_pairs失败: {}", e)))?,
            execution_count: row.try_get("execution_count")
                .map_err(|e| SigmaXError::Database(format!("获取execution_count失败: {}", e)))?,
            success_count: row.try_get("success_count")
                .map_err(|e| SigmaXError::Database(format!("获取success_count失败: {}", e)))?,
            failure_count: row.try_get("failure_count")
                .map_err(|e| SigmaXError::Database(format!("获取failure_count失败: {}", e)))?,
            last_executed_at: row.try_get("last_executed_at")
                .map_err(|e| SigmaXError::Database(format!("获取last_executed_at失败: {}", e)))?,
            average_execution_time_ms: row.try_get("average_execution_time_ms")
                .map_err(|e| SigmaXError::Database(format!("获取average_execution_time_ms失败: {}", e)))?,
            version: row.try_get("version")
                .map_err(|e| SigmaXError::Database(format!("获取version失败: {}", e)))?,
            is_active: row.try_get("is_active")
                .map_err(|e| SigmaXError::Database(format!("获取is_active失败: {}", e)))?,
            created_at: row.try_get("created_at")
                .map_err(|e| SigmaXError::Database(format!("获取created_at失败: {}", e)))?,
            updated_at: row.try_get("updated_at")
                .map_err(|e| SigmaXError::Database(format!("获取updated_at失败: {}", e)))?,
            created_by: row.try_get("created_by")
                .map_err(|e| SigmaXError::Database(format!("获取created_by失败: {}", e)))?,
            updated_by: row.try_get("updated_by")
                .map_err(|e| SigmaXError::Database(format!("获取updated_by失败: {}", e)))?,
        })
    }



    /// 将数据库行转换为风险违规记录
    fn row_to_risk_violation(&self, row: sqlx::postgres::PgRow) -> SigmaXResult<RiskViolationRecord> {
        Ok(RiskViolationRecord {
            id: row.try_get("id")
                .map_err(|e| SigmaXError::Database(format!("获取id失败: {}", e)))?,
            risk_check_id: row.try_get("risk_check_id")
                .map_err(|e| SigmaXError::Database(format!("获取risk_check_id失败: {}", e)))?,
            rule_id: row.try_get("rule_id")
                .map_err(|e| SigmaXError::Database(format!("获取rule_id失败: {}", e)))?,
            rule_name: row.try_get("rule_name")
                .map_err(|e| SigmaXError::Database(format!("获取rule_name失败: {}", e)))?,
            rule_version: row.try_get("rule_version")
                .map_err(|e| SigmaXError::Database(format!("获取rule_version失败: {}", e)))?,
            severity: row.try_get("severity")
                .map_err(|e| SigmaXError::Database(format!("获取severity失败: {}", e)))?,
            status: row.try_get("status")
                .map_err(|e| SigmaXError::Database(format!("获取status失败: {}", e)))?,
            message: row.try_get("message")
                .map_err(|e| SigmaXError::Database(format!("获取message失败: {}", e)))?,
            detailed_message: row.try_get("detailed_message")
                .map_err(|e| SigmaXError::Database(format!("获取detailed_message失败: {}", e)))?,
            current_value: row.try_get("current_value")
                .map_err(|e| SigmaXError::Database(format!("获取current_value失败: {}", e)))?,
            limit_value: row.try_get("limit_value")
                .map_err(|e| SigmaXError::Database(format!("获取limit_value失败: {}", e)))?,
            threshold_exceeded_by: row.try_get("threshold_exceeded_by")
                .map_err(|e| SigmaXError::Database(format!("获取threshold_exceeded_by失败: {}", e)))?,
            threshold_exceeded_percent: row.try_get("threshold_exceeded_percent")
                .map_err(|e| SigmaXError::Database(format!("获取threshold_exceeded_percent失败: {}", e)))?,
            trading_pair: row.try_get("trading_pair")
                .map_err(|e| SigmaXError::Database(format!("获取trading_pair失败: {}", e)))?,
            strategy_id: row.try_get("strategy_id")
                .map_err(|e| SigmaXError::Database(format!("获取strategy_id失败: {}", e)))?,
            portfolio_id: row.try_get("portfolio_id")
                .map_err(|e| SigmaXError::Database(format!("获取portfolio_id失败: {}", e)))?,
            order_context: row.try_get("order_context")
                .map_err(|e| SigmaXError::Database(format!("获取order_context失败: {}", e)))?,
            potential_loss: row.try_get("potential_loss")
                .map_err(|e| SigmaXError::Database(format!("获取potential_loss失败: {}", e)))?,
            risk_contribution: row.try_get("risk_contribution")
                .map_err(|e| SigmaXError::Database(format!("获取risk_contribution失败: {}", e)))?,
            impact_score: row.try_get("impact_score")
                .map_err(|e| SigmaXError::Database(format!("获取impact_score失败: {}", e)))?,
            auto_resolved: row.try_get("auto_resolved")
                .map_err(|e| SigmaXError::Database(format!("获取auto_resolved失败: {}", e)))?,
            resolution_action: row.try_get("resolution_action")
                .map_err(|e| SigmaXError::Database(format!("获取resolution_action失败: {}", e)))?,
            resolution_notes: row.try_get("resolution_notes")
                .map_err(|e| SigmaXError::Database(format!("获取resolution_notes失败: {}", e)))?,
            resolved_at: row.try_get("resolved_at")
                .map_err(|e| SigmaXError::Database(format!("获取resolved_at失败: {}", e)))?,
            resolved_by: row.try_get("resolved_by")
                .map_err(|e| SigmaXError::Database(format!("获取resolved_by失败: {}", e)))?,
            notification_sent: row.try_get("notification_sent")
                .map_err(|e| SigmaXError::Database(format!("获取notification_sent失败: {}", e)))?,
            notification_sent_at: row.try_get("notification_sent_at")
                .map_err(|e| SigmaXError::Database(format!("获取notification_sent_at失败: {}", e)))?,
            escalation_level: row.try_get("escalation_level")
                .map_err(|e| SigmaXError::Database(format!("获取escalation_level失败: {}", e)))?,
            escalated_at: row.try_get("escalated_at")
                .map_err(|e| SigmaXError::Database(format!("获取escalated_at失败: {}", e)))?,
            created_at: row.try_get("created_at")
                .map_err(|e| SigmaXError::Database(format!("获取created_at失败: {}", e)))?,
            updated_at: row.try_get("updated_at")
                .map_err(|e| SigmaXError::Database(format!("获取updated_at失败: {}", e)))?,
            created_by: row.try_get("created_by")
                .map_err(|e| SigmaXError::Database(format!("获取created_by失败: {}", e)))?,
        })
    }

    /// 构建风险检查查询的 WHERE 子句
    fn build_risk_check_where_clause(&self, filter: &RiskQueryFilter) -> (String, Vec<String>) {
        let mut conditions = Vec::new();
        let mut params = Vec::new();
        let mut param_index = 1;

        if let Some(trading_pair) = &filter.trading_pair {
            conditions.push(format!("trading_pair = ${}", param_index));
            params.push(trading_pair.clone());
            param_index += 1;
        }

        if let Some(strategy_id) = &filter.strategy_id {
            conditions.push(format!("strategy_id = ${}", param_index));
            params.push(strategy_id.to_string());
            param_index += 1;
        }

        if let Some(engine_id) = &filter.engine_id {
            conditions.push(format!("engine_id = ${}", param_index));
            params.push(engine_id.to_string());
            param_index += 1;
        }

        if let Some(passed) = filter.passed {
            // 将 boolean 转换为对应的 result 枚举值
            let result_value = if passed { "passed" } else { "failed" };
            conditions.push(format!("result = ${}", param_index));
            params.push(result_value.to_string());
            param_index += 1;
        }

        if let Some(start_time) = &filter.start_time {
            conditions.push(format!("timestamp >= ${}", param_index));
            params.push(start_time.to_rfc3339());
            param_index += 1;
        }

        if let Some(end_time) = &filter.end_time {
            conditions.push(format!("timestamp <= ${}", param_index));
            params.push(end_time.to_rfc3339());
            param_index += 1;
        }

        if let Some(min_score) = &filter.min_risk_score {
            conditions.push(format!("risk_score >= ${}", param_index));
            params.push(min_score.to_string());
            param_index += 1;
        }

        if let Some(max_score) = &filter.max_risk_score {
            conditions.push(format!("risk_score <= ${}", param_index));
            params.push(max_score.to_string());
            param_index += 1;
        }

        let where_clause = if conditions.is_empty() {
            String::new()
        } else {
            format!("WHERE {}", conditions.join(" AND "))
        };

        (where_clause, params)
    }
}

#[async_trait]
impl RiskRepository for SqlRiskRepository {
    // ============================================================================
    // 风险检查记录管理
    // ============================================================================

    async fn save_risk_check(&self, record: &RiskCheckRecord) -> SigmaXResult<()> {
        let pool = self.db.pool();

        sqlx::query(
            r#"
            INSERT INTO risk_checks (
                id, timestamp, trading_pair, side, quantity, price, order_type,
                result, risk_score, confidence_level, violations, warnings, recommendations,
                applied_rules, max_allowed_quantity, suggested_price_min, suggested_price_max,
                alternative_suggestions, processing_time_ms, rules_evaluated, cache_hit_rate,
                strategy_id, portfolio_id, engine_id, session_id, created_at, check_date
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, $25, $26, $27
            )
            ON CONFLICT (id, check_date) DO UPDATE SET
                timestamp = EXCLUDED.timestamp,
                trading_pair = EXCLUDED.trading_pair,
                side = EXCLUDED.side,
                quantity = EXCLUDED.quantity,
                price = EXCLUDED.price,
                order_type = EXCLUDED.order_type,
                result = EXCLUDED.result,
                risk_score = EXCLUDED.risk_score,
                confidence_level = EXCLUDED.confidence_level,
                violations = EXCLUDED.violations,
                warnings = EXCLUDED.warnings,
                recommendations = EXCLUDED.recommendations,
                applied_rules = EXCLUDED.applied_rules,
                max_allowed_quantity = EXCLUDED.max_allowed_quantity,
                suggested_price_min = EXCLUDED.suggested_price_min,
                suggested_price_max = EXCLUDED.suggested_price_max,
                alternative_suggestions = EXCLUDED.alternative_suggestions,
                processing_time_ms = EXCLUDED.processing_time_ms,
                rules_evaluated = EXCLUDED.rules_evaluated,
                cache_hit_rate = EXCLUDED.cache_hit_rate,
                strategy_id = EXCLUDED.strategy_id,
                portfolio_id = EXCLUDED.portfolio_id,
                engine_id = EXCLUDED.engine_id,
                session_id = EXCLUDED.session_id
            "#
        )
        .bind(&record.id)
        .bind(&record.timestamp)
        .bind(&record.trading_pair)
        .bind(&record.side)
        .bind(&record.quantity)
        .bind(&record.price)
        .bind(&record.order_type)
        .bind(&record.result)
        .bind(&record.risk_score)
        .bind(&record.confidence_level)
        .bind(&record.violations)
        .bind(&record.warnings)
        .bind(&record.recommendations)
        .bind(&record.applied_rules)
        .bind(&record.max_allowed_quantity)
        .bind(&record.suggested_price_min)
        .bind(&record.suggested_price_max)
        .bind(&record.alternative_suggestions)
        .bind(&record.processing_time_ms)
        .bind(&record.rules_evaluated)
        .bind(&record.cache_hit_rate)
        .bind(&record.strategy_id)
        .bind(&record.portfolio_id)
        .bind(&record.engine_id)
        .bind(&record.session_id)
        .bind(&record.created_at)
        .bind(&record.check_date)
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("保存风险检查记录失败: {}", e)))?;

        Ok(())
    }

    async fn get_risk_check(&self, id: Uuid) -> SigmaXResult<Option<RiskCheckRecord>> {
        let pool = self.db.pool();

        let row = sqlx::query(
            r#"
            SELECT id, timestamp, trading_pair, side, quantity, price, order_type,
                   result, risk_score, confidence_level, violations, warnings, recommendations,
                   applied_rules, max_allowed_quantity, suggested_price_min, suggested_price_max,
                   alternative_suggestions, processing_time_ms, rules_evaluated, cache_hit_rate,
                   strategy_id, portfolio_id, engine_id, session_id, created_at, check_date
            FROM risk_checks
            WHERE id = $1
            "#
        )
        .bind(&id)
        .fetch_optional(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("获取风险检查记录失败: {}", e)))?;

        match row {
            Some(row) => Ok(Some(self.row_to_risk_check(row)?)),
            None => Ok(None),
        }
    }

    async fn find_risk_checks(
        &self,
        filter: &RiskQueryFilter,
        pagination: Option<&Pagination>
    ) -> SigmaXResult<Vec<RiskCheckRecord>> {
        let pool = self.db.pool();
        let (where_clause, _params) = self.build_risk_check_where_clause(filter);

        let mut query = format!(
            r#"
            SELECT id, timestamp, trading_pair, side, quantity, price, order_type,
                   result, risk_score, confidence_level, violations, warnings, recommendations,
                   applied_rules, max_allowed_quantity, suggested_price_min, suggested_price_max,
                   alternative_suggestions, processing_time_ms, rules_evaluated, cache_hit_rate,
                   strategy_id, portfolio_id, engine_id, session_id, created_at, check_date
            FROM risk_checks
            {}
            ORDER BY timestamp DESC
            "#,
            where_clause
        );

        if let Some(page) = pagination {
            query.push_str(&format!(" LIMIT {} OFFSET {}", page.limit, page.offset));
        }

        let mut sql_query = sqlx::query(&query);

        // 绑定参数
        if let Some(trading_pair) = &filter.trading_pair {
            sql_query = sql_query.bind(trading_pair);
        }
        if let Some(strategy_id) = &filter.strategy_id {
            sql_query = sql_query.bind(strategy_id);
        }
        if let Some(engine_id) = &filter.engine_id {
            sql_query = sql_query.bind(engine_id);
        }
        if let Some(passed) = filter.passed {
            // 将 boolean 转换为对应的 result 枚举值
            let result_value = if passed { "passed" } else { "failed" };
            sql_query = sql_query.bind(result_value);
        }
        if let Some(start_time) = &filter.start_time {
            sql_query = sql_query.bind(start_time);
        }
        if let Some(end_time) = &filter.end_time {
            sql_query = sql_query.bind(end_time);
        }
        if let Some(min_score) = &filter.min_risk_score {
            sql_query = sql_query.bind(min_score);
        }
        if let Some(max_score) = &filter.max_risk_score {
            sql_query = sql_query.bind(max_score);
        }

        let rows = sql_query
            .fetch_all(pool)
            .await
            .map_err(|e| SigmaXError::Database(format!("查询风险检查记录失败: {}", e)))?;

        let mut records = Vec::new();
        for row in rows {
            records.push(self.row_to_risk_check(row)?);
        }

        Ok(records)
    }

    async fn count_risk_checks(&self, filter: &RiskQueryFilter) -> SigmaXResult<u64> {
        let pool = self.db.pool();
        let (where_clause, _params) = self.build_risk_check_where_clause(filter);

        let query = format!(
            "SELECT COUNT(*) as count FROM risk_checks {}",
            where_clause
        );

        let mut sql_query = sqlx::query(&query);

        // 绑定参数
        if let Some(trading_pair) = &filter.trading_pair {
            sql_query = sql_query.bind(trading_pair);
        }
        if let Some(strategy_id) = &filter.strategy_id {
            sql_query = sql_query.bind(strategy_id);
        }
        if let Some(engine_id) = &filter.engine_id {
            sql_query = sql_query.bind(engine_id);
        }
        if let Some(passed) = filter.passed {
            // 将 boolean 转换为对应的 result 枚举值
            let result_value = if passed { "passed" } else { "failed" };
            sql_query = sql_query.bind(result_value);
        }
        if let Some(start_time) = &filter.start_time {
            sql_query = sql_query.bind(start_time);
        }
        if let Some(end_time) = &filter.end_time {
            sql_query = sql_query.bind(end_time);
        }
        if let Some(min_score) = &filter.min_risk_score {
            sql_query = sql_query.bind(min_score);
        }
        if let Some(max_score) = &filter.max_risk_score {
            sql_query = sql_query.bind(max_score);
        }

        let row = sql_query
            .fetch_one(pool)
            .await
            .map_err(|e| SigmaXError::Database(format!("统计风险检查记录失败: {}", e)))?;

        let count: i64 = row.try_get("count")
            .map_err(|e| SigmaXError::Database(format!("获取统计结果失败: {}", e)))?;

        Ok(count as u64)
    }

    // ============================================================================
    // 风险规则管理
    // ============================================================================

    async fn save_risk_rule(&self, rule: &RiskRuleRecord) -> SigmaXResult<()> {
        let pool = self.db.pool();

        sqlx::query(
            r#"
            INSERT INTO risk_rules (
                id, name, description, category, rule_type, parameters, conditions,
                enabled, priority, strategy_type, trading_pairs, execution_count,
                success_count, failure_count, last_executed_at, average_execution_time_ms,
                version, is_active, created_at, updated_at, created_by, updated_by
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22
            )
            ON CONFLICT (id) DO UPDATE SET
                name = EXCLUDED.name,
                description = EXCLUDED.description,
                category = EXCLUDED.category,
                rule_type = EXCLUDED.rule_type,
                parameters = EXCLUDED.parameters,
                conditions = EXCLUDED.conditions,
                enabled = EXCLUDED.enabled,
                priority = EXCLUDED.priority,
                strategy_type = EXCLUDED.strategy_type,
                trading_pairs = EXCLUDED.trading_pairs,
                execution_count = EXCLUDED.execution_count,
                success_count = EXCLUDED.success_count,
                failure_count = EXCLUDED.failure_count,
                last_executed_at = EXCLUDED.last_executed_at,
                average_execution_time_ms = EXCLUDED.average_execution_time_ms,
                version = EXCLUDED.version,
                is_active = EXCLUDED.is_active,
                updated_at = EXCLUDED.updated_at,
                updated_by = EXCLUDED.updated_by
            "#
        )
        .bind(&rule.id)
        .bind(&rule.name)
        .bind(&rule.description)
        .bind(&rule.category)
        .bind(&rule.rule_type)
        .bind(&rule.parameters)
        .bind(&rule.conditions)
        .bind(&rule.enabled)
        .bind(&rule.priority)
        .bind(&rule.strategy_type)
        .bind(&rule.trading_pairs)
        .bind(&rule.execution_count)
        .bind(&rule.success_count)
        .bind(&rule.failure_count)
        .bind(&rule.last_executed_at)
        .bind(&rule.average_execution_time_ms)
        .bind(&rule.version)
        .bind(&rule.is_active)
        .bind(&rule.created_at)
        .bind(&rule.updated_at)
        .bind(&rule.created_by)
        .bind(&rule.updated_by)
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("保存风险规则失败: {}", e)))?;

        Ok(())
    }

    async fn get_risk_rule(&self, id: Uuid) -> SigmaXResult<Option<RiskRuleRecord>> {
        let pool = self.db.pool();

        let row = sqlx::query(
            r#"
            SELECT id, name, description, category, rule_type, parameters, conditions,
                   enabled, priority, strategy_type, trading_pairs, execution_count,
                   success_count, failure_count, last_executed_at, average_execution_time_ms,
                   version, is_active, created_at, updated_at, created_by, updated_by
            FROM risk_rules
            WHERE id = $1
            "#
        )
        .bind(&id)
        .fetch_optional(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("获取风险规则失败: {}", e)))?;

        match row {
            Some(row) => Ok(Some(self.row_to_risk_rule(row)?)),
            None => Ok(None),
        }
    }

    async fn get_enabled_risk_rules(&self) -> SigmaXResult<Vec<RiskRuleRecord>> {
        let pool = self.db.pool();

        let rows = sqlx::query(
            r#"
            SELECT id, name, description, category, rule_type, parameters, conditions,
                   enabled, priority, strategy_type, trading_pairs, execution_count,
                   success_count, failure_count, last_executed_at, average_execution_time_ms,
                   version, is_active, created_at, updated_at, created_by, updated_by
            FROM risk_rules
            WHERE enabled = true AND is_active = true
            ORDER BY priority DESC, created_at ASC
            "#
        )
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("获取启用的风险规则失败: {}", e)))?;

        let mut rules = Vec::new();
        for row in rows {
            rules.push(self.row_to_risk_rule(row)?);
        }

        Ok(rules)
    }

    async fn get_risk_rules_by_type(&self, rule_type: &str) -> SigmaXResult<Vec<RiskRuleRecord>> {
        let pool = self.db.pool();

        let rows = sqlx::query(
            r#"
            SELECT id, name, description, category, rule_type, parameters, conditions,
                   enabled, priority, strategy_type, trading_pairs, execution_count,
                   success_count, failure_count, last_executed_at, average_execution_time_ms,
                   version, is_active, created_at, updated_at, created_by, updated_by
            FROM risk_rules
            WHERE rule_type = $1 AND enabled = true AND is_active = true
            ORDER BY priority DESC, created_at ASC
            "#
        )
        .bind(rule_type)
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("按类型获取风险规则失败: {}", e)))?;

        let mut rules = Vec::new();
        for row in rows {
            rules.push(self.row_to_risk_rule(row)?);
        }

        Ok(rules)
    }

    async fn find_risk_rules(
        &self,
        filter: &RiskRuleFilter,
        pagination: Option<&Pagination>
    ) -> SigmaXResult<Vec<RiskRuleRecord>> {
        let pool = self.db.pool();

        let mut conditions = Vec::new();
        let mut param_index = 1;

        if let Some(rule_type) = &filter.rule_type {
            conditions.push(format!("rule_type = ${}", param_index));
            param_index += 1;
        }

        if let Some(enabled) = filter.enabled {
            conditions.push(format!("enabled = ${}", param_index));
            param_index += 1;
        }

        if let Some(created_by) = &filter.created_by {
            conditions.push(format!("created_by = ${}", param_index));
            param_index += 1;
        }

        let where_clause = if conditions.is_empty() {
            String::new()
        } else {
            format!("WHERE {}", conditions.join(" AND "))
        };

        let mut query = format!(
            r#"
            SELECT id, name, description, category, rule_type, parameters, conditions,
                   enabled, priority, strategy_type, trading_pairs, execution_count,
                   success_count, failure_count, last_executed_at, average_execution_time_ms,
                   version, is_active, created_at, updated_at, created_by, updated_by
            FROM risk_rules
            {}
            ORDER BY priority DESC, created_at ASC
            "#,
            where_clause
        );

        if let Some(page) = pagination {
            query.push_str(&format!(" LIMIT {} OFFSET {}", page.limit, page.offset));
        }

        let mut sql_query = sqlx::query(&query);

        // 绑定参数
        if let Some(rule_type) = &filter.rule_type {
            sql_query = sql_query.bind(rule_type);
        }
        if let Some(enabled) = filter.enabled {
            sql_query = sql_query.bind(enabled);
        }
        if let Some(created_by) = &filter.created_by {
            sql_query = sql_query.bind(created_by);
        }

        let rows = sql_query
            .fetch_all(pool)
            .await
            .map_err(|e| SigmaXError::Database(format!("查询风险规则失败: {}", e)))?;

        let mut rules = Vec::new();
        for row in rows {
            rules.push(self.row_to_risk_rule(row)?);
        }

        Ok(rules)
    }

    async fn update_risk_rule_status(&self, id: Uuid, enabled: bool) -> SigmaXResult<()> {
        let pool = self.db.pool();

        sqlx::query(
            r#"
            UPDATE risk_rules
            SET enabled = $1, updated_at = NOW()
            WHERE id = $2
            "#
        )
        .bind(enabled)
        .bind(&id)
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("更新风险规则状态失败: {}", e)))?;

        Ok(())
    }

    async fn delete_risk_rule(&self, id: Uuid) -> SigmaXResult<()> {
        let pool = self.db.pool();

        sqlx::query("DELETE FROM risk_rules WHERE id = $1")
            .bind(&id)
            .execute(pool)
            .await
            .map_err(|e| SigmaXError::Database(format!("删除风险规则失败: {}", e)))?;

        Ok(())
    }



    // ============================================================================
    // 风险违规记录管理
    // ============================================================================

    async fn save_risk_violation(&self, violation: &RiskViolationRecord) -> SigmaXResult<()> {
        let pool = self.db.pool();

        sqlx::query(
            r#"
            INSERT INTO risk_violations (
                id, risk_check_id, rule_id, rule_name, rule_version, severity, status,
                message, detailed_message, current_value, limit_value, threshold_exceeded_by,
                threshold_exceeded_percent, trading_pair, strategy_id, portfolio_id, order_context,
                potential_loss, risk_contribution, impact_score, auto_resolved, resolution_action,
                resolution_notes, resolved_at, resolved_by, notification_sent, notification_sent_at,
                escalation_level, escalated_at, created_at, updated_at, created_by
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, $25, $26, $27, $28, $29, $30, $31, $32
            )
            ON CONFLICT (id) DO UPDATE SET
                risk_check_id = EXCLUDED.risk_check_id,
                rule_id = EXCLUDED.rule_id,
                rule_name = EXCLUDED.rule_name,
                rule_version = EXCLUDED.rule_version,
                severity = EXCLUDED.severity,
                status = EXCLUDED.status,
                message = EXCLUDED.message,
                detailed_message = EXCLUDED.detailed_message,
                current_value = EXCLUDED.current_value,
                limit_value = EXCLUDED.limit_value,
                threshold_exceeded_by = EXCLUDED.threshold_exceeded_by,
                threshold_exceeded_percent = EXCLUDED.threshold_exceeded_percent,
                trading_pair = EXCLUDED.trading_pair,
                strategy_id = EXCLUDED.strategy_id,
                portfolio_id = EXCLUDED.portfolio_id,
                order_context = EXCLUDED.order_context,
                potential_loss = EXCLUDED.potential_loss,
                risk_contribution = EXCLUDED.risk_contribution,
                impact_score = EXCLUDED.impact_score,
                auto_resolved = EXCLUDED.auto_resolved,
                resolution_action = EXCLUDED.resolution_action,
                resolution_notes = EXCLUDED.resolution_notes,
                resolved_at = EXCLUDED.resolved_at,
                resolved_by = EXCLUDED.resolved_by,
                notification_sent = EXCLUDED.notification_sent,
                notification_sent_at = EXCLUDED.notification_sent_at,
                escalation_level = EXCLUDED.escalation_level,
                escalated_at = EXCLUDED.escalated_at,
                updated_at = EXCLUDED.updated_at,
                created_by = EXCLUDED.created_by
            "#
        )
        .bind(&violation.id)
        .bind(&violation.risk_check_id)
        .bind(&violation.rule_id)
        .bind(&violation.rule_name)
        .bind(&violation.rule_version)
        .bind(&violation.severity)
        .bind(&violation.status)
        .bind(&violation.message)
        .bind(&violation.detailed_message)
        .bind(&violation.current_value)
        .bind(&violation.limit_value)
        .bind(&violation.threshold_exceeded_by)
        .bind(&violation.threshold_exceeded_percent)
        .bind(&violation.trading_pair)
        .bind(&violation.strategy_id)
        .bind(&violation.portfolio_id)
        .bind(&violation.order_context)
        .bind(&violation.potential_loss)
        .bind(&violation.risk_contribution)
        .bind(&violation.impact_score)
        .bind(&violation.auto_resolved)
        .bind(&violation.resolution_action)
        .bind(&violation.resolution_notes)
        .bind(&violation.resolved_at)
        .bind(&violation.resolved_by)
        .bind(&violation.notification_sent)
        .bind(&violation.notification_sent_at)
        .bind(&violation.escalation_level)
        .bind(&violation.escalated_at)
        .bind(&violation.created_at)
        .bind(&violation.updated_at)
        .bind(&violation.created_by)
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("保存风险违规记录失败: {}", e)))?;

        Ok(())
    }

    async fn get_violations_by_check_id(&self, check_id: Uuid) -> SigmaXResult<Vec<RiskViolationRecord>> {
        let pool = self.db.pool();

        let rows = sqlx::query(
            r#"
            SELECT id, risk_check_id, rule_id, rule_name, rule_version, severity, status,
                   message, detailed_message, current_value, limit_value, threshold_exceeded_by,
                   threshold_exceeded_percent, trading_pair, strategy_id, portfolio_id, order_context,
                   potential_loss, risk_contribution, impact_score, auto_resolved, resolution_action,
                   resolution_notes, resolved_at, resolved_by, notification_sent, notification_sent_at,
                   escalation_level, escalated_at, created_at, updated_at, created_by
            FROM risk_violations
            WHERE risk_check_id = $1
            ORDER BY created_at DESC
            "#
        )
        .bind(&check_id)
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("根据检查ID获取违规记录失败: {}", e)))?;

        let mut violations = Vec::new();
        for row in rows {
            violations.push(self.row_to_risk_violation(row)?);
        }

        Ok(violations)
    }

    async fn get_violations_by_rule_id(&self, rule_id: Uuid) -> SigmaXResult<Vec<RiskViolationRecord>> {
        let pool = self.db.pool();

        let rows = sqlx::query(
            r#"
            SELECT id, risk_check_id, rule_id, rule_name, rule_version, severity, status,
                   message, detailed_message, current_value, limit_value, threshold_exceeded_by,
                   threshold_exceeded_percent, trading_pair, strategy_id, portfolio_id, order_context,
                   potential_loss, risk_contribution, impact_score, auto_resolved, resolution_action,
                   resolution_notes, resolved_at, resolved_by, notification_sent, notification_sent_at,
                   escalation_level, escalated_at, created_at, updated_at, created_by
            FROM risk_violations
            WHERE rule_id = $1
            ORDER BY created_at DESC
            "#
        )
        .bind(&rule_id)
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("根据规则ID获取违规记录失败: {}", e)))?;

        let mut violations = Vec::new();
        for row in rows {
            violations.push(self.row_to_risk_violation(row)?);
        }

        Ok(violations)
    }

    async fn get_recent_violations(&self, limit: usize) -> SigmaXResult<Vec<RiskViolationRecord>> {
        let pool = self.db.pool();

        let rows = sqlx::query(
            r#"
            SELECT id, risk_check_id, rule_id, rule_name, rule_version, severity, status,
                   message, detailed_message, current_value, limit_value, threshold_exceeded_by,
                   threshold_exceeded_percent, trading_pair, strategy_id, portfolio_id, order_context,
                   potential_loss, risk_contribution, impact_score, auto_resolved, resolution_action,
                   resolution_notes, resolved_at, resolved_by, notification_sent, notification_sent_at,
                   escalation_level, escalated_at, created_at, updated_at, created_by
            FROM risk_violations
            ORDER BY created_at DESC
            LIMIT $1
            "#
        )
        .bind(limit as i64)
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("获取最近违规记录失败: {}", e)))?;

        let mut violations = Vec::new();
        for row in rows {
            violations.push(self.row_to_risk_violation(row)?);
        }

        Ok(violations)
    }






}


