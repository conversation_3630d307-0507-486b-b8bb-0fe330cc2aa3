//! 策略配置验证工具
//!
//! 提供通用的策略配置验证功能
//!
//! 🔥 注意：此模块将逐步迁移到统一的validator框架

use serde::{Serialize, Deserialize};
use sigmax_core::validation::UnifiedValidate;

/// 验证结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationResult {
    /// 是否通过验证
    pub is_valid: bool,
    /// 验证错误列表
    pub errors: Vec<StrategyValidationError>,
    /// 验证警告列表
    pub warnings: Vec<ValidationWarning>,
    /// 建议列表
    pub suggestions: Vec<String>,
}

/// 策略验证错误
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StrategyValidationError {
    /// 错误字段
    pub field: String,
    /// 错误消息
    pub message: String,
    /// 错误类型
    pub error_type: StrategyValidationErrorType,
}

/// 验证警告
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ValidationWarning {
    /// 警告字段
    pub field: String,
    /// 警告消息
    pub message: String,
    /// 建议
    pub suggestion: Option<String>,
}

/// 策略验证错误类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum StrategyValidationErrorType {
    /// 必填字段缺失
    Required,
    /// 数值范围错误
    Range,
    /// 格式错误
    Format,
    /// 逻辑错误
    Logic,
    /// 配置冲突
    Conflict,
}

impl ValidationResult {
    /// 创建成功的验证结果
    pub fn success() -> Self {
        Self {
            is_valid: true,
            errors: Vec::new(),
            warnings: Vec::new(),
            suggestions: Vec::new(),
        }
    }

    /// 创建失败的验证结果
    pub fn failure(errors: Vec<StrategyValidationError>) -> Self {
        Self {
            is_valid: false,
            errors,
            warnings: Vec::new(),
            suggestions: Vec::new(),
        }
    }

    /// 添加错误
    pub fn add_error(&mut self, error: StrategyValidationError) {
        self.errors.push(error);
        self.is_valid = false;
    }

    /// 添加警告
    pub fn add_warning(&mut self, warning: ValidationWarning) {
        self.warnings.push(warning);
    }

    /// 添加建议
    pub fn add_suggestion(&mut self, suggestion: String) {
        self.suggestions.push(suggestion);
    }
}

impl StrategyValidationError {
    /// 创建必填字段错误
    pub fn required(field: &str, message: &str) -> Self {
        Self {
            field: field.to_string(),
            message: message.to_string(),
            error_type: StrategyValidationErrorType::Required,
        }
    }

    /// 创建范围错误
    pub fn range(field: &str, message: &str) -> Self {
        Self {
            field: field.to_string(),
            message: message.to_string(),
            error_type: StrategyValidationErrorType::Range,
        }
    }

    /// 创建格式错误
    pub fn format(field: &str, message: &str) -> Self {
        Self {
            field: field.to_string(),
            message: message.to_string(),
            error_type: StrategyValidationErrorType::Format,
        }
    }

    /// 创建逻辑错误
    pub fn logic(field: &str, message: &str) -> Self {
        Self {
            field: field.to_string(),
            message: message.to_string(),
            error_type: StrategyValidationErrorType::Logic,
        }
    }

    /// 创建冲突错误
    pub fn conflict(field: &str, message: &str) -> Self {
        Self {
            field: field.to_string(),
            message: message.to_string(),
            error_type: StrategyValidationErrorType::Conflict,
        }
    }
}

impl ValidationWarning {
    /// 创建新的警告
    pub fn new(field: &str, message: &str, suggestion: Option<String>) -> Self {
        Self {
            field: field.to_string(),
            message: message.to_string(),
            suggestion,
        }
    }
}

/// 策略验证器
pub struct StrategyValidator;

impl StrategyValidator {
    /// 验证数值范围
    pub fn validate_range(
        field: &str,
        value: f64,
        min: f64,
        max: f64,
        result: &mut ValidationResult,
    ) {
        if value < min || value > max {
            result.add_error(StrategyValidationError::range(
                field,
                &format!("值 {} 超出范围 [{}, {}]", value, min, max),
            ));
        }
    }

    /// 验证正数
    pub fn validate_positive(
        field: &str,
        value: f64,
        result: &mut ValidationResult,
    ) {
        if value <= 0.0 {
            result.add_error(StrategyValidationError::range(
                field,
                &format!("值 {} 必须大于0", value),
            ));
        }
    }

    /// 验证非负数
    pub fn validate_non_negative(
        field: &str,
        value: f64,
        result: &mut ValidationResult,
    ) {
        if value < 0.0 {
            result.add_error(StrategyValidationError::range(
                field,
                &format!("值 {} 不能为负数", value),
            ));
        }
    }

    /// 验证百分比
    pub fn validate_percentage(
        field: &str,
        value: f64,
        result: &mut ValidationResult,
    ) {
        if value < 0.0 || value > 1.0 {
            result.add_error(StrategyValidationError::range(
                field,
                &format!("百分比值 {} 必须在0-1之间", value),
            ));
        }
    }

    /// 验证必填字段
    pub fn validate_required<T>(
        field: &str,
        value: &Option<T>,
        result: &mut ValidationResult,
    ) {
        if value.is_none() {
            result.add_error(StrategyValidationError::required(
                field,
                &format!("字段 {} 是必填的", field),
            ));
        }
    }

    /// 验证字符串非空
    pub fn validate_non_empty_string(
        field: &str,
        value: &str,
        result: &mut ValidationResult,
    ) {
        if value.trim().is_empty() {
            result.add_error(StrategyValidationError::required(
                field,
                &format!("字段 {} 不能为空", field),
            ));
        }
    }

    /// 验证逻辑关系
    pub fn validate_logic_relation(
        field1: &str,
        value1: f64,
        field2: &str,
        value2: f64,
        should_be_greater: bool,
        result: &mut ValidationResult,
    ) {
        let is_valid = if should_be_greater {
            value1 > value2
        } else {
            value1 < value2
        };

        if !is_valid {
            let relation = if should_be_greater { "大于" } else { "小于" };
            result.add_error(StrategyValidationError::logic(
                field1,
                &format!("{} ({}) 必须{} {} ({})", field1, value1, relation, field2, value2),
            ));
        }
    }
}
