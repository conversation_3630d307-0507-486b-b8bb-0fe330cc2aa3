# SigmaX Trading System Database

SigmaX交易系统的PostgreSQL数据库结构和管理脚本。

## 🚨 重要提示：SQL 文件导入问题已修复

之前的 SQL 文件存在枚举类型缺失的问题，现在已经提供了完整的解决方案。

## 📋 目录结构

```
database/
├── Cargo.toml
├── README.md
├── sql/                         # 数据库脚本（已修复）
│   ├── 00_create_enums.sql      # 🆕 枚举类型定义（必须首先运行）
│   ├── init_database.sql        # 🆕 完整数据库初始化脚本
│   ├── fix_existing_database.sql # 🆕 修复现有数据库的脚本
│   ├── public.sql               # 完整的表结构（包含所有表）
│   ├── strategies.sql           # 策略相关表
│   ├── orders.sql               # 订单相关表
│   ├── exchanges.sql            # 交易所表
│   ├── trading_pairs.sql        # 交易对表
│   └── ...                      # 其他表结构文件
├── src/                         # Rust 数据库模块源代码
│   ├── schema.rs                # Diesel ORM 数据库 Schema 定义
│   └── ...
└── external-db/                 # 外部数据库部署相关脚本和文档
    ├── init_database.sql
    ├── setup.sh
    └── ...
```

## 🔧 数据库问题修复方案

### 问题描述
原始的 SQL 文件引用了多个 PostgreSQL 枚举类型（如 `strategy_status`、`order_type`、`order_side` 等），但这些枚举类型的定义缺失，导致无法正常导入到新数据库中。

### 解决方案

我们提供了三种解决方案：

#### 方案一：全新数据库初始化（推荐）
如果你要在全新的数据库中初始化 SigmaX 系统：

```bash
# 使用 Neon 数据库
psql "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require" -f database/sql/init_database.sql

# 或使用本地数据库
psql -U username -d database_name -f database/sql/init_database.sql
```

#### 方案二：修复现有数据库
如果你的数据库已经有表但缺少枚举类型：

```bash
# 先运行修复脚本
psql "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require" -f database/sql/fix_existing_database.sql

# 然后可以安全地导入其他 SQL 文件
```

#### 方案三：手动创建枚举类型
如果你只需要枚举类型定义：

```bash
psql "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require" -f database/sql/00_create_enums.sql
```

## 📊 枚举类型说明

修复后的数据库包含以下枚举类型：

- **strategy_status**: `Created`, `Running`, `Paused`, `Stopped`, `Error`, `Completed`
- **exchange_status**: `Active`, `Inactive`, `Maintenance`, `Suspended`
- **order_type**: `Market`, `Limit`, `StopLoss`, `StopLimit`, `TakeProfit`, `TakeProfitLimit`
- **order_side**: `Buy`, `Sell`
- **order_status**: `Pending`, `Submitted`, `PartiallyFilled`, `Filled`, `Cancelled`, `Rejected`, `Expired`

## 🚀 快速开始

### 1. 初始化新数据库

```bash
# 克隆项目后，进入 database 目录
cd database

# 运行完整初始化脚本
psql "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require" -f sql/init_database.sql
```

### 2. 验证数据库状态

```sql
-- 检查枚举类型
SELECT typname FROM pg_type WHERE typname IN ('strategy_status', 'exchange_status', 'order_type', 'order_side', 'order_status');

-- 检查表结构
SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' ORDER BY table_name;

-- 检查初始数据
SELECT name, display_name FROM exchanges;
SELECT symbol, base_asset, quote_asset FROM trading_pairs;
```

## 🚀 快速开始

### 1. 环境要求

- PostgreSQL 12+
- psql 客户端工具
- bash shell (Linux/macOS)
- Docker (推荐用于快速部署)

### 2. 当前数据库连接信息

**Docker PostgreSQL 实例（推荐）**
```
主机: localhost
端口: 5432
数据库: sigmax_trading
用户名: postgres
密码: postgres
连接字符串: postgresql://postgres:postgres@localhost:5432/sigmax_trading
```

**Docker 容器管理**
```bash
# 启动容器
docker start sigmax-postgres

# 停止容器
docker stop sigmax-postgres

# 查看容器状态
docker ps -a | grep sigmax-postgres

# 连接到数据库
docker exec -it sigmax-postgres psql -U postgres -d sigmax_trading

# 查看容器日志
docker logs sigmax-postgres
```

**环境变量配置**
```bash
# 复制环境变量配置文件
cp .env.example .env

# 编辑配置文件
nano .env

# 加载环境变量
source .env

# 或者直接设置关键环境变量
export DATABASE_URL="postgresql://postgres:postgres@localhost:5432/sigmax_trading"
export WEB_HOST="127.0.0.1"
export WEB_PORT="8080"
```

### 3. 独立PostgreSQL Docker部署（⭐ 推荐生产环境）

使用独立的PostgreSQL Docker容器，与应用分离部署：

```bash
# 进入独立PostgreSQL目录
cd standalone-postgres

# 方法1：快速启动向导（推荐新手）
./quick-start.sh

# 方法2：手动部署（推荐高级用户）
cp .env.example .env
nano .env  # 编辑配置
./deploy.sh deploy

# 方法3：包含管理界面
./deploy.sh deploy --with-admin

# 验证部署
./test.sh

# 查看状态
./deploy.sh status
```

**独立部署的优势：**
- ✅ 数据库与应用完全分离
- ✅ 更好的资源管理和扩展性
- ✅ 独立的备份和恢复
- ✅ 支持多个应用实例连接
- ✅ 生产级别的配置和监控
- ✅ 自动化管理脚本
- ✅ 完整的测试验证

**连接信息：**
```
主机: localhost
端口: 5432
数据库: sigmax_trading
连接字符串: postgresql://postgres:postgres@localhost:5432/sigmax_trading
```

**管理命令：**
```bash
# 部署管理
./deploy.sh deploy|start|stop|restart|status|logs|connect|clean

# 数据管理
./deploy.sh backup|restore

# 测试验证
./test.sh [connection|tables|data|health|backup|performance]
```

**详细文档：** [standalone-postgres/README.md](standalone-postgres/README.md)

### 4. Hugging Face Spaces 部署

在Hugging Face Spaces中部署SigmaX系统有多种方案：

#### 方案1：使用外部云数据库（推荐）

**使用Supabase（免费层可用）**
```bash
# 1. 在 https://supabase.com 创建项目
# 2. 获取数据库连接信息
# 3. 在Hugging Face Spaces中设置环境变量

# Hugging Face Spaces环境变量设置：
DATABASE_URL=postgresql://postgres:[password]@db.[project-ref].supabase.co:5432/postgres
WEB_HOST=0.0.0.0
WEB_PORT=7860
```

**使用Neon（免费层可用）**
```bash
# 1. 在 https://neon.tech 创建项目
# 2. 获取连接字符串
# 3. 设置环境变量

DATABASE_URL=postgresql://[user]:[password]@[endpoint]/[dbname]?sslmode=require
```

**使用Railway**
```bash
# 1. 在 https://railway.app 创建PostgreSQL服务
# 2. 获取连接信息
# 3. 设置环境变量

DATABASE_URL=postgresql://postgres:[password]@[host]:[port]/railway
```

#### 方案2：容器内数据库（开发测试用）

在同一容器中运行PostgreSQL和应用：

```dockerfile
# 参考 huggingface/Dockerfile.spaces
FROM postgres:15

# 安装Rust和应用依赖
RUN apt-get update && apt-get install -y curl build-essential
RUN curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y

# 复制应用代码和数据库脚本
COPY . /app
COPY database/*.sql /docker-entrypoint-initdb.d/

# 设置环境变量
ENV POSTGRES_DB=sigmax_trading
ENV POSTGRES_USER=postgres
ENV POSTGRES_PASSWORD=postgres
ENV DATABASE_URL=postgresql://postgres:postgres@localhost:5432/sigmax_trading

# 启动脚本
COPY huggingface/start.sh /start.sh
RUN chmod +x /start.sh

EXPOSE 7860
CMD ["/start.sh"]
```

### 4. Docker 快速部署（本地开发）

使用Docker快速创建PostgreSQL实例：

```bash
# 创建并启动PostgreSQL容器
docker run --name sigmax-postgres \
  -e POSTGRES_PASSWORD=postgres \
  -e POSTGRES_DB=sigmax_trading \
  -e POSTGRES_USER=postgres \
  -p 5432:5432 \
  -d postgres:15

# 等待容器启动
sleep 10

# 执行数据库初始化脚本
docker cp sql/01_create_database.sql sigmax-postgres:/tmp/
docker cp sql/02_create_tables.sql sigmax-postgres:/tmp/
docker cp sql/03_create_indexes.sql sigmax-postgres:/tmp/
docker cp sql/04_create_triggers.sql sigmax-postgres:/tmp/
docker cp sql/05_insert_initial_data.sql sigmax-postgres:/tmp/

# 执行初始化
docker exec sigmax-postgres psql -U postgres -d sigmax_trading -f /tmp/01_create_database.sql
docker exec sigmax-postgres psql -U postgres -d sigmax_trading -f /tmp/02_create_tables.sql
docker exec sigmax-postgres psql -U postgres -d sigmax_trading -f /tmp/03_create_indexes.sql
docker exec sigmax-postgres psql -U postgres -d sigmax_trading -f /tmp/04_create_triggers.sql
docker exec sigmax-postgres psql -U postgres -d sigmax_trading -f /tmp/05_insert_initial_data.sql
```

### 4. 传统安装方式

使用提供的设置脚本一键创建数据库：

```bash
# 使用默认配置
./setup_database.sh

# 自定义配置
./setup_database.sh --host localhost --port 5432 --dbname sigmax_trading --user sigmax_user --password your_password

# 使用环境变量
export DB_HOST=localhost
export DB_PORT=5432
export DB_NAME=sigmax_trading
export DB_USER=sigmax_user
export DB_PASSWORD=your_password
./setup_database.sh
```

### 5. 手动设置

如果需要手动执行，按以下顺序运行SQL脚本：

```bash
# 使用Docker容器
# 1. 初始化数据库
docker exec sigmax-postgres psql -U postgres -d sigmax_trading -f /tmp/01_init_database.sql

# 2. 创建表结构
docker exec sigmax-postgres psql -U postgres -d sigmax_trading -f /tmp/02_create_tables_fixed.sql

# 3. 创建索引
docker exec sigmax-postgres psql -U postgres -d sigmax_trading -f /tmp/03_create_indexes.sql

# 4. 创建触发器
docker exec sigmax-postgres psql -U postgres -d sigmax_trading -f /tmp/04_create_triggers.sql

# 5. 插入初始数据
docker exec sigmax-postgres psql -U postgres -d sigmax_trading -f /tmp/05_insert_initial_data.sql

# 6. 设置迁移管理
docker exec sigmax-postgres psql -U postgres -d sigmax_trading -f /tmp/06_migrations.sql

# 使用本地PostgreSQL
# 1. 初始化数据库
psql -U postgres -f 01_init_database.sql

# 2. 创建表结构
psql -U postgres -d sigmax_trading -f 02_create_tables_fixed.sql

# 3. 创建索引
psql -U postgres -d sigmax_trading -f 03_create_indexes.sql

# 4. 创建触发器
psql -U postgres -d sigmax_trading -f 04_create_triggers.sql

# 5. 插入初始数据
psql -U postgres -d sigmax_trading -f 05_insert_initial_data.sql

# 6. 设置迁移管理
psql -U postgres -d sigmax_trading -f 06_migrations.sql
```

## 📊 数据库架构

### 核心表结构

#### 1. 交易所 (exchanges)
存储支持的交易所信息
- 基本信息：名称、显示名称、API端点
- 配置：费率、限制、支持的订单类型
- 状态：活跃、维护、停用

#### 2. 交易对 (trading_pairs)
存储交易对信息
- 基本信息：符号、基础资产、计价资产
- 精度：价格精度、数量精度
- 限制：最小/最大数量、价格范围

#### 3. 策略 (strategies)
存储交易策略信息
- 基本信息：名称、类型、描述
- 配置：策略参数、风险配置
- 状态：创建、运行、暂停、停止、错误

#### 4. 订单 (orders)
存储订单信息
- 基本信息：类型、方向、数量、价格
- 状态：待处理、部分成交、已成交、已取消
- 关联：策略、交易对、交易所

#### 5. 交易记录 (trades)
存储实际交易记录
- 执行信息：数量、价格、手续费
- 时间：交易时间、执行时间
- 关联：订单、交易对、交易所

#### 6. 投资组合 (portfolios)
存储投资组合信息
- 资金：初始资金、当前资金
- 统计：总盈亏、手续费、交易次数
- 指标：胜率、夏普比率、最大回撤

#### 7. 持仓 (positions)
存储持仓信息
- 基本信息：数量、平均价格、市场价格
- 盈亏：已实现盈亏、未实现盈亏
- 关联：投资组合、交易对、交易所

### 系统表

#### 8. 系统配置 (system_config)
存储系统配置参数
- 交易配置、风险配置、系统配置
- 通知配置、API配置、缓存配置

#### 9. 审计日志 (audit_logs)
存储数据变更审计记录
- 操作类型：插入、更新、删除
- 变更内容：旧值、新值
- 操作信息：时间、用户、IP

#### 10. 事件 (events)
存储系统事件记录
- 事件类型、事件数据
- 聚合信息、版本控制
- 关联ID、元数据

## 🌐 Hugging Face Spaces 部署详细指南

### 快速部署工具

使用提供的部署脚本快速准备Hugging Face Spaces部署：

```bash
# 查看部署选项
./huggingface/deploy.sh

# 方案1：外部数据库部署（推荐）
./huggingface/deploy.sh external-db

# 方案2：容器内数据库部署（测试用）
./huggingface/deploy.sh container-db

# 初始化外部数据库
./huggingface/deploy.sh init-db "postgresql://user:pass@host:port/db"
```

### 外部数据库服务推荐

#### 1. Supabase（推荐）
```bash
# 优势：
# - 免费层：500MB存储，2个并发连接
# - 自动备份
# - Web界面管理
# - 实时功能支持

# 设置步骤：
# 1. 访问 https://supabase.com
# 2. 创建新项目
# 3. 在 Settings > Database 获取连接信息
# 4. 使用部署脚本初始化：
./huggingface/deploy.sh init-db "postgresql://postgres:[password]@db.[project-ref].supabase.co:5432/postgres"
```

#### 2. Neon（高性能）
```bash
# 优势：
# - 免费层：3GB存储，无连接限制
# - 自动扩缩容
# - 分支功能
# - 高性能

# 设置步骤：
# 1. 访问 https://neon.tech
# 2. 创建新项目
# 3. 获取连接字符串
# 4. 初始化数据库
```

#### 3. Railway（简单易用）
```bash
# 优势：
# - 简单部署
# - 自动SSL
# - 监控面板
# - Git集成

# 设置步骤：
# 1. 访问 https://railway.app
# 2. 创建PostgreSQL服务
# 3. 获取连接信息
# 4. 初始化数据库
```

### Hugging Face Spaces 配置

#### 环境变量设置
在Hugging Face Spaces的Settings中添加：

```bash
# 必需变量
DATABASE_URL=postgresql://your-connection-string
WEB_HOST=0.0.0.0
WEB_PORT=7860

# 可选变量
RUST_LOG=info
JWT_SECRET=your-super-secret-jwt-key-change-in-production
LOG_LEVEL=info
DEVELOPMENT_MODE=false
```

#### 文件结构
```
your-huggingface-space/
├── Dockerfile                 # 从 huggingface/Dockerfile.spaces 复制
├── requirements.txt           # 空文件（Docker不需要）
├── README.md                  # Space描述
├── [SigmaX项目所有文件]
└── database/
    └── huggingface/
        ├── README.md          # 部署指南
        ├── init_external_db.sql # 数据库初始化脚本
        └── deploy.sh          # 部署工具
```

### 部署流程

#### 方案1：外部数据库（生产推荐）

1. **创建数据库服务**
   ```bash
   # 选择云服务商（Supabase/Neon/Railway）
   # 创建PostgreSQL实例
   # 获取连接字符串
   ```

2. **初始化数据库**
   ```bash
   # 使用部署脚本
   ./huggingface/deploy.sh init-db "your-database-url"

   # 或手动执行
   psql "your-database-url" -f huggingface/init_external_db.sql
   ```

3. **准备部署文件**
   ```bash
   ./huggingface/deploy.sh external-db
   ```

4. **创建Hugging Face Space**
   - 访问 https://huggingface.co/spaces
   - 选择Docker SDK
   - 上传项目文件
   - 设置环境变量

5. **验证部署**
   ```bash
   # 访问你的Space URL
   curl https://your-space.hf.space/api/health
   ```

#### 方案2：容器内数据库（测试用）

1. **准备部署文件**
   ```bash
   ./huggingface/deploy.sh container-db
   ```

2. **创建Hugging Face Space**
   - 上传修改后的文件
   - 设置基本环境变量

3. **注意事项**
   - 数据不会持久化
   - 首次启动较慢
   - 适合演示和测试

### 监控和维护

#### 健康检查
```bash
# API健康检查
curl https://your-space.hf.space/api/health

# 数据库健康检查
curl https://your-space.hf.space/api/status
```

#### 日志监控
- 在Hugging Face Spaces界面查看Logs标签
- 监控应用启动和运行状态
- 检查数据库连接状态

#### 数据备份
```bash
# 对于外部数据库，使用云服务商的备份功能
# 或手动备份：
pg_dump "your-database-url" > backup.sql
```

## 🔧 数据库管理

### 当前数据库状态

**数据库信息**
- 数据库名称: sigmax_trading
- PostgreSQL版本: 15
- 字符编码: UTF8
- 时区: UTC

**已创建的表**
```sql
-- 查看所有表
SELECT table_name FROM information_schema.tables
WHERE table_schema='public' AND table_type='BASE TABLE';

-- 当前包含的表：
-- 1. exchanges (交易所)
-- 2. trading_pairs (交易对)
-- 3. portfolios (投资组合)
-- 4. strategies (策略)
-- 5. orders (订单)
-- 6. trades (交易记录)
-- 7. positions (持仓)
-- 8. system_config (系统配置)
-- 9. audit_logs (审计日志)
-- 10. events (事件)
```

**初始数据**
```sql
-- 查看交易所数据
SELECT name, display_name, status FROM exchanges;
-- 结果: Binance, Coinbase, Kraken

-- 查看交易对数据
SELECT symbol, base_asset, quote_asset FROM trading_pairs LIMIT 5;
-- 结果: BTC/USDT, ETH/USDT, BNB/USDT, ADA/USDT, DOT/USDT 等
```

### 快速连接测试

使用提供的测试脚本验证数据库连接和状态：

```bash
# 运行连接测试脚本
./test_connection.sh

# 输出示例：
# 🔍 SigmaX数据库连接测试
# ==========================
# 📦 检查Docker容器状态...
# ✅ sigmax-postgres容器正在运行
# 🔗 测试数据库连接...
# ✅ 数据库连接成功
# 📊 数据库信息: PostgreSQL 15.13
# 📋 表统计: 表数量: 11
# 🏢 交易所数据: Binance, Coinbase, Kraken, Simulator
# 💱 交易对数据: BTC/USDT, ETH/USDT, BNB/USDT...
# 🏥 健康检查: 所有组件正常
# ✅ 数据库测试完成！
```

该脚本会自动：
- 检查Docker容器状态
- 测试数据库连接
- 显示数据库版本信息
- 统计表数量
- 查看初始数据
- 运行健康检查
- 显示连接信息

### 健康检查

```sql
-- 运行健康检查
SELECT * FROM health_check();

-- 查看数据库统计
SELECT * FROM get_database_stats();

-- 验证数据库连接
SELECT version();

-- 检查表数量
SELECT COUNT(*) as table_count
FROM information_schema.tables
WHERE table_schema='public' AND table_type='BASE TABLE';
```

### 数据清理

```sql
-- 清理测试数据
SELECT cleanup_test_data();

-- 清理过期数据 (90天前)
SELECT cleanup_expired_data(90);

-- 清理孤立数据
SELECT cleanup_orphaned_data();

-- 重置演示数据
SELECT reset_demo_data();
```

### 数据库维护

```sql
-- 更新统计信息
SELECT update_table_statistics();

-- 清理数据库碎片
SELECT vacuum_database();

-- 重建索引
SELECT rebuild_indexes();

-- 每日清理任务
SELECT daily_cleanup_task();

-- 每周清理任务
SELECT weekly_cleanup_task();
```

## 📈 性能优化

### 索引策略

数据库包含以下类型的索引：
- **单列索引**：常用查询字段
- **复合索引**：多字段组合查询
- **唯一索引**：保证数据唯一性
- **部分索引**：条件过滤索引

### 查询优化

- 使用预定义的视图进行复杂查询
- 利用生成列避免重复计算
- 合理使用分页和限制
- 定期更新表统计信息

## 🔒 安全考虑

### 权限管理

- 使用专用数据库用户
- 最小权限原则
- 定期审查权限

### 数据保护

- 敏感配置加密存储
- 审计日志记录所有变更
- 定期备份重要数据

## 📦 备份和恢复

### 备份脚本

**Docker环境备份**
```bash
#!/bin/bash
# Docker环境创建备份
BACKUP_DIR="/var/backups/sigmax"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="$BACKUP_DIR/sigmax_backup_$TIMESTAMP.sql"

mkdir -p $BACKUP_DIR

# 使用Docker容器进行备份
docker exec sigmax-postgres pg_dump -U postgres -d sigmax_trading > $BACKUP_FILE
gzip $BACKUP_FILE

# 清理旧备份
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete

echo "备份完成: $BACKUP_FILE.gz"
```

**本地环境备份**
```bash
#!/bin/bash
# 本地环境创建备份
BACKUP_DIR="/var/backups/sigmax"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="$BACKUP_DIR/sigmax_backup_$TIMESTAMP.sql"

mkdir -p $BACKUP_DIR
pg_dump -h localhost -U postgres -d sigmax_trading > $BACKUP_FILE
gzip $BACKUP_FILE

# 清理旧备份
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete
```

### 恢复脚本

**Docker环境恢复**
```bash
#!/bin/bash
# Docker环境恢复备份
BACKUP_FILE=$1

if [ -z "$BACKUP_FILE" ]; then
    echo "Usage: $0 <backup_file>"
    exit 1
fi

# 恢复数据库
gunzip -c $BACKUP_FILE | docker exec -i sigmax-postgres psql -U postgres -d sigmax_trading

echo "恢复完成: $BACKUP_FILE"
```

**本地环境恢复**
```bash
#!/bin/bash
# 本地环境恢复备份
BACKUP_FILE=$1

if [ -z "$BACKUP_FILE" ]; then
    echo "Usage: $0 <backup_file>"
    exit 1
fi

# 恢复数据库
gunzip -c $BACKUP_FILE | psql -h localhost -U postgres -d sigmax_trading
```

## 🔄 迁移管理

数据库使用版本化迁移管理：

```sql
-- 查看迁移历史
SELECT * FROM schema_migrations ORDER BY applied_at;

-- 添加新迁移
INSERT INTO schema_migrations (version, description, checksum) 
VALUES ('1.0.1', 'Add new feature', 'checksum_here');
```

## 📞 故障排除

### 常见问题

1. **Docker容器连接失败**
   - 检查容器状态: `docker ps -a | grep sigmax-postgres`
   - 启动容器: `docker start sigmax-postgres`
   - 查看容器日志: `docker logs sigmax-postgres`
   - 检查端口映射: `docker port sigmax-postgres`

2. **数据库连接失败**
   - 检查PostgreSQL服务状态
   - 验证连接参数
   - 检查防火墙设置
   - 确认容器网络配置

3. **权限错误**
   - 确认用户权限
   - 检查数据库所有者
   - 验证表权限
   - 检查Docker容器用户配置

4. **性能问题**
   - 检查索引使用情况
   - 分析查询执行计划
   - 更新表统计信息
   - 监控容器资源使用

### 诊断命令

**Docker环境诊断**
```bash
# 检查容器状态
docker ps -a | grep sigmax-postgres

# 检查容器资源使用
docker stats sigmax-postgres

# 进入容器
docker exec -it sigmax-postgres bash

# 连接数据库
docker exec -it sigmax-postgres psql -U postgres -d sigmax_trading
```

**数据库诊断**
```sql
-- 检查活跃连接
SELECT * FROM pg_stat_activity;

-- 检查锁等待
SELECT * FROM pg_locks WHERE NOT granted;

-- 检查表大小
SELECT * FROM get_database_stats();

-- 检查索引使用情况
SELECT * FROM pg_stat_user_indexes;

-- 检查数据库版本和配置
SELECT version();
SHOW all;

-- 检查表和数据统计
SELECT
    schemaname,
    tablename,
    attname,
    n_distinct,
    correlation
FROM pg_stats
WHERE schemaname = 'public'
ORDER BY tablename, attname;
```

## 📝 更新日志

### v1.1.0 (2024-06-09)
- ✅ 添加Docker部署支持
- ✅ 更新数据库连接信息
- ✅ 完成PostgreSQL 15安装和配置
- ✅ 创建所有核心表结构
- ✅ 插入初始交易所和交易对数据
- ✅ 添加健康检查函数
- ✅ 集成SigmaX Web API
- ✅ 更新备份和恢复脚本
- ✅ 添加Docker环境诊断命令

### v1.0.0 (2024-12-19)
- 初始数据库结构
- 核心表和索引
- 触发器和函数
- 初始数据和配置
- 管理和清理脚本

## 🤝 贡献

如需修改数据库结构：

1. 创建新的迁移脚本
2. 更新相关文档
3. 测试迁移过程
4. 更新版本号

## 📄 许可证

本项目采用 MIT 许可证。
