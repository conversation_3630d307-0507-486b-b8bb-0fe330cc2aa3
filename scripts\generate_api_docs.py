#!/usr/bin/env python3
"""
SigmaX-R API文档生成器
自动生成完整的API文档
"""

import requests
import json
import time
from datetime import datetime
from pathlib import Path

BASE_URL = "http://127.0.0.1:8080"

def test_api_endpoint(method, endpoint, data=None):
    """测试API端点并返回详细信息"""
    url = f"{BASE_URL}{endpoint}"
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, timeout=10)
        elif method.upper() == "POST":
            response = requests.post(url, json=data, timeout=10)
        elif method.upper() == "PUT":
            response = requests.put(url, json=data, timeout=10)
        elif method.upper() == "DELETE":
            response = requests.delete(url, timeout=10)
        else:
            return None
            
        return {
            'status_code': response.status_code,
            'success': response.status_code == 200,
            'response_text': response.text[:500] if response.text else '',
            'headers': dict(response.headers)
        }
            
    except requests.exceptions.RequestException as e:
        return {
            'status_code': 0,
            'success': False,
            'response_text': f"连接错误: {str(e)}",
            'headers': {}
        }

def generate_api_documentation():
    """生成完整的API文档"""
    
    # API端点定义
    api_endpoints = [
        # 第一阶段：基本API
        {
            'category': '基本API',
            'description': '健康检查、系统状态、订单、交易、策略',
            'endpoints': [
                ('GET', '/api/health', None, '健康检查'),
                ('GET', '/api/status', None, '获取系统状态'),
                ('GET', '/api/orders', None, '获取订单列表'),
                ('GET', '/api/trades', None, '获取交易记录'),
                ('GET', '/api/strategies', None, '获取策略列表'),
            ]
        },
        # 第二阶段：回测API
        {
            'category': '回测API',
            'description': '回测数据、配置、进度、结果、报告',
            'endpoints': [
                ('GET', '/api/backtest/files', None, '获取回测数据文件列表'),
                ('GET', '/api/backtest/files/BNB_USDT_1d.json', None, '获取K线数据'),
                ('GET', '/api/backtest/files/BNB_USDT_1d.json/validate', None, '验证回测文件'),
                ('GET', '/api/backtest/stats', None, '获取回测数据统计'),
            ]
        },
        # 第三阶段：交易所管理API
        {
            'category': '交易所管理API',
            'description': '交易所连接、状态、余额、订单、交易对',
            'endpoints': [
                ('GET', '/api/v2/exchanges', None, '获取交易所列表'),
                ('GET', '/api/v2/exchanges/binance/info', None, '获取交易所信息'),
                ('GET', '/api/v2/exchanges/binance/status', None, '获取交易所状态'),
                ('GET', '/api/v2/exchanges/binance/health', None, '获取交易所健康状态'),
                ('GET', '/api/v2/exchanges/status/all', None, '获取所有交易所状态'),
                ('GET', '/api/v2/exchanges/binance/balances', None, '获取交易所余额'),
                ('GET', '/api/v2/exchanges/binance/orders', None, '获取交易所订单'),
                ('GET', '/api/v2/exchanges/binance/trades', None, '获取交易所交易记录'),
                ('GET', '/api/v2/exchanges/binance/symbols', None, '获取交易所交易对'),
            ]
        },
        # 第四阶段：市场数据API
        {
            'category': '市场数据API',
            'description': '实时价格、订单簿、K线、成交量、波动率',
            'endpoints': [
                ('GET', '/api/v2/market/ticker/BTCUSDT', None, '获取实时价格'),
                ('GET', '/api/v2/market/orderbook/BTCUSDT', None, '获取订单簿'),
                ('GET', '/api/v2/market/trades/BTCUSDT', None, '获取最近交易'),
                ('GET', '/api/v2/market/candles/BTCUSDT', None, '获取K线数据'),
                ('GET', '/api/v2/market/volume/BTCUSDT', None, '获取成交量数据'),
                ('GET', '/api/v2/market/volatility/BTCUSDT', None, '获取波动率数据'),
                ('GET', '/api/v2/market/correlation', None, '获取相关性分析'),
                ('GET', '/api/v2/market/sentiment', None, '获取市场情绪'),
            ]
        },
        # 第五阶段：报告生成API
        {
            'category': '报告生成API',
            'description': '性能报告、风险报告、交易报告、导出',
            'endpoints': [
                ('GET', '/api/v2/reports/performance', None, '获取性能报告'),
                ('GET', '/api/v2/reports/risk', None, '获取风险报告'),
                ('GET', '/api/v2/reports/trades', None, '获取交易报告'),
                ('GET', '/api/v2/reports/portfolio', None, '获取投资组合报告'),
                ('GET', '/api/v2/reports/templates', None, '获取报告模板'),
            ]
        },
        # 第六阶段：系统配置API
        {
            'category': '系统配置API',
            'description': '系统配置、交易配置、通知配置',
            'endpoints': [
                ('GET', '/api/v2/config', None, '获取系统配置'),
                ('GET', '/api/v2/config/trading', None, '获取交易配置'),
                ('GET', '/api/v2/config/notifications', None, '获取通知配置'),
                ('GET', '/api/v2/config/channels', None, '获取通知渠道'),
            ]
        },
        # 第七阶段：高级管理API
        {
            'category': '高级管理API',
            'description': '风险管理、投资组合、订单、持仓、引擎',
            'endpoints': [
                ('GET', '/api/v2/risk/parameters', None, '获取风险参数'),
                ('GET', '/api/v2/risk/monitor/realtime', None, '获取实时风险监控'),
                ('GET', '/api/v2/risk/monitor/alerts', None, '获取风险告警'),
                ('GET', '/api/v2/portfolios', None, '获取投资组合列表'),
                ('GET', '/api/v2/orders', None, '获取订单列表'),
                ('GET', '/api/v2/orders/history', None, '获取订单历史'),
                ('GET', '/api/v2/orders/statistics', None, '获取订单统计'),
                ('GET', '/api/v2/positions', None, '获取持仓列表'),
                ('GET', '/api/v2/positions/summary', None, '获取持仓摘要'),
                ('GET', '/api/v2/positions/pnl', None, '获取盈亏详情'),
            ]
        },
        # 第八阶段：系统管理API
        {
            'category': '系统管理API',
            'description': '数据管理、性能监控、缓存、数据库',
            'endpoints': [
                ('GET', '/api/v2/data/providers', None, '获取数据提供者列表'),
                ('GET', '/api/v2/data/quality/report', None, '获取数据质量报告'),
                ('GET', '/api/v2/data/integrity', None, '检查数据完整性'),
                ('GET', '/api/v2/monitoring/metrics', None, '获取系统指标'),
                ('GET', '/api/v2/monitoring/health', None, '获取系统健康状态'),
                ('GET', '/api/v2/monitoring/resources', None, '获取资源使用情况'),
                ('GET', '/api/v2/monitoring/performance', None, '获取性能统计'),
                ('GET', '/api/v2/monitoring/alerts', None, '获取监控警报'),
                ('GET', '/api/v2/monitoring/logs', None, '获取系统日志'),
                ('GET', '/api/v2/cache/stats', None, '获取缓存统计'),
                ('GET', '/api/v2/cache/keys', None, '获取缓存键列表'),
                ('GET', '/api/v2/cache/health', None, '获取缓存健康状态'),
                ('GET', '/api/v2/database/status', None, '获取数据库状态'),
                ('GET', '/api/v2/database/connections', None, '获取连接池状态'),
            ]
        },
        # 第九阶段：策略与风控API
        {
            'category': '策略与风控API',
            'description': '策略管理、风险配置、策略执行、风控管理',
            'endpoints': [
                ('GET', '/api/v2/strategies/supported', None, '获取支持的策略类型'),
                ('GET', '/api/v2/strategies/grid/template', None, '获取策略配置模板'),
                ('GET', '/api/v2/risk-configs', None, '获取风险配置列表'),
                ('GET', '/api/v2/risk-configs/templates', None, '获取风险配置模板'),
                ('GET', '/api/strategies/execution/running', None, '获取运行中的策略'),
                ('GET', '/api/strategies/execution/status', None, '获取所有执行状态'),
                ('GET', '/api/risk/rules', None, '获取风控规则列表'),
                ('GET', '/api/risk/statistics', None, '获取风控统计'),
            ]
        },
        # 第十阶段：引擎与风险监控API
        {
            'category': '引擎与风险监控API',
            'description': '引擎管理、回测配置、风险参数、实时监控',
            'endpoints': [
                ('GET', '/api/engines', None, '获取引擎列表'),
                ('GET', '/api/v2/risk/parameters/max_position_ratio', None, '获取单个风险参数'),
            ]
        }
    ]
    
    # 生成文档
    doc_content = []
    doc_content.append("# SigmaX-R 交易系统 API 文档")
    doc_content.append("")
    doc_content.append(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    doc_content.append("")
    doc_content.append("## 📊 API概览")
    doc_content.append("")
    
    total_endpoints = 0
    successful_endpoints = 0
    
    for category_info in api_endpoints:
        category = category_info['category']
        description = category_info['description']
        endpoints = category_info['endpoints']
        
        doc_content.append(f"### {category}")
        doc_content.append(f"**描述**: {description}")
        doc_content.append("")
        
        category_success = 0
        category_total = len(endpoints)
        
        for method, endpoint, data, desc in endpoints:
            print(f"测试 {method} {endpoint}...")
            result = test_api_endpoint(method, endpoint, data)
            
            if result and result['success']:
                status = "✅"
                category_success += 1
                successful_endpoints += 1
            else:
                status = "❌"
            
            doc_content.append(f"- {status} `{method} {endpoint}` - {desc}")
            
            total_endpoints += 1
            time.sleep(0.1)
        
        success_rate = (category_success / category_total) * 100 if category_total > 0 else 0
        doc_content.append(f"  - **成功率**: {success_rate:.1f}% ({category_success}/{category_total})")
        doc_content.append("")
    
    # 添加总结
    overall_success_rate = (successful_endpoints / total_endpoints) * 100 if total_endpoints > 0 else 0
    doc_content.append("## 📈 总体统计")
    doc_content.append("")
    doc_content.append(f"- **总API端点数**: {total_endpoints}")
    doc_content.append(f"- **成功端点数**: {successful_endpoints}")
    doc_content.append(f"- **失败端点数**: {total_endpoints - successful_endpoints}")
    doc_content.append(f"- **总体成功率**: {overall_success_rate:.1f}%")
    doc_content.append("")
    
    # 保存文档
    docs_dir = Path("docs/api")
    docs_dir.mkdir(parents=True, exist_ok=True)
    
    doc_file = docs_dir / "api_documentation.md"
    with open(doc_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(doc_content))
    
    print(f"📚 API文档已生成: {doc_file}")
    print(f"📊 总体成功率: {overall_success_rate:.1f}% ({successful_endpoints}/{total_endpoints})")
    
    return doc_file

if __name__ == "__main__":
    print("🚀 开始生成SigmaX-R API文档...")
    print("=" * 60)
    
    doc_file = generate_api_documentation()
    
    print("\n" + "=" * 60)
    print("🎉 API文档生成完成！")
    print(f"📄 文档位置: {doc_file}")
    print(f"🕒 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
