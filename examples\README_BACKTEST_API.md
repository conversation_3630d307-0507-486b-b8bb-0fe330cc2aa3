# SigmaX-R 回测API使用指南

## 📋 概述

SigmaX-R提供了完整的RESTful API来进行量化交易策略回测。通过API调用，您可以：

- 🔧 创建和配置回测引擎
- 📊 设置策略参数和数据源
- 🚀 启动回测并实时监控进度
- 📈 获取详细的回测结果和分析报告

## 🚀 快速开始

### 1. 启动服务器

```bash
# 启动SigmaX-R服务器
cargo run --bin sigmax-web
```

服务器默认运行在 `http://localhost:8080`

### 2. 运行测试脚本

```bash
# 基本API测试
python examples/test_backtest_api.py

# 完整回测演示
python examples/api_backtest_demo.py
```

## 📡 API端点详解

### 基础端点

| 端点 | 方法 | 功能 |
|------|------|------|
| `/health` | GET | 健康检查 |
| `/api/v1/backtest/files` | GET | 获取可用数据文件 |
| `/api/v1/engines` | POST | 创建新引擎 |

### 回测配置

| 端点 | 方法 | 功能 |
|------|------|------|
| `/api/v1/engines/{id}/backtest/config` | POST | 设置回测配置 |
| `/api/v1/engines/{id}/backtest/config` | GET | 获取回测配置 |
| `/api/v1/engines/{id}/backtest/config/status` | GET | 检查配置状态 |

### 回测执行

| 端点 | 方法 | 功能 |
|------|------|------|
| `/api/v1/engines/{id}/start` | POST | 启动回测 |
| `/api/v1/engines/{id}/stop` | POST | 停止回测 |
| `/api/v1/engines/{id}/status` | GET | 获取引擎状态 |

### 结果获取

| 端点 | 方法 | 功能 |
|------|------|------|
| `/api/v1/engines/{id}/backtest/progress` | GET | 获取回测进度 |
| `/api/v1/engines/{id}/backtest/result` | GET | 获取回测结果 |
| `/api/v1/engines/{id}/backtest/trades` | GET | 获取交易记录 |
| `/api/v1/engines/{id}/backtest/portfolio` | GET | 获取投资组合历史 |
| `/api/v1/engines/{id}/backtest/report` | GET | 获取详细报告 |

## 🔧 使用示例

### 1. 创建引擎

```bash
curl -X POST http://localhost:8080/api/v1/engines \
  -H "Content-Type: application/json" \
  -d '{
    "engine_type": "Backtest",
    "trading_pairs": ["BNB_USDT"],
    "initial_capital": "10000.0"
  }'
```

### 2. 设置回测配置

```bash
curl -X POST http://localhost:8080/api/v1/engines/{engine_id}/backtest/config \
  -H "Content-Type: application/json" \
  -d '{
    "data_file": "BNB_USDT_1d_60.json",
    "initial_capital": "10000.0",
    "trading_pairs": ["BNB_USDT"],
    "timeframe": "1d",
    "strategy_config": {
      "type": "AsymmetricVolatilityGrid",
      "grid_levels": 10,
      "grid_spacing": 0.02,
      "base_order_size": 100.0,
      "max_position_size": 5000.0
    }
  }'
```

### 3. 启动回测

```bash
curl -X POST http://localhost:8080/api/v1/engines/{engine_id}/start
```

### 4. 监控进度

```bash
curl http://localhost:8080/api/v1/engines/{engine_id}/backtest/progress
```

### 5. 获取结果

```bash
curl http://localhost:8080/api/v1/engines/{engine_id}/backtest/result
```

## 📊 策略配置

### 网格策略配置示例

```json
{
  "type": "AsymmetricVolatilityGrid",
  "grid_levels": 10,
  "grid_spacing": 0.02,
  "base_order_size": 100.0,
  "max_position_size": 5000.0,
  "profit_target": 0.05,
  "stop_loss": 0.03,
  "volatility_threshold": 0.1,
  "adaptive_spacing": true
}
```

### 支持的时间框架

- `1m` - 1分钟
- `5m` - 5分钟
- `15m` - 15分钟
- `30m` - 30分钟
- `1h` - 1小时
- `4h` - 4小时
- `1d` - 1天
- `1w` - 1周

## 📈 响应格式

### 成功响应

```json
{
  "success": true,
  "data": { ... },
  "message": "操作成功",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 错误响应

```json
{
  "success": false,
  "error": {
    "code": "BAD_REQUEST",
    "message": "参数错误",
    "details": "..."
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 🔍 进度监控

回测进度响应包含：

```json
{
  "engine_id": "uuid",
  "status": "Running",
  "progress": {
    "current_candle": 45,
    "total_candles": 60,
    "percentage": 75.0,
    "current_time": "2024-01-15T00:00:00Z",
    "elapsed_time": "00:02:30",
    "estimated_remaining": "00:00:50"
  },
  "current_stats": {
    "total_trades": 12,
    "current_balance": "10500.00",
    "unrealized_pnl": "500.00",
    "max_drawdown": "2.5%"
  }
}
```

## 🛠️ 故障排除

### 常见问题

1. **连接失败**
   - 确保服务器正在运行
   - 检查端口8080是否可用

2. **数据文件不存在**
   - 检查 `bt_klines` 目录中的数据文件
   - 使用 `/api/v1/backtest/files` 查看可用文件

3. **策略配置错误**
   - 确保策略类型正确
   - 检查参数格式和范围

4. **回测启动失败**
   - 确保已设置回测配置
   - 检查引擎状态

### 调试技巧

- 使用 `test_backtest_api.py` 进行基本功能测试
- 查看服务器日志获取详细错误信息
- 使用浏览器开发者工具监控API调用

## 📞 支持

如需帮助，请参考：
- 项目文档：`docs/` 目录
- 示例代码：`examples/` 目录
- 源代码：`web/src/handlers/backtest_*.rs`
