#!/bin/bash

# SigmaX-R 风险管理模块渐进式重构启动脚本
# 开始实际的迁移过程

set -e

echo "🚀 开始 SigmaX-R 风险管理模块渐进式重构"
echo "========================================"

# 检查环境
echo "📋 检查环境..."
if [ ! -f "Cargo.toml" ]; then
    echo "❌ 错误：请在项目根目录运行此脚本"
    exit 1
fi

# 检查新模块是否存在
if [ ! -d "risk_new" ]; then
    echo "❌ 错误：新模块 risk_new 不存在，请先运行准备脚本"
    exit 1
fi

# 创建备份
echo "💾 创建当前状态备份..."
BACKUP_DIR="backups/migration_start_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"
cp -r risk/ "$BACKUP_DIR/" 2>/dev/null || true
cp -r web/src/ "$BACKUP_DIR/" 2>/dev/null || true
cp -r src/ "$BACKUP_DIR/" 2>/dev/null || true
echo "✅ 备份已保存到 $BACKUP_DIR"

# 编译检查
echo "🔨 编译检查..."
echo "检查新模块..."
cd risk_new
if ! cargo check; then
    echo "❌ 新模块编译失败"
    exit 1
fi
cd ..

echo "检查主项目..."
if ! cargo check; then
    echo "❌ 主项目编译失败"
    exit 1
fi

# 运行测试
echo "🧪 运行测试..."
echo "运行新模块测试..."
cd risk_new
if ! cargo test; then
    echo "❌ 新模块测试失败"
    exit 1
fi
cd ..

echo "运行集成测试..."
cd risk_new
if ! cargo test --test integration_tests; then
    echo "❌ 集成测试失败"
    exit 1
fi
cd ..

# 创建迁移配置文件
echo "⚙️ 创建迁移配置..."
mkdir -p config
cat > config/migration.toml << EOF
# SigmaX-R 风险管理模块迁移配置

[migration]
# 是否启用新引擎
enable_new_engine = false

# 是否启用双写模式（新旧系统并行运行）
enable_dual_write = true

# 是否启用结果对比
enable_result_comparison = true

# 新引擎流量比例 (0-100)
new_engine_traffic_percentage = 0

# 自动回滚错误率阈值 (%)
auto_rollback_error_rate = 5.0

# 自动回滚响应时间阈值 (毫秒)
auto_rollback_response_time_ms = 1000

[monitoring]
# 是否启用详细监控
enable_detailed_monitoring = true

# 是否记录性能指标
enable_performance_metrics = true

# 是否记录一致性检查
enable_consistency_checks = true

# 指标收集间隔（秒）
metrics_collection_interval_seconds = 60

# 日志级别
log_level = "info"

[risk_engine]
# 是否启用并行执行
parallel_execution = true

# 最大执行时间（毫秒）
max_execution_time_ms = 5000

# 是否启用缓存
enable_cache = true

# 默认风险等级
default_risk_level = "Medium"

[rule_engine]
# 最大规则执行时间（毫秒）
max_rule_execution_time_ms = 1000

# 是否启用规则缓存
enable_rule_cache = true

# 缓存过期时间（秒）
cache_expiry_seconds = 300

[metrics_calculator]
# VaR 置信度
var_confidence_levels = [0.95, 0.99]

# 波动率计算窗口（天）
volatility_window_days = 30

# 相关性计算窗口（天）
correlation_window_days = 60

# 是否使用指数加权移动平均
use_ewma = true

# EWMA 衰减因子
ewma_lambda = 0.94
EOF

# 创建数据库迁移脚本
echo "🗄️ 准备数据库迁移..."
mkdir -p database/migrations
cat > database/migrations/001_create_new_risk_tables.sql << EOF
-- 新的统一风险规则表
CREATE TABLE IF NOT EXISTS unified_risk_rules_new (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    rule_type VARCHAR(50) NOT NULL,
    parameters JSONB NOT NULL,
    conditions JSONB,
    enabled BOOLEAN NOT NULL DEFAULT true,
    priority INTEGER NOT NULL DEFAULT 100,
    strategy_types TEXT[] DEFAULT '{}',
    trading_pairs TEXT[] DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 规则执行历史表
CREATE TABLE IF NOT EXISTS rule_execution_history_new (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    rule_id UUID NOT NULL REFERENCES unified_risk_rules_new(id),
    execution_time TIMESTAMPTZ NOT NULL,
    result VARCHAR(20) NOT NULL,
    execution_time_ms BIGINT NOT NULL,
    context_summary TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 风险检查结果对比表
CREATE TABLE IF NOT EXISTS risk_check_comparison (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id UUID,
    old_engine_result BOOLEAN,
    new_engine_result BOOLEAN,
    consistent BOOLEAN,
    old_engine_time_ms BIGINT,
    new_engine_time_ms BIGINT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_unified_risk_rules_new_enabled ON unified_risk_rules_new(enabled);
CREATE INDEX IF NOT EXISTS idx_unified_risk_rules_new_strategy_types ON unified_risk_rules_new USING GIN(strategy_types);
CREATE INDEX IF NOT EXISTS idx_unified_risk_rules_new_trading_pairs ON unified_risk_rules_new USING GIN(trading_pairs);
CREATE INDEX IF NOT EXISTS idx_rule_execution_history_new_rule_id ON rule_execution_history_new(rule_id);
CREATE INDEX IF NOT EXISTS idx_rule_execution_history_new_execution_time ON rule_execution_history_new(execution_time);
CREATE INDEX IF NOT EXISTS idx_risk_check_comparison_created_at ON risk_check_comparison(created_at);
CREATE INDEX IF NOT EXISTS idx_risk_check_comparison_consistent ON risk_check_comparison(consistent);
EOF

# 运行数据库迁移（如果有数据库连接）
if command -v sqlx &> /dev/null && [ -n "${DATABASE_URL:-}" ]; then
    echo "运行数据库迁移..."
    sqlx migrate run --source database/migrations
else
    echo "⚠️  数据库迁移脚本已准备，请手动执行："
    echo "   sqlx migrate run --source database/migrations"
    echo "   或直接执行 database/migrations/001_create_new_risk_tables.sql"
fi

# 创建监控脚本
echo "📊 创建监控脚本..."
cat > scripts/monitor_migration.sh << 'EOF'
#!/bin/bash

echo "📊 SigmaX-R 迁移监控"
echo "==================="

# 检查迁移状态
echo "🔍 检查迁移状态..."
curl -s http://localhost:8080/api/migration/status | jq '.' || echo "无法连接到服务"

echo ""
echo "📈 获取迁移统计..."
curl -s http://localhost:8080/api/migration/stats | jq '.' || echo "无法获取统计信息"

echo ""
echo "🏥 检查健康状态..."
curl -s http://localhost:8080/api/migration/health | jq '.' || echo "无法获取健康状态"

echo ""
echo "⚙️ 当前配置..."
curl -s http://localhost:8080/api/migration/config | jq '.' || echo "无法获取配置"
EOF

chmod +x scripts/monitor_migration.sh

# 创建流量控制脚本
echo "🎛️ 创建流量控制脚本..."
cat > scripts/control_traffic.sh << 'EOF'
#!/bin/bash

if [ $# -eq 0 ]; then
    echo "用法: $0 <流量比例> [原因]"
    echo "示例: $0 5 '开始小流量测试'"
    echo "      $0 0 '回滚到旧系统'"
    exit 1
fi

PERCENTAGE=$1
REASON=${2:-"手动调整"}

echo "🎛️ 调整新引擎流量比例到 $PERCENTAGE%"
echo "原因: $REASON"

curl -X PUT http://localhost:8080/api/migration/traffic \
  -H "Content-Type: application/json" \
  -d "{\"percentage\": $PERCENTAGE, \"reason\": \"$REASON\"}" | jq '.'

echo ""
echo "✅ 流量调整完成"
EOF

chmod +x scripts/control_traffic.sh

# 创建回滚脚本
echo "🔙 创建回滚脚本..."
cat > scripts/emergency_rollback.sh << EOF
#!/bin/bash

echo "🚨 执行紧急回滚..."

# 停止新引擎
curl -X POST "http://localhost:8080/api/migration/rollback?reason=紧急回滚" | jq '.'

# 恢复备份（如果需要）
if [ -d "$BACKUP_DIR" ]; then
    echo "📁 恢复代码备份..."
    read -p "是否要恢复代码备份？(y/N): " -n 1 -r
    echo
    if [[ \$REPLY =~ ^[Yy]$ ]]; then
        cp -r "$BACKUP_DIR/risk/" ./
        cp -r "$BACKUP_DIR/web/src/" ./web/
        cp -r "$BACKUP_DIR/src/" ./
        echo "✅ 代码已恢复"
    fi
fi

echo "✅ 紧急回滚完成"
EOF

chmod +x scripts/emergency_rollback.sh

# 最终检查
echo "✅ 迁移准备完成！"
echo ""
echo "📋 下一步操作："
echo "1. 启动服务（如果尚未启动）"
echo "2. 监控迁移状态: ./scripts/monitor_migration.sh"
echo "3. 开始小流量测试: ./scripts/control_traffic.sh 5 '开始小流量测试'"
echo "4. 监控结果一致性和性能指标"
echo ""
echo "🎛️ 流量控制："
echo "- 0%:   ./scripts/control_traffic.sh 0"
echo "- 5%:   ./scripts/control_traffic.sh 5"
echo "- 20%:  ./scripts/control_traffic.sh 20"
echo "- 50%:  ./scripts/control_traffic.sh 50"
echo "- 100%: ./scripts/control_traffic.sh 100"
echo ""
echo "🚨 紧急回滚: ./scripts/emergency_rollback.sh"
echo ""
echo "📊 监控地址:"
echo "- 迁移状态: http://localhost:8080/api/migration/status"
echo "- 迁移仪表板: http://localhost:8080/api/migration/dashboard"
echo "- 健康检查: http://localhost:8080/api/migration/health"
echo ""
echo "🎉 准备就绪，可以开始渐进式迁移！"
EOF

chmod +x scripts/start_migration.sh
