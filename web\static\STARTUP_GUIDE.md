# SigmaX Trading System - 启动指南

## 🚀 快速开始

### 1. 系统要求
- 现代浏览器 (Chrome 80+, Firefox 75+, Safari 13+, Edge 80+)
- SigmaX后端服务运行在 `127.0.0.1:8080`

### 2. 启动步骤

#### 方法一：直接访问主应用
1. 打开浏览器
2. 访问 `index.html`
3. 系统会自动进行健康检查
4. 连接成功后进入主界面

#### 方法二：从演示页面开始
1. 访问 `demo.html` 查看功能演示
2. 点击"进入主应用"按钮
3. 自动跳转到主应用

### 3. 系统配置

#### 默认配置
```
系统地址: 127.0.0.1:8080
API路径: /api/v1
健康检查: http://127.0.0.1:8080/api/v1/health
```

#### 修改配置
如果您的后端服务运行在不同的地址或端口：

1. **使用配置页面** (推荐)
   - 访问 `config.html`
   - 修改服务器地址和端口
   - 点击"测试连接"验证
   - 保存配置

2. **使用URL参数**
   ```
   index.html?host=*************&port=9090
   ```

3. **修改代码**
   编辑 `js/config.js` 文件中的配置

### 4. 故障排除

#### 连接失败
如果系统显示连接失败：

1. **检查后端服务**
   - 确认SigmaX后端服务正在运行
   - 检查服务监听的地址和端口

2. **检查网络**
   - 确认网络连接正常
   - 检查防火墙设置

3. **使用测试工具**
   - 访问 `health-test.html`
   - 运行连接测试
   - 查看详细错误信息

4. **检查配置**
   - 访问 `config.html`
   - 验证服务器地址和端口设置
   - 使用"测试连接"功能

#### 常见问题

**Q: 页面显示"系统连接失败"**
A: 检查后端服务是否运行，确认地址和端口配置正确

**Q: 健康检查一直失败**
A: 确认后端服务提供 `/api/v1/health` 接口

**Q: 如何更改服务器地址？**
A: 使用 `config.html` 页面或修改 `js/config.js` 文件

**Q: 配置修改后不生效**
A: 刷新页面或清除浏览器缓存

### 5. 开发模式

启用调试模式查看详细日志：

1. **URL参数方式**
   ```
   index.html?debug=true
   ```

2. **配置页面方式**
   - 访问 `config.html`
   - 勾选"启用调试模式"
   - 保存配置

3. **代码方式**
   ```javascript
   window.SigmaXConfig.development.debug = true;
   ```

### 6. 页面说明

| 页面 | 用途 | 说明 |
|------|------|------|
| `index.html` | 主应用 | 完整的交易系统界面 |
| `demo.html` | 演示页面 | 功能演示和介绍 |
| `config.html` | 系统配置 | 可视化配置管理 |
| `health-test.html` | 连接测试 | API连接和健康检查测试 |

### 7. API接口要求

后端服务需要提供以下接口：

#### 健康检查接口
- **URL**: `GET /api/v1/health`
- **响应**: 
  ```json
  {
    "status": "ok",
    "healthy": true,
    "timestamp": "2024-01-01T12:00:00Z"
  }
  ```

#### CORS配置
确保后端服务配置了正确的CORS头：
```
Access-Control-Allow-Origin: *
Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS
Access-Control-Allow-Headers: Content-Type, Authorization
```

### 8. 浏览器兼容性

| 浏览器 | 最低版本 | 状态 |
|--------|----------|------|
| Chrome | 80+ | ✅ 完全支持 |
| Firefox | 75+ | ✅ 完全支持 |
| Safari | 13+ | ✅ 完全支持 |
| Edge | 80+ | ✅ 完全支持 |

### 9. 性能优化

- 使用CDN加载外部资源
- 启用浏览器缓存
- 压缩静态文件
- 使用HTTP/2

### 10. 安全注意事项

- 生产环境使用HTTPS
- 配置适当的CORS策略
- 定期更新依赖库
- 启用内容安全策略(CSP)

---

## 📞 技术支持

如果遇到问题，请：
1. 查看浏览器控制台错误信息
2. 使用 `health-test.html` 进行诊断
3. 检查后端服务日志
4. 联系技术支持团队

---

**祝您使用愉快！** 🎉
