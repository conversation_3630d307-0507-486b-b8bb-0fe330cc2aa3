//! 策略仓储接口定义

use sigmax_core::{StrategyId, StrategyState, StrategyInfo, SigmaXResult, StrategySystemConfig, StrategyTemplate};

/// 策略仓库
pub trait StrategyRepository: Send + Sync {
    /// 保存策略状态
    async fn save_strategy_state(&self, strategy_id: StrategyId, state: &StrategyState) -> SigmaXResult<()>;

    /// 加载策略状态
    async fn load_strategy_state(&self, strategy_id: StrategyId) -> SigmaXResult<Option<StrategyState>>;

    /// 获取所有策略
    async fn get_all_strategies(&self) -> SigmaXResult<Vec<StrategyInfo>>;

    // ============================================================================
    // 策略配置管理
    // ============================================================================

    /// 获取策略系统配置
    async fn get_strategy_config(&self) -> SigmaXResult<StrategySystemConfig>;

    /// 保存策略系统配置
    async fn save_strategy_config(&self, config: &StrategySystemConfig) -> SigmaXResult<()>;

    /// 重置策略配置为默认值
    async fn reset_strategy_config(&self) -> SigmaXResult<()>;

    // ============================================================================
    // 策略模板管理
    // ============================================================================

    /// 获取策略模板
    async fn get_strategy_template(&self, template_name: &str) -> SigmaXResult<Option<StrategyTemplate>>;

    /// 获取所有策略模板
    async fn get_all_strategy_templates(&self) -> SigmaXResult<Vec<(String, StrategyTemplate)>>;

    /// 保存策略模板
    async fn save_strategy_template(&self, template_name: &str, template: &StrategyTemplate) -> SigmaXResult<()>;

    /// 删除策略模板
    async fn delete_strategy_template(&self, template_name: &str) -> SigmaXResult<()>;
}
