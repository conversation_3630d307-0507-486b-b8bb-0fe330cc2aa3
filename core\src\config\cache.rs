//! 系统缓存配置模型

use serde::{Deserialize, Serialize};
use serde_json;
use validator::Validate;
use crate::SigmaXResult;

/// 缓存配置
///
/// 统一的缓存管理配置，整合了基础配置、性能监控、健康检查等功能
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Validate)]
pub struct CacheConfig {
    /// 配置名称
    pub name: String,
    /// 配置描述
    pub description: Option<String>,
    /// 是否启用缓存
    pub enabled: bool,

    // ============================================================================
    // 基础缓存配置
    // ============================================================================
    /// Redis缓存开关
    pub redis_enabled: bool,
    /// 默认缓存TTL(秒)
    pub default_ttl_seconds: u32,
    /// 最大缓存内存(MB)
    pub max_memory_mb: u32,
    /// 最大缓存条目数
    pub max_entries: u64,
    /// 缓存清理间隔(秒)
    pub cleanup_interval_seconds: u32,
    /// 内存使用阈值(百分比)
    pub memory_threshold_percentage: u32,

    // ============================================================================
    // 缓存策略配置
    // ============================================================================
    /// 缓存淘汰策略
    pub eviction_policy: CacheEvictionPolicy,
    /// 缓存预热策略
    pub warmup_strategy: CacheWarmupStrategy,
    /// 缓存分片配置
    pub sharding_config: CacheShardingConfig,

    // ============================================================================
    // 性能监控配置
    // ============================================================================
    /// 性能监控配置
    pub performance_monitoring: CachePerformanceMonitoringConfig,
    /// 健康检查配置
    pub health_check: CacheHealthCheckConfig,
    /// 告警配置
    pub alerting: CacheAlertingConfig,

    // ============================================================================
    // 高级功能配置
    // ============================================================================
    /// 缓存压缩配置
    pub compression: CacheCompressionConfig,
    /// 缓存持久化配置
    pub persistence: CachePersistenceConfig,
    /// 缓存同步配置
    pub synchronization: CacheSynchronizationConfig,
}

/// 缓存淘汰策略
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum CacheEvictionPolicy {
    LRU, // 最近最少使用
    LFU, // 最少使用频率
    FIFO, // 先进先出
    TTL, // 基于过期时间
    Random, // 随机淘汰
}

impl Default for CacheEvictionPolicy {
    fn default() -> Self {
        CacheEvictionPolicy::LRU
    }
}

/// 缓存预热策略
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct CacheWarmupStrategy {
    /// 是否启用预热
    pub enabled: bool,
    /// 预热优先级键列表
    pub priority_keys: Vec<String>,
    /// 预热批次大小
    pub batch_size: u32,
    /// 最大并发数
    pub max_concurrent: u32,
    /// 预热超时时间(秒)
    pub timeout_seconds: u32,
}

impl Default for CacheWarmupStrategy {
    fn default() -> Self {
        Self {
            enabled: false,
            priority_keys: Vec::new(),
            batch_size: 100,
            max_concurrent: 5,
            timeout_seconds: 30,
        }
    }
}

/// 缓存分片配置
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct CacheShardingConfig {
    /// 是否启用分片
    pub enabled: bool,
    /// 分片数量
    pub shard_count: u32,
    /// 分片策略
    pub strategy: CacheShardingStrategy,
    /// 一致性哈希虚拟节点数
    pub virtual_nodes: u32,
}

/// 缓存分片策略
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum CacheShardingStrategy {
    /// 哈希分片
    Hash,
    /// 一致性哈希
    ConsistentHash,
    /// 范围分片
    Range,
    /// 自定义分片
    Custom,
}

impl Default for CacheShardingStrategy {
    fn default() -> Self {
        CacheShardingStrategy::Hash
    }
}

impl Default for CacheShardingConfig {
    fn default() -> Self {
        Self {
            enabled: false,
            shard_count: 16,
            strategy: CacheShardingStrategy::default(),
            virtual_nodes: 150,
        }
    }
}

/// 缓存性能监控配置
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct CachePerformanceMonitoringConfig {
    /// 是否启用性能监控
    pub enabled: bool,
    /// 监控间隔(秒)
    pub monitoring_interval: u32,
    /// 性能数据保留天数
    pub data_retention_days: u32,
    /// 是否记录详细指标
    pub detailed_metrics: bool,
    /// 采样率 (0.0-1.0)
    pub sampling_rate: f64,
    /// 性能基准配置
    pub benchmarks: CachePerformanceBenchmarks,
}

/// 缓存性能基准配置
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct CachePerformanceBenchmarks {
    /// 平均响应时间基准(毫秒)
    pub average_response_time_baseline: f64,
    /// P95响应时间基准(毫秒)
    pub p95_response_time_baseline: f64,
    /// P99响应时间基准(毫秒)
    pub p99_response_time_baseline: f64,
    /// 吞吐量基准(操作/秒)
    pub throughput_baseline: f64,
    /// 命中率基准 (0.0-1.0)
    pub hit_rate_baseline: f64,
}

/// 缓存健康检查配置
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct CacheHealthCheckConfig {
    /// 是否启用健康检查
    pub enabled: bool,
    /// 检查间隔(秒)
    pub check_interval: u32,
    /// 超时时间(秒)
    pub timeout_seconds: u32,
    /// 重试次数
    pub retry_count: u32,
    /// 健康检查阈值
    pub thresholds: CacheHealthThresholds,
}

/// 缓存健康检查阈值
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct CacheHealthThresholds {
    /// 内存使用率警告阈值 (0-100)
    pub memory_usage_warning: u32,
    /// 内存使用率严重阈值 (0-100)
    pub memory_usage_critical: u32,
    /// 命中率警告阈值 (0.0-1.0)
    pub hit_rate_warning: f64,
    /// 命中率严重阈值 (0.0-1.0)
    pub hit_rate_critical: f64,
    /// 响应时间警告阈值(毫秒)
    pub response_time_warning: f64,
    /// 响应时间严重阈值(毫秒)
    pub response_time_critical: f64,
    /// 淘汰率警告阈值 (0.0-1.0)
    pub eviction_rate_warning: f64,
    /// 淘汰率严重阈值 (0.0-1.0)
    pub eviction_rate_critical: f64,
}

/// 缓存告警配置
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct CacheAlertingConfig {
    /// 是否启用告警
    pub enabled: bool,
    /// 告警规则列表
    pub alert_rules: Vec<CacheAlertRule>,
    /// 通知渠道配置
    pub notification_channels: Vec<CacheNotificationChannel>,
    /// 告警抑制配置
    pub suppression: CacheAlertSuppressionConfig,
}

/// 缓存告警规则
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct CacheAlertRule {
    /// 规则名称
    pub name: String,
    /// 规则描述
    pub description: Option<String>,
    /// 是否启用
    pub enabled: bool,
    /// 告警条件
    pub condition: CacheAlertCondition,
    /// 告警级别
    pub severity: CacheAlertSeverity,
    /// 评估间隔(秒)
    pub evaluation_interval: u32,
    /// 持续时间阈值(秒)
    pub duration_threshold: u32,
}

/// 缓存告警条件
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum CacheAlertCondition {
    /// 内存使用率超过阈值
    MemoryUsageExceeds(f64),
    /// 命中率低于阈值
    HitRateBelow(f64),
    /// 响应时间超过阈值
    ResponseTimeExceeds(f64),
    /// 淘汰率超过阈值
    EvictionRateExceeds(f64),
    /// 错误率超过阈值
    ErrorRateExceeds(f64),
}

/// 缓存告警级别
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum CacheAlertSeverity {
    /// 信息
    Info,
    /// 警告
    Warning,
    /// 严重
    Critical,
    /// 紧急
    Emergency,
}

/// 缓存通知渠道
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct CacheNotificationChannel {
    /// 渠道名称
    pub name: String,
    /// 渠道类型
    pub channel_type: CacheNotificationChannelType,
    /// 是否启用
    pub enabled: bool,
    /// 渠道配置
    pub config: serde_json::Value,
}

/// 缓存通知渠道类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum CacheNotificationChannelType {
    /// 邮件
    Email,
    /// Webhook
    Webhook,
    /// Slack
    Slack,
    /// Telegram
    Telegram,
    /// 短信
    SMS,
}

/// 缓存告警抑制配置
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct CacheAlertSuppressionConfig {
    /// 是否启用抑制
    pub enabled: bool,
    /// 抑制窗口(秒)
    pub suppression_window: u32,
    /// 最大告警频率(次/小时)
    pub max_alerts_per_hour: u32,
    /// 抑制规则
    pub suppression_rules: Vec<CacheAlertSuppressionRule>,
}

/// 缓存告警抑制规则
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct CacheAlertSuppressionRule {
    /// 规则名称
    pub name: String,
    /// 匹配条件
    pub match_conditions: Vec<String>,
    /// 抑制时间(秒)
    pub suppression_duration: u32,
}

/// 缓存压缩配置
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct CacheCompressionConfig {
    /// 是否启用压缩
    pub enabled: bool,
    /// 压缩算法
    pub algorithm: CacheCompressionAlgorithm,
    /// 压缩级别 (1-9)
    pub compression_level: u8,
    /// 最小压缩大小(字节)
    pub min_compression_size: u64,
    /// 压缩比阈值
    pub compression_ratio_threshold: f64,
}

/// 缓存压缩算法
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum CacheCompressionAlgorithm {
    /// Gzip压缩
    Gzip,
    /// LZ4压缩
    LZ4,
    /// Snappy压缩
    Snappy,
    /// Zstd压缩
    Zstd,
    /// 无压缩
    None,
}

/// 缓存持久化配置
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct CachePersistenceConfig {
    /// 是否启用持久化
    pub enabled: bool,
    /// 持久化策略
    pub strategy: CachePersistenceStrategy,
    /// 持久化间隔(秒)
    pub persistence_interval: u32,
    /// 持久化文件路径
    pub file_path: Option<String>,
    /// 最大持久化文件大小(MB)
    pub max_file_size_mb: u64,
    /// 持久化文件保留数量
    pub file_retention_count: u32,
}

/// 缓存持久化策略
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum CachePersistenceStrategy {
    /// 定时持久化
    Periodic,
    /// 写时持久化
    WriteThrough,
    /// 写后持久化
    WriteBack,
    /// 手动持久化
    Manual,
    /// 无持久化
    None,
}

/// 缓存同步配置
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct CacheSynchronizationConfig {
    /// 是否启用同步
    pub enabled: bool,
    /// 同步策略
    pub strategy: CacheSynchronizationStrategy,
    /// 同步间隔(秒)
    pub sync_interval: u32,
    /// 冲突解决策略
    pub conflict_resolution: CacheConflictResolution,
    /// 同步超时时间(秒)
    pub sync_timeout: u32,
    /// 最大重试次数
    pub max_retries: u32,
}

/// 缓存同步策略
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum CacheSynchronizationStrategy {
    /// 主从同步
    MasterSlave,
    /// 对等同步
    PeerToPeer,
    /// 集群同步
    Cluster,
    /// 无同步
    None,
}

/// 缓存冲突解决策略
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum CacheConflictResolution {
    /// 最后写入获胜
    LastWriteWins,
    /// 时间戳优先
    TimestampBased,
    /// 版本号优先
    VersionBased,
    /// 手动解决
    Manual,
}

// ============================================================================
// Default implementations for all cache config structs
// ============================================================================

impl Default for CacheConfig {
    fn default() -> Self {
        Self {
            name: "default".to_string(),
            description: Some("默认缓存配置".to_string()),
            enabled: true,

            // 基础缓存配置
            redis_enabled: true,
            default_ttl_seconds: 3600,
            max_memory_mb: 512,
            max_entries: 100000,
            cleanup_interval_seconds: 300,
            memory_threshold_percentage: 80,

            // 缓存策略配置
            eviction_policy: CacheEvictionPolicy::LRU,
            warmup_strategy: CacheWarmupStrategy::default(),
            sharding_config: CacheShardingConfig::default(),

            // 性能监控配置
            performance_monitoring: CachePerformanceMonitoringConfig::default(),
            health_check: CacheHealthCheckConfig::default(),
            alerting: CacheAlertingConfig::default(),

            // 高级功能配置
            compression: CacheCompressionConfig::default(),
            persistence: CachePersistenceConfig::default(),
            synchronization: CacheSynchronizationConfig::default(),
        }
    }
}

impl CacheConfig {
    /// 执行完整的缓存配置验证
    pub fn validate_complete(&self) -> SigmaXResult<()> {
        // 首先执行基础验证（通过validator derive宏）
        if let Err(validation_errors) = <Self as Validate>::validate(self) {
            return Err(crate::SigmaXError::from(validation_errors));
        }

        // 可以在这里添加更多跨字段验证逻辑
        Ok(())
    }
}

impl Default for CachePerformanceBenchmarks {
    fn default() -> Self {
        Self {
            average_response_time_baseline: 10.0,
            p95_response_time_baseline: 50.0,
            p99_response_time_baseline: 100.0,
            throughput_baseline: 1000.0,
            hit_rate_baseline: 0.95,
        }
    }
}

impl Default for CachePerformanceMonitoringConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            monitoring_interval: 60,
            data_retention_days: 7,
            detailed_metrics: false,
            sampling_rate: 0.1,
            benchmarks: CachePerformanceBenchmarks::default(),
        }
    }
}

impl Default for CacheHealthThresholds {
    fn default() -> Self {
        Self {
            memory_usage_warning: 80,
            memory_usage_critical: 95,
            hit_rate_warning: 0.8,
            hit_rate_critical: 0.6,
            response_time_warning: 100.0,
            response_time_critical: 500.0,
            eviction_rate_warning: 0.1,
            eviction_rate_critical: 0.3,
        }
    }
}

impl Default for CacheHealthCheckConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            check_interval: 30,
            timeout_seconds: 5,
            retry_count: 3,
            thresholds: CacheHealthThresholds::default(),
        }
    }
}

impl Default for CacheAlertSuppressionConfig {
    fn default() -> Self {
        Self {
            enabled: false,
            suppression_window: 300,
            max_alerts_per_hour: 10,
            suppression_rules: Vec::new(),
        }
    }
}

impl Default for CacheAlertingConfig {
    fn default() -> Self {
        Self {
            enabled: false,
            alert_rules: Vec::new(),
            notification_channels: Vec::new(),
            suppression: CacheAlertSuppressionConfig::default(),
        }
    }
}

impl Default for CacheCompressionAlgorithm {
    fn default() -> Self {
        CacheCompressionAlgorithm::None
    }
}

impl Default for CacheCompressionConfig {
    fn default() -> Self {
        Self {
            enabled: false,
            algorithm: CacheCompressionAlgorithm::default(),
            compression_level: 6,
            min_compression_size: 1024,
            compression_ratio_threshold: 0.8,
        }
    }
}

impl Default for CachePersistenceStrategy {
    fn default() -> Self {
        CachePersistenceStrategy::None
    }
}

impl Default for CachePersistenceConfig {
    fn default() -> Self {
        Self {
            enabled: false,
            strategy: CachePersistenceStrategy::default(),
            persistence_interval: 300,
            file_path: None,
            max_file_size_mb: 100,
            file_retention_count: 10,
        }
    }
}

impl Default for CacheSynchronizationStrategy {
    fn default() -> Self {
        CacheSynchronizationStrategy::None
    }
}

impl Default for CacheConflictResolution {
    fn default() -> Self {
        CacheConflictResolution::LastWriteWins
    }
}

impl Default for CacheSynchronizationConfig {
    fn default() -> Self {
        Self {
            enabled: false,
            strategy: CacheSynchronizationStrategy::default(),
            sync_interval: 60,
            conflict_resolution: CacheConflictResolution::default(),
            sync_timeout: 30,
            max_retries: 3,
        }
    }
}





