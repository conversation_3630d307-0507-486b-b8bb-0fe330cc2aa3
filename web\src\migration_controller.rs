//! 迁移控制器（Web模块版本）
//!
//! 负责控制新旧系统之间的流量切换和功能迁移

use std::sync::Arc;
use std::sync::atomic::{AtomicU8, AtomicBool, Ordering};
use tokio::sync::RwLock;
use tracing::{info, warn, error};
use serde::{Deserialize, Serialize};

use sigmax_core::{Order, SigmaXResult, SigmaXError};
use sigmax_risk_new::{RiskEngine as NewRiskEngine, RiskContext, RiskCheckResult};

/// 迁移配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MigrationConfig {
    /// 是否启用新引擎
    pub enable_new_engine: bool,
    /// 是否启用双写模式
    pub enable_dual_write: bool,
    /// 是否启用结果对比
    pub enable_result_comparison: bool,
    /// 新引擎流量比例 (0-100)
    pub new_engine_traffic_percentage: u8,
    /// 自动回滚错误率阈值
    pub auto_rollback_error_rate: f64,
    /// 自动回滚响应时间阈值（毫秒）
    pub auto_rollback_response_time_ms: u64,
}

impl Default for MigrationConfig {
    fn default() -> Self {
        Self {
            enable_new_engine: false,
            enable_dual_write: true,
            enable_result_comparison: true,
            new_engine_traffic_percentage: 0,
            auto_rollback_error_rate: 5.0,
            auto_rollback_response_time_ms: 1000,
        }
    }
}

/// 迁移统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MigrationStats {
    /// 总请求数
    pub total_requests: u64,
    /// 新引擎处理的请求数
    pub new_engine_requests: u64,
    /// 旧引擎处理的请求数
    pub old_engine_requests: u64,
    /// 结果一致的请求数
    pub consistent_results: u64,
    /// 结果不一致的请求数
    pub inconsistent_results: u64,
    /// 新引擎错误数
    pub new_engine_errors: u64,
    /// 旧引擎错误数
    pub old_engine_errors: u64,
    /// 平均响应时间（新引擎）
    pub new_engine_avg_response_time: f64,
    /// 平均响应时间（旧引擎）
    pub old_engine_avg_response_time: f64,
}

impl Default for MigrationStats {
    fn default() -> Self {
        Self {
            total_requests: 0,
            new_engine_requests: 0,
            old_engine_requests: 0,
            consistent_results: 0,
            inconsistent_results: 0,
            new_engine_errors: 0,
            old_engine_errors: 0,
            new_engine_avg_response_time: 0.0,
            old_engine_avg_response_time: 0.0,
        }
    }
}

/// 迁移控制器
pub struct MigrationController {
    /// 新的风险引擎
    new_engine: Arc<dyn NewRiskEngine>,
    /// 迁移配置
    config: Arc<RwLock<MigrationConfig>>,
    /// 统计信息
    stats: Arc<RwLock<MigrationStats>>,
    /// 流量分配计数器
    traffic_counter: AtomicU8,
    /// 是否启用自动回滚
    auto_rollback_enabled: AtomicBool,
}

impl MigrationController {
    /// 创建新的迁移控制器
    pub fn new(
        new_engine: Arc<dyn NewRiskEngine>,
        config: Option<MigrationConfig>,
    ) -> Self {
        Self {
            new_engine,
            config: Arc::new(RwLock::new(config.unwrap_or_default())),
            stats: Arc::new(RwLock::new(MigrationStats::default())),
            traffic_counter: AtomicU8::new(0),
            auto_rollback_enabled: AtomicBool::new(true),
        }
    }

    /// 检查订单风险（智能路由）
    pub async fn check_order_risk(
        &self,
        order: &Order,
        context: &RiskContext,
    ) -> SigmaXResult<RiskCheckResult> {
        let config = self.config.read().await;
        
        // 更新总请求数
        {
            let mut stats = self.stats.write().await;
            stats.total_requests += 1;
        }

        if !config.enable_new_engine {
            // 只使用旧引擎（模拟）
            return self.simulate_old_engine_check(order, context).await;
        }

        if config.enable_dual_write {
            // 双写模式：同时调用新旧引擎
            self.check_with_dual_write(order, context).await
        } else {
            // 根据流量比例路由
            if self.should_use_new_engine(config.new_engine_traffic_percentage) {
                self.check_with_new_engine(order, context).await
            } else {
                self.simulate_old_engine_check(order, context).await
            }
        }
    }

    /// 模拟旧引擎检查
    async fn simulate_old_engine_check(
        &self,
        _order: &Order,
        _context: &RiskContext,
    ) -> SigmaXResult<RiskCheckResult> {
        let start_time = std::time::Instant::now();
        
        // 模拟旧引擎的处理时间
        tokio::time::sleep(tokio::time::Duration::from_millis(20)).await;
        
        let duration = start_time.elapsed().as_millis() as u64;

        // 更新统计信息
        {
            let mut stats = self.stats.write().await;
            stats.old_engine_requests += 1;
            stats.old_engine_avg_response_time = 
                (stats.old_engine_avg_response_time * (stats.old_engine_requests - 1) as f64 + duration as f64) 
                / stats.old_engine_requests as f64;
        }

        // 模拟旧引擎的结果
        Ok(RiskCheckResult {
            passed: true, // 简化：总是通过
            risk_level: sigmax_risk_new::RiskLevel::Low,
            risk_score: 0.1,
            check_type: sigmax_risk_new::RiskCheckType::Order,
            message: Some("旧引擎检查通过".to_string()),
            warnings: vec![],
            suggestions: vec![],
            violated_rules: vec![],
            timestamp: chrono::Utc::now(),
            execution_time_ms: duration,
        })
    }

    /// 使用新引擎检查
    async fn check_with_new_engine(
        &self,
        order: &Order,
        context: &RiskContext,
    ) -> SigmaXResult<RiskCheckResult> {
        let start_time = std::time::Instant::now();
        
        let result = self.new_engine.check_order_risk(order, context).await;
        
        let duration = start_time.elapsed().as_millis() as u64;

        // 更新统计信息
        {
            let mut stats = self.stats.write().await;
            stats.new_engine_requests += 1;
            
            match &result {
                Ok(_) => {
                    stats.new_engine_avg_response_time = 
                        (stats.new_engine_avg_response_time * (stats.new_engine_requests - 1) as f64 + duration as f64) 
                        / stats.new_engine_requests as f64;
                }
                Err(_) => {
                    stats.new_engine_errors += 1;
                }
            }
        }

        result
    }

    /// 双写模式检查
    async fn check_with_dual_write(
        &self,
        order: &Order,
        context: &RiskContext,
    ) -> SigmaXResult<RiskCheckResult> {
        let config = self.config.read().await;
        
        // 并行执行新旧引擎
        let (new_result, old_result) = tokio::join!(
            self.check_with_new_engine(order, context),
            self.simulate_old_engine_check(order, context)
        );

        // 比较结果一致性
        if config.enable_result_comparison {
            self.compare_results(&new_result, &old_result, order).await;
        }

        // 优先返回新引擎的结果，如果失败则返回旧引擎的结果
        match new_result {
            Ok(result) => Ok(result),
            Err(_) => old_result,
        }
    }

    /// 比较新旧引擎的结果
    async fn compare_results(
        &self,
        new_result: &SigmaXResult<RiskCheckResult>,
        old_result: &SigmaXResult<RiskCheckResult>,
        order: &Order,
    ) {
        let consistent = match (new_result, old_result) {
            (Ok(new), Ok(old)) => new.passed == old.passed,
            (Err(_), Err(_)) => true,
            _ => false,
        };

        // 更新一致性统计
        {
            let mut stats = self.stats.write().await;
            if consistent {
                stats.consistent_results += 1;
            } else {
                stats.inconsistent_results += 1;
                
                warn!(
                    "新旧引擎结果不一致: 订单={}, 新引擎={:?}, 旧引擎={:?}",
                    order.id,
                    new_result.as_ref().map(|r| r.passed),
                    old_result.as_ref().map(|r| r.passed)
                );
            }
        }
    }

    /// 判断是否应该使用新引擎
    fn should_use_new_engine(&self, percentage: u8) -> bool {
        if percentage == 0 {
            return false;
        }
        if percentage >= 100 {
            return true;
        }

        let counter = self.traffic_counter.fetch_add(1, Ordering::Relaxed);
        (counter % 100) < percentage
    }

    /// 更新迁移配置
    pub async fn update_config(&self, new_config: MigrationConfig) {
        let mut config = self.config.write().await;
        *config = new_config;
        info!("迁移配置已更新: {:?}", *config);
    }

    /// 获取迁移统计信息
    pub async fn get_stats(&self) -> MigrationStats {
        self.stats.read().await.clone()
    }

    /// 重置统计信息
    pub async fn reset_stats(&self) {
        let mut stats = self.stats.write().await;
        *stats = MigrationStats::default();
        info!("迁移统计信息已重置");
    }

    /// 获取当前配置
    pub async fn get_config(&self) -> MigrationConfig {
        self.config.read().await.clone()
    }
}
