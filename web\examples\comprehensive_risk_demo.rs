//! 综合风险指标计算演示
//! 
//! 展示完整的风险度量指标体系

use sigmax_core::{Amount, SigmaXResult};
use sigmax_risk::metrics::MetricsCalculator;
use rust_decimal::Decimal;
use rust_decimal_macros::dec;

#[tokio::main]
async fn main() -> SigmaXResult<()> {
    // 初始化日志
    tracing_subscriber::fmt::init();

    println!("🚀 SigmaX综合风险指标计算系统演示");
    println!("{}", "=".repeat(60));

    // 1. 基础风险指标演示
    demo_basic_risk_metrics().await?;

    // 2. 收益风险比率演示
    demo_return_risk_ratios().await?;

    // 3. 波动率指标演示
    demo_volatility_metrics().await?;

    // 4. 回撤指标演示
    demo_drawdown_metrics().await?;

    // 5. 市场相关指标演示
    demo_market_metrics().await?;

    // 6. 高级风险指标演示
    demo_advanced_metrics().await?;

    println!("\n🎉 综合风险指标计算系统演示完成！");
    println!("✅ 所有风险指标计算正常");

    Ok(())
}

/// 演示基础风险指标
async fn demo_basic_risk_metrics() -> SigmaXResult<()> {
    println!("\n📊 1. 基础风险指标演示");
    println!("{}", "-".repeat(40));

    let mut calculator = MetricsCalculator::new(252);

    // 模拟投资组合数据
    let initial_value = dec!(1000000);
    let mut current_value = initial_value;

    // 模拟收益率序列（包含不同市场情况）
    let returns = vec![
        dec!(0.02), dec!(-0.01), dec!(0.015), dec!(-0.005), dec!(0.01),
        dec!(-0.03), dec!(0.025), dec!(-0.015), dec!(0.008), dec!(-0.002),
        dec!(0.012), dec!(-0.008), dec!(0.018), dec!(-0.012), dec!(0.005),
        dec!(-0.025), dec!(0.03), dec!(-0.018), dec!(0.015), dec!(-0.01),
        dec!(0.022), dec!(-0.015), dec!(0.01), dec!(-0.005), dec!(0.008),
        dec!(-0.02), dec!(0.028), dec!(-0.012), dec!(0.018), dec!(-0.008),
        dec!(0.015), dec!(-0.01), dec!(0.025), dec!(-0.02), dec!(0.012),
        dec!(-0.015), dec!(0.02), dec!(-0.008), dec!(0.01), dec!(-0.005),
    ];

    // 模拟基准收益率
    let benchmark_returns = vec![
        dec!(0.015), dec!(-0.008), dec!(0.012), dec!(-0.003), dec!(0.008),
        dec!(-0.025), dec!(0.02), dec!(-0.012), dec!(0.006), dec!(-0.001),
        dec!(0.01), dec!(-0.006), dec!(0.015), dec!(-0.01), dec!(0.004),
        dec!(-0.02), dec!(0.025), dec!(-0.015), dec!(0.012), dec!(-0.008),
        dec!(0.018), dec!(-0.012), dec!(0.008), dec!(-0.004), dec!(0.006),
        dec!(-0.015), dec!(0.022), dec!(-0.01), dec!(0.015), dec!(-0.006),
        dec!(0.012), dec!(-0.008), dec!(0.02), dec!(-0.015), dec!(0.01),
        dec!(-0.012), dec!(0.015), dec!(-0.006), dec!(0.008), dec!(-0.004),
    ];

    println!("📈 处理 {} 个交易日数据", returns.len());

    for (i, (&portfolio_return, &benchmark_return)) in returns.iter().zip(benchmark_returns.iter()).enumerate() {
        // 更新投资组合价值
        current_value = current_value * (Decimal::ONE + portfolio_return);
        
        // 更新计算器数据
        calculator.update_returns(portfolio_return, Some(benchmark_return));
        calculator.update_portfolio_value(current_value);

        // 每10天显示一次进度
        if (i + 1) % 10 == 0 {
            println!("   第 {} 天: 投资组合价值 ${:.2}", i + 1, current_value);
        }
    }

    // 计算综合风险指标
    let metrics = calculator.calculate_comprehensive_metrics()?;

    println!("\n📊 基础风险指标结果:");
    println!("   VaR 95%: ${:.2}", metrics.var_95);
    println!("   VaR 99%: ${:.2}", metrics.var_99);
    println!("   CVaR 95%: ${:.2}", metrics.cvar_95);
    println!("   CVaR 99%: ${:.2}", metrics.cvar_99);
    println!("   最大回撤: {:.2}%", metrics.max_drawdown * dec!(100));
    println!("   当前回撤: {:.2}%", metrics.current_drawdown * dec!(100));

    println!("\n✅ 基础风险指标计算完成");
    Ok(())
}

/// 演示收益风险比率
async fn demo_return_risk_ratios() -> SigmaXResult<()> {
    println!("\n📈 2. 收益风险比率演示");
    println!("{}", "-".repeat(40));

    let mut calculator = MetricsCalculator::new(252);

    // 模拟高质量策略的收益率数据
    let high_quality_returns = vec![
        dec!(0.008), dec!(0.012), dec!(-0.005), dec!(0.015), dec!(0.01),
        dec!(-0.008), dec!(0.018), dec!(0.006), dec!(-0.003), dec!(0.012),
        dec!(0.015), dec!(-0.01), dec!(0.02), dec!(0.008), dec!(-0.005),
        dec!(0.012), dec!(0.018), dec!(-0.008), dec!(0.015), dec!(0.01),
        dec!(-0.006), dec!(0.022), dec!(0.012), dec!(-0.01), dec!(0.015),
        dec!(0.008), dec!(-0.005), dec!(0.018), dec!(0.012), dec!(-0.008),
        dec!(0.015), dec!(0.01), dec!(-0.006), dec!(0.02), dec!(0.008),
        dec!(-0.01), dec!(0.015), dec!(0.012), dec!(-0.005), dec!(0.018),
    ];

    let benchmark_returns = vec![
        dec!(0.005), dec!(0.008), dec!(-0.003), dec!(0.01), dec!(0.006),
        dec!(-0.005), dec!(0.012), dec!(0.004), dec!(-0.002), dec!(0.008),
        dec!(0.01), dec!(-0.006), dec!(0.015), dec!(0.005), dec!(-0.003),
        dec!(0.008), dec!(0.012), dec!(-0.005), dec!(0.01), dec!(0.006),
        dec!(-0.004), dec!(0.015), dec!(0.008), dec!(-0.006), dec!(0.01),
        dec!(0.005), dec!(-0.003), dec!(0.012), dec!(0.008), dec!(-0.005),
        dec!(0.01), dec!(0.006), dec!(-0.004), dec!(0.015), dec!(0.005),
        dec!(-0.006), dec!(0.01), dec!(0.008), dec!(-0.003), dec!(0.012),
    ];

    let mut portfolio_value = dec!(1000000);

    for (&portfolio_return, &benchmark_return) in high_quality_returns.iter().zip(benchmark_returns.iter()) {
        portfolio_value = portfolio_value * (Decimal::ONE + portfolio_return);
        calculator.update_returns(portfolio_return, Some(benchmark_return));
        calculator.update_portfolio_value(portfolio_value);
    }

    let metrics = calculator.calculate_comprehensive_metrics()?;

    println!("📊 收益风险比率结果:");
    println!("   夏普比率: {:.3}", metrics.sharpe_ratio);
    println!("   索提诺比率: {:.3}", metrics.sortino_ratio);
    println!("   卡尔马比率: {:.3}", metrics.calmar_ratio);
    println!("   信息比率: {:.3}", metrics.information_ratio);
    println!("   特雷诺比率: {:.3}", metrics.treynor_ratio);

    println!("\n💡 比率解读:");
    if metrics.sharpe_ratio > dec!(1.0) {
        println!("   ✅ 夏普比率 > 1.0，风险调整收益优秀");
    } else if metrics.sharpe_ratio > dec!(0.5) {
        println!("   ⚠️ 夏普比率 > 0.5，风险调整收益良好");
    } else {
        println!("   ❌ 夏普比率 < 0.5，风险调整收益较差");
    }

    println!("\n✅ 收益风险比率计算完成");
    Ok(())
}

/// 演示波动率指标
async fn demo_volatility_metrics() -> SigmaXResult<()> {
    println!("\n📊 3. 波动率指标演示");
    println!("{}", "-".repeat(40));

    let mut calculator = MetricsCalculator::new(252);

    // 模拟不同波动率特征的收益率
    let volatile_returns = vec![
        dec!(0.03), dec!(-0.025), dec!(0.04), dec!(-0.035), dec!(0.02),
        dec!(-0.03), dec!(0.045), dec!(-0.02), dec!(0.025), dec!(-0.04),
        dec!(0.035), dec!(-0.025), dec!(0.03), dec!(-0.045), dec!(0.02),
        dec!(-0.035), dec!(0.04), dec!(-0.02), dec!(0.025), dec!(-0.03),
        dec!(0.045), dec!(-0.035), dec!(0.02), dec!(-0.025), dec!(0.03),
        dec!(-0.04), dec!(0.035), dec!(-0.02), dec!(0.025), dec!(-0.045),
        dec!(0.03), dec!(-0.025), dec!(0.04), dec!(-0.035), dec!(0.02),
        dec!(-0.03), dec!(0.045), dec!(-0.02), dec!(0.025), dec!(-0.04),
    ];

    let mut portfolio_value = dec!(1000000);

    for &portfolio_return in volatile_returns.iter() {
        portfolio_value = portfolio_value * (Decimal::ONE + portfolio_return);
        calculator.update_returns(portfolio_return, None);
        calculator.update_portfolio_value(portfolio_value);
    }

    let metrics = calculator.calculate_comprehensive_metrics()?;

    println!("📊 波动率指标结果:");
    println!("   总波动率: {:.3}%", metrics.volatility * dec!(100));
    println!("   下行波动率: {:.3}%", metrics.downside_volatility * dec!(100));
    println!("   上行波动率: {:.3}%", metrics.upside_volatility * dec!(100));
    println!("   波动率偏度: {:.3}", metrics.volatility_skew);

    println!("\n💡 波动率分析:");
    if metrics.volatility > dec!(0.2) {
        println!("   ⚠️ 高波动率策略，风险较大");
    } else if metrics.volatility > dec!(0.1) {
        println!("   📊 中等波动率策略，风险适中");
    } else {
        println!("   ✅ 低波动率策略，风险较小");
    }

    if metrics.volatility_skew > dec!(1.0) {
        println!("   📈 上行波动率大于下行波动率，收益分布右偏");
    } else {
        println!("   📉 下行波动率大于上行波动率，收益分布左偏");
    }

    println!("\n✅ 波动率指标计算完成");
    Ok(())
}

/// 演示回撤指标
async fn demo_drawdown_metrics() -> SigmaXResult<()> {
    println!("\n📉 4. 回撤指标演示");
    println!("{}", "-".repeat(40));

    let mut calculator = MetricsCalculator::new(252);

    // 模拟包含显著回撤的收益率序列
    let drawdown_returns = vec![
        dec!(0.02), dec!(0.015), dec!(0.01), dec!(0.008), dec!(0.012),  // 上涨期
        dec!(-0.05), dec!(-0.03), dec!(-0.025), dec!(-0.02), dec!(-0.015), // 回撤期
        dec!(-0.01), dec!(-0.008), dec!(0.005), dec!(0.01), dec!(0.015),   // 恢复期
        dec!(0.02), dec!(0.025), dec!(0.018), dec!(0.012), dec!(0.015),    // 新高期
        dec!(-0.04), dec!(-0.025), dec!(-0.015), dec!(-0.01), dec!(0.005), // 第二次回撤
        dec!(0.01), dec!(0.015), dec!(0.02), dec!(0.025), dec!(0.018),     // 恢复期
        dec!(0.012), dec!(0.015), dec!(0.01), dec!(0.008), dec!(0.012),    // 稳定期
        dec!(-0.02), dec!(-0.015), dec!(-0.01), dec!(0.005), dec!(0.01),   // 小幅回撤
    ];

    let mut portfolio_value = dec!(1000000);

    for &portfolio_return in drawdown_returns.iter() {
        portfolio_value = portfolio_value * (Decimal::ONE + portfolio_return);
        calculator.update_returns(portfolio_return, None);
        calculator.update_portfolio_value(portfolio_value);
    }

    let metrics = calculator.calculate_comprehensive_metrics()?;

    println!("📊 回撤指标结果:");
    println!("   最大回撤: {:.2}%", metrics.max_drawdown * dec!(100));
    println!("   当前回撤: {:.2}%", metrics.current_drawdown * dec!(100));
    println!("   最大回撤持续期: {} 天", metrics.max_drawdown_duration);
    println!("   平均回撤: {:.2}%", metrics.average_drawdown * dec!(100));
    println!("   恢复因子: {:.3}", metrics.recovery_factor);
    println!("   溃疡指数: {:.3}", metrics.ulcer_index);

    println!("\n💡 回撤分析:");
    if metrics.max_drawdown > dec!(0.2) {
        println!("   ❌ 最大回撤 > 20%，风险控制需要改进");
    } else if metrics.max_drawdown > dec!(0.1) {
        println!("   ⚠️ 最大回撤 > 10%，风险适中");
    } else {
        println!("   ✅ 最大回撤 < 10%，风险控制良好");
    }

    if metrics.recovery_factor > dec!(2.0) {
        println!("   ✅ 恢复因子 > 2.0，恢复能力强");
    } else {
        println!("   ⚠️ 恢复因子 < 2.0，恢复能力一般");
    }

    println!("\n✅ 回撤指标计算完成");
    Ok(())
}

/// 演示市场相关指标
async fn demo_market_metrics() -> SigmaXResult<()> {
    println!("\n📈 5. 市场相关指标演示");
    println!("{}", "-".repeat(40));

    let mut calculator = MetricsCalculator::new(252);

    // 模拟与市场相关的收益率数据
    let portfolio_returns = vec![
        dec!(0.015), dec!(-0.01), dec!(0.02), dec!(-0.008), dec!(0.012),
        dec!(-0.015), dec!(0.025), dec!(-0.012), dec!(0.018), dec!(-0.006),
        dec!(0.01), dec!(-0.008), dec!(0.022), dec!(-0.015), dec!(0.008),
        dec!(-0.01), dec!(0.018), dec!(-0.012), dec!(0.015), dec!(-0.005),
        dec!(0.012), dec!(-0.008), dec!(0.02), dec!(-0.015), dec!(0.01),
        dec!(-0.012), dec!(0.025), dec!(-0.01), dec!(0.015), dec!(-0.008),
        dec!(0.018), dec!(-0.012), dec!(0.01), dec!(-0.005), dec!(0.015),
        dec!(-0.01), dec!(0.02), dec!(-0.008), dec!(0.012), dec!(-0.006),
    ];

    // 市场基准收益率（相关性约0.7）
    let market_returns = vec![
        dec!(0.01), dec!(-0.008), dec!(0.015), dec!(-0.005), dec!(0.008),
        dec!(-0.01), dec!(0.018), dec!(-0.008), dec!(0.012), dec!(-0.004),
        dec!(0.006), dec!(-0.005), dec!(0.015), dec!(-0.01), dec!(0.005),
        dec!(-0.006), dec!(0.012), dec!(-0.008), dec!(0.01), dec!(-0.003),
        dec!(0.008), dec!(-0.005), dec!(0.015), dec!(-0.01), dec!(0.006),
        dec!(-0.008), dec!(0.018), dec!(-0.006), dec!(0.01), dec!(-0.005),
        dec!(0.012), dec!(-0.008), dec!(0.006), dec!(-0.003), dec!(0.01),
        dec!(-0.006), dec!(0.015), dec!(-0.005), dec!(0.008), dec!(-0.004),
    ];

    let mut portfolio_value = dec!(1000000);

    for (&portfolio_return, &market_return) in portfolio_returns.iter().zip(market_returns.iter()) {
        portfolio_value = portfolio_value * (Decimal::ONE + portfolio_return);
        calculator.update_returns(portfolio_return, Some(market_return));
        calculator.update_portfolio_value(portfolio_value);
    }

    let metrics = calculator.calculate_comprehensive_metrics()?;

    println!("📊 市场相关指标结果:");
    println!("   贝塔系数: {:.3}", metrics.beta);
    println!("   阿尔法: {:.3}%", metrics.alpha * dec!(100));
    println!("   跟踪误差: {:.3}%", metrics.tracking_error * dec!(100));
    println!("   相关性: {:.3}", metrics.correlation);

    println!("\n💡 市场关系分析:");
    if metrics.beta > dec!(1.2) {
        println!("   📈 高贝塔策略，市场敏感性强");
    } else if metrics.beta > dec!(0.8) {
        println!("   📊 中等贝塔策略，与市场同步");
    } else {
        println!("   📉 低贝塔策略，市场敏感性低");
    }

    if metrics.alpha > dec!(0.05) {
        println!("   ✅ 正阿尔法 > 5%，超额收益显著");
    } else if metrics.alpha > Decimal::ZERO {
        println!("   📊 正阿尔法，有超额收益");
    } else {
        println!("   ❌ 负阿尔法，未能跑赢基准");
    }

    println!("\n✅ 市场相关指标计算完成");
    Ok(())
}

/// 演示高级风险指标
async fn demo_advanced_metrics() -> SigmaXResult<()> {
    println!("\n🔬 6. 高级风险指标演示");
    println!("{}", "-".repeat(40));

    let mut calculator = MetricsCalculator::new(252);

    // 模拟复杂的收益率分布
    let complex_returns = vec![
        dec!(0.025), dec!(-0.015), dec!(0.035), dec!(-0.02), dec!(0.01),
        dec!(-0.03), dec!(0.04), dec!(-0.025), dec!(0.015), dec!(-0.01),
        dec!(0.02), dec!(-0.035), dec!(0.03), dec!(-0.015), dec!(0.008),
        dec!(-0.025), dec!(0.045), dec!(-0.02), dec!(0.012), dec!(-0.008),
        dec!(0.018), dec!(-0.03), dec!(0.025), dec!(-0.015), dec!(0.01),
        dec!(-0.02), dec!(0.035), dec!(-0.025), dec!(0.015), dec!(-0.012),
        dec!(0.022), dec!(-0.018), dec!(0.03), dec!(-0.025), dec!(0.008),
        dec!(-0.015), dec!(0.028), dec!(-0.02), dec!(0.012), dec!(-0.01),
    ];

    let mut portfolio_value = dec!(1000000);

    for &portfolio_return in complex_returns.iter() {
        portfolio_value = portfolio_value * (Decimal::ONE + portfolio_return);
        calculator.update_returns(portfolio_return, None);
        calculator.update_portfolio_value(portfolio_value);
    }

    let metrics = calculator.calculate_comprehensive_metrics()?;

    println!("📊 高级风险指标结果:");
    println!("   尾部比率: {:.3}", metrics.tail_ratio);
    println!("   收益痛苦比率: {:.3}", metrics.gain_pain_ratio);
    println!("   斯特林比率: {:.3}", metrics.sterling_ratio);
    println!("   伯克比率: {:.3}", metrics.burke_ratio);
    println!("   欧米茄比率: {:.3}", metrics.omega_ratio);
    println!("   卡帕比率: {:.3}", metrics.kappa_ratio);
    println!("   马丁比率: {:.3}", metrics.martin_ratio);
    println!("   痛苦指数: {:.3}%", metrics.pain_index * dec!(100));

    println!("\n💡 高级指标解读:");
    if metrics.omega_ratio > dec!(1.5) {
        println!("   ✅ 欧米茄比率 > 1.5，收益质量优秀");
    } else if metrics.omega_ratio > dec!(1.0) {
        println!("   📊 欧米茄比率 > 1.0，收益质量良好");
    } else {
        println!("   ❌ 欧米茄比率 < 1.0，收益质量较差");
    }

    if metrics.tail_ratio > dec!(1.0) {
        println!("   📈 尾部比率 > 1.0，极端收益偏向正面");
    } else {
        println!("   📉 尾部比率 < 1.0，极端收益偏向负面");
    }

    println!("\n✅ 高级风险指标计算完成");
    Ok(())
}
