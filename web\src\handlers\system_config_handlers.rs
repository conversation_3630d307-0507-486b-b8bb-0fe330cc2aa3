//! 系统配置API处理器
//!
//! 专门负责系统级基础设施配置管理
//! 职责范围：数据库、缓存、监控、API等系统级配置
//!
//! 不包括：交易、风险、策略、通知等业务级配置（由专门处理器负责）

use axum::{
    extract::State,
    response::Json,
};
use tracing::info;
use chrono::Utc;

use crate::{
    state::AppState,
    error::{ApiError, ApiResult, ApiResponse},
};
use sigmax_core::{
    SystemGeneralConfig, ApiConfig, CacheConfig, MonitoringConfig
};



// ============================================================================
// 系统级配置管理 API
// ============================================================================




/// 获取系统配置
/// GET /api/v1/config/system
pub async fn get_system_config(
    State(state): State<AppState>,
) -> ApiResult<Json<ApiResponse<SystemGeneralConfig>>> {
    info!("Getting system config");

    let config = state.config_manager.system_config.get_config().await
        .map_err(|e| ApiError::Internal(e.to_string()))?;
    let response = ApiResponse::success(config, "获取系统配置成功");
    Ok(Json(response))
}

/// 保存系统配置
/// PUT /api/v1/config/system
pub async fn update_system_config(
    State(state): State<AppState>,
    Json(config): Json<SystemGeneralConfig>,
) -> ApiResult<Json<ApiResponse<()>>> {
    info!("Updating system config");

    state.config_manager.system_config.save_config(&config).await
        .map_err(|e| ApiError::Internal(e.to_string()))?;
    let response = ApiResponse::success((), "系统配置更新成功");
    Ok(Json(response))
}

/// 获取API配置
/// GET /api/v1/config/api
pub async fn get_api_config(
    State(state): State<AppState>,
) -> ApiResult<Json<ApiResponse<ApiConfig>>> {
    info!("Getting API config");

    let config = state.config_manager.api_config.get_config().await
        .map_err(|e| ApiError::Internal(e.to_string()))?;
    let response = ApiResponse::success(config, "获取API配置成功");
    Ok(Json(response))
}

/// 保存API配置
/// PUT /api/v1/config/api
pub async fn update_api_config(
    State(state): State<AppState>,
    Json(config): Json<ApiConfig>,
) -> ApiResult<Json<ApiResponse<()>>> {
    info!("Updating API config");

    state.config_manager.api_config.save_config(&config).await
        .map_err(|e| ApiError::Internal(e.to_string()))?;
    let response = ApiResponse::success((), "API配置更新成功");
    Ok(Json(response))
}



/// 获取缓存配置
/// GET /api/v1/config/cache
pub async fn get_cache_config(
    State(state): State<AppState>,
) -> ApiResult<Json<ApiResponse<CacheConfig>>> {
    info!("Getting cache config");

    let config = state.config_manager.cache_config.get_config().await
        .map_err(|e| ApiError::Internal(e.to_string()))?;
    let response = ApiResponse::success(config, "获取缓存配置成功");
    Ok(Json(response))
}

/// 保存缓存配置
/// PUT /api/v1/config/cache
pub async fn update_cache_config(
    State(state): State<AppState>,
    Json(config): Json<CacheConfig>,
) -> ApiResult<Json<ApiResponse<()>>> {
    info!("Updating cache config");

    state.config_manager.cache_config.save_config(&config).await
        .map_err(|e| ApiError::Internal(e.to_string()))?;
    let response = ApiResponse::success((), "缓存配置更新成功");
    Ok(Json(response))
}

/// 获取监控配置
/// GET /api/v1/config/monitoring
pub async fn get_monitoring_config(
    State(state): State<AppState>,
) -> ApiResult<Json<ApiResponse<MonitoringConfig>>> {
    info!("Getting monitoring config");

    let config = state.config_manager.monitoring_config.get_config().await
        .map_err(|e| ApiError::Internal(e.to_string()))?;
    let response = ApiResponse::success(config, "获取监控配置成功");
    Ok(Json(response))
}

/// 保存监控配置
/// PUT /api/v1/config/monitoring
pub async fn update_monitoring_config(
    State(state): State<AppState>,
    Json(config): Json<MonitoringConfig>,
) -> ApiResult<Json<ApiResponse<()>>> {
    info!("Updating monitoring config");

    state.config_manager.monitoring_config.save_config(&config).await
        .map_err(|e| ApiError::Internal(e.to_string()))?;
    let response = ApiResponse::success((), "监控配置更新成功");
    Ok(Json(response))
}



