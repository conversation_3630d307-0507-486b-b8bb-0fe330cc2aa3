use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc};

use rust_decimal::Decimal;

/// 实时事件类型
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type", content = "data")]
pub enum RealtimeEventType {
    // 订单事件
    OrderCreated(OrderEvent),
    OrderUpdated(OrderEvent),
    OrderFilled(OrderEvent),
    OrderCancelled(OrderEvent),
    OrderRejected(OrderEvent),

    // 交易事件
    TradeExecuted(TradeEvent),
    TradeSettled(TradeEvent),
    TradeFailed(TradeEvent),

    // 策略事件
    StrategyStarted(StrategyEvent),
    StrategyStopped(StrategyEvent),
    StrategyPaused(StrategyEvent),
    StrategyResumed(StrategyEvent),
    StrategyError(StrategyEvent),
    StrategyPerformanceUpdate(PerformanceEvent),

    // 风险事件
    RiskAlert(RiskAlertEvent),
    RiskThresholdTriggered(RiskThresholdEvent),
    EmergencyStop(EmergencyEvent),
    AutoResponse(AutoResponseEvent),

    // 投资组合事件
    BalanceUpdated(BalanceEvent),
    PositionChanged(PositionEvent),
    PnlUpdated(PnlEvent),
    ExposureChanged(ExposureEvent),

    // 市场数据事件
    PriceUpdate(PriceEvent),
    VolumeUpdate(VolumeEvent),
    OrderbookUpdate(OrderbookEvent),
    TickerUpdate(TickerEvent),

    // 系统事件
    SystemStatusUpdate(SystemStatusEvent),
    PerformanceMetrics(MetricsEvent),
    EngineStatusUpdate(EngineStatusEvent),
    ConnectionStatusUpdate(ConnectionStatusEvent),

    // 回测事件
    BacktestStarted(BacktestEvent),
    BacktestProgressUpdate(BacktestProgressEvent),
    BacktestCompleted(BacktestEvent),

    // 报告事件
    ReportGenerating(ReportEvent),
    ReportCompleted(ReportEvent),
    ReportFailed(ReportEvent),
}

/// 订单事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrderEvent {
    pub order_id: String,
    pub strategy_id: Option<String>,
    pub symbol: String,
    pub side: String,
    pub order_type: String,
    pub quantity: Decimal,
    pub price: Option<Decimal>,
    pub status: String,
    pub filled_quantity: Decimal,
    pub remaining_quantity: Decimal,
    pub average_price: Option<Decimal>,
    pub timestamp: DateTime<Utc>,
    pub reason: Option<String>,
}

/// 交易事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TradeEvent {
    pub trade_id: String,
    pub order_id: String,
    pub strategy_id: Option<String>,
    pub symbol: String,
    pub side: String,
    pub quantity: Decimal,
    pub price: Decimal,
    pub fee: Decimal,
    pub fee_asset: String,
    pub timestamp: DateTime<Utc>,
}

/// 策略事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StrategyEvent {
    pub strategy_id: String,
    pub strategy_name: String,
    pub status: String,
    pub timestamp: DateTime<Utc>,
    pub reason: Option<String>,
    pub metadata: Option<serde_json::Value>,
}

/// 性能事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceEvent {
    pub strategy_id: String,
    pub total_return: Decimal,
    pub daily_return: Decimal,
    pub sharpe_ratio: Option<Decimal>,
    pub max_drawdown: Decimal,
    pub win_rate: Decimal,
    pub profit_factor: Option<Decimal>,
    pub total_trades: u64,
    pub timestamp: DateTime<Utc>,
}

/// 风险告警事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskAlertEvent {
    pub alert_id: String,
    pub alert_type: String,
    pub severity: String,
    pub message: String,
    pub strategy_id: Option<String>,
    pub risk_metric: String,
    pub current_value: Decimal,
    pub threshold: Decimal,
    pub timestamp: DateTime<Utc>,
}

/// 风险阈值事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskThresholdEvent {
    pub threshold_id: String,
    pub metric_name: String,
    pub current_value: Decimal,
    pub threshold_value: Decimal,
    pub breach_type: String, // "above" | "below"
    pub strategy_id: Option<String>,
    pub timestamp: DateTime<Utc>,
}

/// 紧急停止事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmergencyEvent {
    pub event_id: String,
    pub trigger_reason: String,
    pub affected_strategies: Vec<String>,
    pub action_taken: String,
    pub timestamp: DateTime<Utc>,
}

/// 自动响应事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AutoResponseEvent {
    pub response_id: String,
    pub trigger_event: String,
    pub response_action: String,
    pub strategy_id: Option<String>,
    pub success: bool,
    pub details: String,
    pub timestamp: DateTime<Utc>,
}

/// 余额事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BalanceEvent {
    pub asset: String,
    pub available_balance: Decimal,
    pub locked_balance: Decimal,
    pub total_balance: Decimal,
    pub change_amount: Decimal,
    pub change_reason: String,
    pub timestamp: DateTime<Utc>,
}

/// 持仓事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PositionEvent {
    pub symbol: String,
    pub side: String, // "long" | "short"
    pub size: Decimal,
    pub entry_price: Decimal,
    pub current_price: Decimal,
    pub unrealized_pnl: Decimal,
    pub realized_pnl: Decimal,
    pub timestamp: DateTime<Utc>,
}

/// 盈亏事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PnlEvent {
    pub strategy_id: Option<String>,
    pub symbol: Option<String>,
    pub unrealized_pnl: Decimal,
    pub realized_pnl: Decimal,
    pub total_pnl: Decimal,
    pub pnl_change: Decimal,
    pub timestamp: DateTime<Utc>,
}

/// 风险敞口事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExposureEvent {
    pub asset: String,
    pub exposure_amount: Decimal,
    pub exposure_percentage: Decimal,
    pub risk_level: String,
    pub timestamp: DateTime<Utc>,
}

/// 价格事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PriceEvent {
    pub symbol: String,
    pub price: Decimal,
    pub price_change: Decimal,
    pub price_change_percent: Decimal,
    pub timestamp: DateTime<Utc>,
}

/// 成交量事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VolumeEvent {
    pub symbol: String,
    pub volume_24h: Decimal,
    pub volume_change: Decimal,
    pub timestamp: DateTime<Utc>,
}

/// 订单簿事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrderbookEvent {
    pub symbol: String,
    pub best_bid: Decimal,
    pub best_ask: Decimal,
    pub spread: Decimal,
    pub bid_depth: Decimal,
    pub ask_depth: Decimal,
    pub timestamp: DateTime<Utc>,
}

/// 行情事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TickerEvent {
    pub symbol: String,
    pub last_price: Decimal,
    pub high_24h: Decimal,
    pub low_24h: Decimal,
    pub volume_24h: Decimal,
    pub change_24h: Decimal,
    pub timestamp: DateTime<Utc>,
}

/// 系统状态事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemStatusEvent {
    pub component: String,
    pub status: String, // "healthy" | "warning" | "error"
    pub message: String,
    pub timestamp: DateTime<Utc>,
}

/// 性能指标事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MetricsEvent {
    pub cpu_usage: f64,
    pub memory_usage: f64,
    pub disk_usage: f64,
    pub network_io: f64,
    pub active_connections: u64,
    pub requests_per_second: f64,
    pub timestamp: DateTime<Utc>,
}

/// 引擎状态事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EngineStatusEvent {
    pub engine_id: String,
    pub status: String,
    pub uptime: u64,
    pub processed_orders: u64,
    pub error_count: u64,
    pub timestamp: DateTime<Utc>,
}

/// 连接状态事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConnectionStatusEvent {
    pub connection_type: String,
    pub endpoint: String,
    pub status: String, // "connected" | "disconnected" | "error"
    pub latency: Option<u64>,
    pub timestamp: DateTime<Utc>,
}

/// 回测事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BacktestEvent {
    pub backtest_id: String,
    pub strategy_id: String,
    pub status: String,
    pub start_date: DateTime<Utc>,
    pub end_date: Option<DateTime<Utc>>,
    pub timestamp: DateTime<Utc>,
}

/// 回测进度事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BacktestProgressEvent {
    pub backtest_id: String,
    pub progress_percentage: f64,
    pub current_date: DateTime<Utc>,
    pub processed_candles: u64,
    pub total_candles: u64,
    pub estimated_completion: Option<DateTime<Utc>>,
    pub timestamp: DateTime<Utc>,
}

/// 报告事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ReportEvent {
    pub report_id: String,
    pub report_type: String,
    pub status: String,
    pub progress: Option<f64>,
    pub file_path: Option<String>,
    pub error_message: Option<String>,
    pub timestamp: DateTime<Utc>,
}

impl RealtimeEventType {
    /// 获取事件对应的WebSocket频道
    pub fn get_channel(&self) -> &'static str {
        match self {
            // 订单和交易事件
            RealtimeEventType::OrderCreated(_) |
            RealtimeEventType::OrderUpdated(_) |
            RealtimeEventType::OrderFilled(_) |
            RealtimeEventType::OrderCancelled(_) |
            RealtimeEventType::OrderRejected(_) => "orders",

            RealtimeEventType::TradeExecuted(_) |
            RealtimeEventType::TradeSettled(_) |
            RealtimeEventType::TradeFailed(_) => "trades",

            // 策略事件
            RealtimeEventType::StrategyStarted(_) |
            RealtimeEventType::StrategyStopped(_) |
            RealtimeEventType::StrategyPaused(_) |
            RealtimeEventType::StrategyResumed(_) |
            RealtimeEventType::StrategyError(_) |
            RealtimeEventType::StrategyPerformanceUpdate(_) => "strategy_execution",

            // 风险事件
            RealtimeEventType::RiskAlert(_) |
            RealtimeEventType::RiskThresholdTriggered(_) |
            RealtimeEventType::EmergencyStop(_) |
            RealtimeEventType::AutoResponse(_) => "risk_alerts",

            // 投资组合事件
            RealtimeEventType::BalanceUpdated(_) |
            RealtimeEventType::PositionChanged(_) |
            RealtimeEventType::PnlUpdated(_) |
            RealtimeEventType::ExposureChanged(_) => "portfolio",

            // 市场数据事件
            RealtimeEventType::PriceUpdate(_) |
            RealtimeEventType::VolumeUpdate(_) |
            RealtimeEventType::OrderbookUpdate(_) |
            RealtimeEventType::TickerUpdate(_) => "market_data",

            // 系统事件
            RealtimeEventType::SystemStatusUpdate(_) |
            RealtimeEventType::PerformanceMetrics(_) |
            RealtimeEventType::EngineStatusUpdate(_) |
            RealtimeEventType::ConnectionStatusUpdate(_) => "system_status",

            // 回测事件
            RealtimeEventType::BacktestStarted(_) |
            RealtimeEventType::BacktestProgressUpdate(_) |
            RealtimeEventType::BacktestCompleted(_) => "backtest_progress",

            // 报告事件
            RealtimeEventType::ReportGenerating(_) |
            RealtimeEventType::ReportCompleted(_) |
            RealtimeEventType::ReportFailed(_) => "reports",
        }
    }

    /// 获取事件类型名称
    pub fn get_event_name(&self) -> &'static str {
        match self {
            RealtimeEventType::OrderCreated(_) => "order_created",
            RealtimeEventType::OrderUpdated(_) => "order_updated",
            RealtimeEventType::OrderFilled(_) => "order_filled",
            RealtimeEventType::OrderCancelled(_) => "order_cancelled",
            RealtimeEventType::OrderRejected(_) => "order_rejected",
            RealtimeEventType::TradeExecuted(_) => "trade_executed",
            RealtimeEventType::TradeSettled(_) => "trade_settled",
            RealtimeEventType::TradeFailed(_) => "trade_failed",
            RealtimeEventType::StrategyStarted(_) => "strategy_started",
            RealtimeEventType::StrategyStopped(_) => "strategy_stopped",
            RealtimeEventType::StrategyPaused(_) => "strategy_paused",
            RealtimeEventType::StrategyResumed(_) => "strategy_resumed",
            RealtimeEventType::StrategyError(_) => "strategy_error",
            RealtimeEventType::StrategyPerformanceUpdate(_) => "performance_update",
            RealtimeEventType::RiskAlert(_) => "risk_alert",
            RealtimeEventType::RiskThresholdTriggered(_) => "risk_threshold",
            RealtimeEventType::EmergencyStop(_) => "emergency_stop",
            RealtimeEventType::AutoResponse(_) => "auto_response",
            RealtimeEventType::BalanceUpdated(_) => "balance_updated",
            RealtimeEventType::PositionChanged(_) => "position_changed",
            RealtimeEventType::PnlUpdated(_) => "pnl_updated",
            RealtimeEventType::ExposureChanged(_) => "exposure_changed",
            RealtimeEventType::PriceUpdate(_) => "price_update",
            RealtimeEventType::VolumeUpdate(_) => "volume_update",
            RealtimeEventType::OrderbookUpdate(_) => "orderbook_update",
            RealtimeEventType::TickerUpdate(_) => "ticker_update",
            RealtimeEventType::SystemStatusUpdate(_) => "system_status",
            RealtimeEventType::PerformanceMetrics(_) => "performance_metrics",
            RealtimeEventType::EngineStatusUpdate(_) => "engine_status",
            RealtimeEventType::ConnectionStatusUpdate(_) => "connection_status",
            RealtimeEventType::BacktestStarted(_) => "backtest_started",
            RealtimeEventType::BacktestProgressUpdate(_) => "progress_update",
            RealtimeEventType::BacktestCompleted(_) => "backtest_completed",
            RealtimeEventType::ReportGenerating(_) => "report_generating",
            RealtimeEventType::ReportCompleted(_) => "report_completed",
            RealtimeEventType::ReportFailed(_) => "report_failed",
        }
    }

    /// 判断是否为高优先级事件（需要立即推送）
    pub fn is_high_priority(&self) -> bool {
        matches!(self,
            RealtimeEventType::RiskAlert(_) |
            RealtimeEventType::EmergencyStop(_) |
            RealtimeEventType::StrategyError(_) |
            RealtimeEventType::OrderRejected(_) |
            RealtimeEventType::TradeFailed(_)
        )
    }
}
