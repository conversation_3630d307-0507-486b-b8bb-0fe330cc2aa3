//! SigmaX 接口层 - Interface Layer
//!
//! 实现面向接口设计原则，定义清晰的接口契约
//! 
//! ## 核心设计原则体现
//!
//! ### 3. 面向接口设计 (Design to an Interface)
//! - 定义清晰的接口契约
//! - 依赖抽象而非具体实现
//! - 支持多种实现方式
//!
//! ### 4. 可测试性设计 (Design for Testability)
//! - 所有接口都支持Mock实现
//! - 依赖注入友好的设计
//! - 便于单元测试和集成测试
//!
//! ## 接口分类
//!
//! - **交易相关**: 订单处理、交易执行
//! - **风控相关**: 风险检查、限制管理
//! - **数据相关**: 市场数据、历史数据
//! - **执行相关**: 引擎管理、订单路由
//!
//! ## 使用方式
//!
//! ```rust
//! use sigmax_interfaces::{RiskController, ExecutionEngine, TradingOrchestrator};
//! use std::sync::Arc;
//!
//! // 依赖抽象接口而非具体实现
//! struct TradingSystem {
//!     risk_controller: Arc<dyn RiskController>,
//!     execution_engine: Arc<dyn ExecutionEngine>,
//!     orchestrator: Arc<dyn TradingOrchestrator>,
//! }
//! ```

// ============================================================================
// 模块导出
// ============================================================================

/// 交易相关接口
pub mod trading;

/// 风控相关接口  
pub mod risk;

/// 数据相关接口
pub mod data;

/// 执行相关接口
pub mod execution;

// ============================================================================
// 统一导出
// ============================================================================

// 交易接口
pub use trading::{
    TradingOrchestrator, TradeRequest, TradeResult, TradeError,
    OrderManager, OrderResult, OrderError,
    PortfolioManager, Portfolio, PortfolioError
};

// 风控接口
pub use risk::{
    RiskController, RiskResult, RiskError,
    RiskMetrics, RiskContext, RiskPolicy,
    PolicyResult, PolicyError
};

// 数据接口
pub use data::{
    DataProvider, DataResult, DataError,
    MarketDataProvider, HistoricalDataProvider,
    PriceProvider, PriceResult, MarketData, OrderBook, Trade
};

// 执行接口
pub use execution::{
    ExecutionEngine, ExecutionResult, ExecutionError,
    EngineStatus, EngineManager, EngineConfig
};

// ============================================================================
// 通用结果类型
// ============================================================================

/// 接口层统一结果类型
pub type InterfaceResult<T> = Result<T, InterfaceError>;

/// 接口层统一错误类型
#[derive(Debug, thiserror::Error)]
pub enum InterfaceError {
    #[error("Trading error: {0}")]
    Trading(#[from] TradeError),
    
    #[error("Risk error: {0}")]
    Risk(#[from] RiskError),
    
    #[error("Data error: {0}")]
    Data(#[from] DataError),
    
    #[error("Execution error: {0}")]
    Execution(#[from] ExecutionError),
    
    #[error("Configuration error: {message}")]
    Configuration { message: String },
    
    #[error("Validation error: {message}")]
    Validation { message: String },
    
    #[error("Timeout error: operation timed out after {timeout_ms}ms")]
    Timeout { timeout_ms: u64 },
    
    #[error("Not implemented: {feature}")]
    NotImplemented { feature: String },
}