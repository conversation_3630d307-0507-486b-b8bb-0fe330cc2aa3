/**
 * SigmaX Trading System - 配置工具类
 * 
 * 提供配置导入/导出、验证、模板等实用功能
 */

class ConfigUtils {
    constructor() {
        // 🔥 更新：配置模板使用后端真实数据格式
        this.configTemplates = {
            development: {
                system: {
                    maintenance_mode: false,
                    max_concurrent_strategies: 20,
                    data_retention_days: 90,
                    backup_enabled: true,
                    log_level: "DEBUG"
                },
                api: {
                    rate_limit_per_minute: 500,
                    max_request_size: 2097152, // 2MB
                    timeout_seconds: 30,
                    cors_enabled: true
                },
                cache: {
                    name: "development",
                    description: "开发环境缓存配置",
                    enabled: true,
                    redis_enabled: false,
                    default_ttl_seconds: 300,
                    max_memory_mb: 128,
                    max_entries: 10000,
                    cleanup_interval_seconds: 300,
                    memory_threshold_percentage: 80
                },
                monitoring: {
                    name: "development",
                    description: "开发环境监控配置",
                    enabled: true,
                    metrics_enabled: true,
                    health_check_interval: 60,
                    data_collection_interval: 30,
                    data_retention_days: 7
                }
            },
            production: {
                system: {
                    maintenance_mode: false,
                    max_concurrent_strategies: 100,
                    data_retention_days: 365,
                    backup_enabled: true,
                    log_level: "INFO"
                },
                api: {
                    rate_limit_per_minute: 2000,
                    max_request_size: 1048576, // 1MB
                    timeout_seconds: 60,
                    cors_enabled: false
                },
                cache: {
                    name: "production",
                    description: "生产环境缓存配置",
                    enabled: true,
                    redis_enabled: true,
                    default_ttl_seconds: 3600,
                    max_memory_mb: 1024,
                    max_entries: 1000000,
                    cleanup_interval_seconds: 600,
                    memory_threshold_percentage: 85
                },
                monitoring: {
                    name: "production",
                    description: "生产环境监控配置",
                    enabled: true,
                    metrics_enabled: true,
                    health_check_interval: 30,
                    data_collection_interval: 15,
                    data_retention_days: 90
                }
            }
        };
        
        console.log('🛠️ ConfigUtils initialized');
    }

    /**
     * 导出配置为JSON文件
     * @param {Object} configs 配置数据
     * @param {string} filename 文件名
     */
    exportConfigs(configs, filename = null) {
        try {
            if (!filename) {
                const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
                filename = `sigmax-config-${timestamp}.json`;
            }

            const exportData = {
                metadata: {
                    exported_at: new Date().toISOString(),
                    version: "1.0.0",
                    system: "SigmaX Trading System"
                },
                configs: configs
            };

            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            console.log('✅ 配置导出成功:', filename);
            return true;

        } catch (error) {
            console.error('❌ 配置导出失败:', error);
            throw new Error(`配置导出失败: ${error.message}`);
        }
    }

    /**
     * 导入配置文件
     * @param {File} file 配置文件
     * @returns {Promise<Object>} 导入的配置数据
     */
    async importConfigs(file) {
        return new Promise((resolve, reject) => {
            try {
                if (!file) {
                    throw new Error('请选择配置文件');
                }

                if (!file.name.endsWith('.json')) {
                    throw new Error('配置文件必须是JSON格式');
                }

                const reader = new FileReader();
                
                reader.onload = (e) => {
                    try {
                        const importData = JSON.parse(e.target.result);
                        
                        // 验证导入数据格式
                        this.validateImportData(importData);
                        
                        console.log('✅ 配置导入成功');
                        resolve(importData.configs);
                        
                    } catch (parseError) {
                        console.error('❌ 配置文件解析失败:', parseError);
                        reject(new Error(`配置文件解析失败: ${parseError.message}`));
                    }
                };
                
                reader.onerror = () => {
                    reject(new Error('文件读取失败'));
                };
                
                reader.readAsText(file);

            } catch (error) {
                console.error('❌ 配置导入失败:', error);
                reject(error);
            }
        });
    }

    /**
     * 验证导入数据格式
     * @param {Object} importData 导入的数据
     */
    validateImportData(importData) {
        if (!importData || typeof importData !== 'object') {
            throw new Error('配置文件格式无效');
        }

        if (!importData.configs || typeof importData.configs !== 'object') {
            throw new Error('配置文件缺少configs字段');
        }

        // 验证必要的配置类型
        const requiredTypes = ['system', 'api', 'cache', 'monitoring'];
        const missingTypes = requiredTypes.filter(type => !importData.configs[type]);
        
        if (missingTypes.length > 0) {
            console.warn('⚠️ 配置文件缺少以下配置类型:', missingTypes);
        }

        // 验证各配置类型的基本结构
        Object.entries(importData.configs).forEach(([type, config]) => {
            if (!config || typeof config !== 'object') {
                throw new Error(`${type}配置格式无效`);
            }
        });
    }

    /**
     * 获取配置模板
     * @param {string} templateType 模板类型 (development, production)
     * @returns {Object} 配置模板
     */
    getConfigTemplate(templateType) {
        const template = this.configTemplates[templateType];
        if (!template) {
            throw new Error(`未知的模板类型: ${templateType}`);
        }
        
        // 返回深拷贝，避免修改原模板
        return JSON.parse(JSON.stringify(template));
    }

    /**
     * 应用配置模板
     * @param {string} templateType 模板类型
     * @returns {Object} 模板配置
     */
    applyTemplate(templateType) {
        try {
            const template = this.getConfigTemplate(templateType);
            console.log(`✅ 已应用${templateType}模板`);
            return template;
        } catch (error) {
            console.error('❌ 应用模板失败:', error);
            throw error;
        }
    }

    /**
     * 比较两个配置对象的差异
     * @param {Object} config1 配置1
     * @param {Object} config2 配置2
     * @returns {Object} 差异信息
     */
    compareConfigs(config1, config2) {
        const differences = {};
        
        // 获取所有配置类型
        const allTypes = new Set([
            ...Object.keys(config1 || {}),
            ...Object.keys(config2 || {})
        ]);

        allTypes.forEach(type => {
            const diff = this.compareConfigType(config1?.[type], config2?.[type]);
            if (Object.keys(diff).length > 0) {
                differences[type] = diff;
            }
        });

        return differences;
    }

    /**
     * 比较单个配置类型的差异
     * @param {Object} config1 配置1
     * @param {Object} config2 配置2
     * @returns {Object} 差异信息
     */
    compareConfigType(config1, config2) {
        const differences = {};
        
        if (!config1 && !config2) return differences;
        
        if (!config1) {
            return { _status: 'added', _value: config2 };
        }
        
        if (!config2) {
            return { _status: 'removed', _value: config1 };
        }

        // 获取所有字段
        const allFields = new Set([
            ...Object.keys(config1),
            ...Object.keys(config2)
        ]);

        allFields.forEach(field => {
            const value1 = config1[field];
            const value2 = config2[field];

            if (value1 !== value2) {
                differences[field] = {
                    old: value1,
                    new: value2
                };
            }
        });

        return differences;
    }

    /**
     * 验证配置完整性
     * @param {Object} configs 配置对象
     * @returns {Object} 验证结果
     */
    validateConfigs(configs) {
        const results = {
            valid: true,
            errors: [],
            warnings: []
        };

        if (!configs || typeof configs !== 'object') {
            results.valid = false;
            results.errors.push('配置数据无效');
            return results;
        }

        // 验证各配置类型
        Object.entries(configs).forEach(([type, config]) => {
            try {
                this.validateConfigType(type, config);
            } catch (error) {
                results.valid = false;
                results.errors.push(`${type}: ${error.message}`);
            }
        });

        // 检查缺失的配置类型
        const requiredTypes = ['system', 'api', 'cache', 'monitoring'];
        const missingTypes = requiredTypes.filter(type => !configs[type]);
        
        if (missingTypes.length > 0) {
            results.warnings.push(`缺少配置类型: ${missingTypes.join(', ')}`);
        }

        return results;
    }

    /**
     * 验证单个配置类型
     * @param {string} type 配置类型
     * @param {Object} config 配置数据
     */
    validateConfigType(type, config) {
        if (!config || typeof config !== 'object') {
            throw new Error('配置数据必须是对象');
        }

        switch (type) {
            case 'system':
                if (!config.name || typeof config.name !== 'string') {
                    throw new Error('系统名称必须是非空字符串');
                }
                if (!config.version || typeof config.version !== 'string') {
                    throw new Error('系统版本必须是非空字符串');
                }
                break;

            case 'api':
                if (config.timeout_seconds && (typeof config.timeout_seconds !== 'number' || config.timeout_seconds < 1)) {
                    throw new Error('API超时时间必须是正数');
                }
                if (config.retry_count && (typeof config.retry_count !== 'number' || config.retry_count < 0)) {
                    throw new Error('重试次数必须是非负数');
                }
                break;

            case 'cache':
                if (config.default_ttl_seconds && (typeof config.default_ttl_seconds !== 'number' || config.default_ttl_seconds < 1)) {
                    throw new Error('缓存TTL必须是正数');
                }
                if (config.max_size_mb && (typeof config.max_size_mb !== 'number' || config.max_size_mb < 1)) {
                    throw new Error('缓存大小必须是正数');
                }
                break;

            case 'monitoring':
                if (config.interval_seconds && (typeof config.interval_seconds !== 'number' || config.interval_seconds < 1)) {
                    throw new Error('监控间隔必须是正数');
                }
                if (config.retention_days && (typeof config.retention_days !== 'number' || config.retention_days < 1)) {
                    throw new Error('数据保留天数必须是正数');
                }
                break;
        }
    }

    /**
     * 获取可用的模板列表
     * @returns {Array} 模板列表
     */
    getAvailableTemplates() {
        return Object.keys(this.configTemplates).map(key => ({
            key,
            name: this.getTemplateName(key),
            description: this.getTemplateDescription(key)
        }));
    }

    /**
     * 获取模板名称
     * @param {string} templateType 模板类型
     * @returns {string} 模板名称
     */
    getTemplateName(templateType) {
        const names = {
            development: '开发环境',
            production: '生产环境'
        };
        return names[templateType] || templateType;
    }

    /**
     * 获取模板描述
     * @param {string} templateType 模板类型
     * @returns {string} 模板描述
     */
    getTemplateDescription(templateType) {
        const descriptions = {
            development: '适用于开发和测试环境的配置模板',
            production: '适用于生产环境的配置模板'
        };
        return descriptions[templateType] || '配置模板';
    }
}

// 创建全局实例
window.ConfigUtils = new ConfigUtils();

console.log('✅ ConfigUtils 已加载并初始化');
