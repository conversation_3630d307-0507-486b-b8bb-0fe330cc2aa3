//! Repository Module
//!
//! 数据仓储模块，提供统一的数据访问接口和实现
//!
//! 此模块只导出 traits（接口），具体实现通过工厂函数创建

// 公开的子模块
pub mod traits;

// 实现模块（临时公开以支持向后兼容）
pub mod sqlx;
pub(crate) mod mock_order_repository;

// Diesel ORM 模块 (可选，内部使用)
#[cfg(feature = "diesel")]
pub(crate) mod diesel;

// 只导出 traits（接口）
pub use traits::{
    OrderRepository, TradeRepository, StrategyRepository, EventRepository,
    EnhancedOrderRepository, OrderQueryFilter, Pagination, OrderSort,
    OrderSortBy, SortDirection, OrderStatistics, OrderRepositoryBuilder,
    RiskRepository, RiskCheckRecord, RiskRuleRecord,
    RiskViolationRecord, RiskQueryFilter, RiskRuleFilter,
    SystemConfigRepository, ConfigStatistics
};
