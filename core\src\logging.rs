//! 日志系统

use crate::{SigmaXResult, SigmaXError, LoggingConfig, LogFormat, LogOutput};
use tracing::Level;
use tracing_subscriber::EnvFilter;

/// 日志系统管理器
#[derive(Debug)]
pub struct LoggingManager {
    config: LoggingConfig,
}

impl LoggingManager {
    /// 创建新的日志管理器
    pub fn new(config: LoggingConfig) -> Self {
        Self { config }
    }

    /// 初始化日志系统
    pub fn init(&self) -> SigmaXResult<()> {
        // 解析日志级别
        let level = self.parse_log_level(&self.config.level)?;

        // 创建环境过滤器
        let env_filter = EnvFilter::try_from_default_env()
            .unwrap_or_else(|_| EnvFilter::new(level.as_str()));

        // 根据输出配置创建订阅者
        match &self.config.output {
            LogOutput::Console => {
                self.init_console_logging(env_filter)?;
            }
            LogOutput::File { path } => {
                self.init_file_logging(path, env_filter)?;
            }
            LogOutput::Both { path: _ } => {
                // 对于Both模式，我们简化为控制台输出
                self.init_console_logging(env_filter)?;
                tracing::warn!("File logging not yet implemented, using console only");
            }
        }

        tracing::info!("Logging system initialized with level: {}", self.config.level);
        Ok(())
    }

    /// 初始化控制台日志
    fn init_console_logging(&self, env_filter: EnvFilter) -> SigmaXResult<()> {
        match self.config.format {
            LogFormat::Json => {
                tracing_subscriber::fmt()
                    .json()
                    .with_env_filter(env_filter)
                    .init();
            }
            LogFormat::Compact => {
                tracing_subscriber::fmt()
                    .compact()
                    .with_env_filter(env_filter)
                    .init();
            }
            LogFormat::Text => {
                tracing_subscriber::fmt()
                    .with_env_filter(env_filter)
                    .init();
            }
        }

        Ok(())
    }

    /// 初始化文件日志
    fn init_file_logging(&self, _path: &str, env_filter: EnvFilter) -> SigmaXResult<()> {
        // 简化实现：暂时使用控制台输出
        tracing::warn!("File logging not yet implemented, using console output");
        self.init_console_logging(env_filter)
    }

    /// 解析日志级别
    fn parse_log_level(&self, level: &str) -> SigmaXResult<Level> {
        match level.to_lowercase().as_str() {
            "trace" => Ok(Level::TRACE),
            "debug" => Ok(Level::DEBUG),
            "info" => Ok(Level::INFO),
            "warn" => Ok(Level::WARN),
            "error" => Ok(Level::ERROR),
            _ => Err(SigmaXError::Config(format!("Invalid log level: {}", level))),
        }
    }
}

/// 初始化日志系统的便捷函数
pub fn init_logging(config: &LoggingConfig) -> SigmaXResult<()> {
    let manager = LoggingManager::new(config.clone());
    manager.init()
}

/// 创建结构化日志字段
#[macro_export]
macro_rules! log_fields {
    ($($key:expr => $value:expr),* $(,)?) => {
        tracing::field::display(format_args!("{}", serde_json::json!({
            $($key: $value),*
        })))
    };
}

/// 记录交易事件的便捷宏
#[macro_export]
macro_rules! log_trade_event {
    ($level:ident, $event:expr, $($key:expr => $value:expr),* $(,)?) => {
        tracing::$level!(
            event = $event,
            $($key = $value),*
        );
    };
}

/// 记录性能指标的便捷宏
#[macro_export]
macro_rules! log_performance {
    ($operation:expr, $duration:expr, $($key:expr => $value:expr),* $(,)?) => {
        tracing::info!(
            operation = $operation,
            duration_ms = $duration.as_millis(),
            $($key = $value),*
        );
    };
}

/// 记录错误的便捷宏
#[macro_export]
macro_rules! log_error {
    ($error:expr, $context:expr, $($key:expr => $value:expr),* $(,)?) => {
        tracing::error!(
            error = %$error,
            context = $context,
            $($key = $value),*
        );
    };
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parse_log_level() {
        let config = LoggingConfig {
            level: "info".to_string(),
            format: LogFormat::Text,
            output: LogOutput::Console,
            file_rotation: None,
        };

        let manager = LoggingManager::new(config);

        assert!(matches!(manager.parse_log_level("trace"), Ok(Level::TRACE)));
        assert!(matches!(manager.parse_log_level("debug"), Ok(Level::DEBUG)));
        assert!(matches!(manager.parse_log_level("info"), Ok(Level::INFO)));
        assert!(matches!(manager.parse_log_level("warn"), Ok(Level::WARN)));
        assert!(matches!(manager.parse_log_level("error"), Ok(Level::ERROR)));

        assert!(manager.parse_log_level("invalid").is_err());
    }

    #[test]
    fn test_logging_manager_creation() {
        let config = LoggingConfig {
            level: "info".to_string(),
            format: LogFormat::Json,
            output: LogOutput::Console,
            file_rotation: None,
        };

        let manager = LoggingManager::new(config.clone());
        assert_eq!(manager.config.level, "info");
        assert!(matches!(manager.config.format, LogFormat::Json));
        assert!(matches!(manager.config.output, LogOutput::Console));
    }
}