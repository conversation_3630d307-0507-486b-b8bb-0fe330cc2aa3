//! API仓储接口定义
//!
//! 专门负责API相关数据的访问和管理

use async_trait::async_trait;
use sigmax_core::{SigmaXResult, ApiConfig};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use serde::{Serialize, Deserialize};

/// API请求记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiRequestRecord {
    /// 请求ID
    pub id: Uuid,
    /// 请求路径
    pub path: String,
    /// HTTP方法
    pub method: String,
    /// 客户端IP
    pub client_ip: String,
    /// 用户代理
    pub user_agent: Option<String>,
    /// 响应状态码
    pub status_code: u16,
    /// 响应时间（毫秒）
    pub response_time_ms: u32,
    /// 请求时间
    pub timestamp: DateTime<Utc>,
}

/// API限流记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiRateLimitRecord {
    /// 记录ID
    pub id: Uuid,
    /// 客户端标识（IP或用户ID）
    pub client_id: String,
    /// API端点
    pub endpoint: String,
    /// 请求次数
    pub request_count: u32,
    /// 时间窗口开始时间
    pub window_start: DateTime<Utc>,
    /// 时间窗口结束时间
    pub window_end: DateTime<Utc>,
}

/// API仓储接口
#[async_trait]
pub trait ApiRepository: Send + Sync {
    // ============================================================================
    // API配置管理
    // ============================================================================

    /// 获取API配置
    async fn get_api_config(&self) -> SigmaXResult<ApiConfig>;

    /// 保存API配置
    async fn save_api_config(&self, config: &ApiConfig) -> SigmaXResult<()>;

    /// 重置API配置为默认值
    async fn reset_api_config(&self) -> SigmaXResult<()>;

    // ============================================================================
    // API请求记录管理
    // ============================================================================

    /// 保存API请求记录
    async fn save_api_request(&self, request: &ApiRequestRecord) -> SigmaXResult<()>;

    /// 获取API请求统计
    async fn get_api_request_stats(&self, start_time: DateTime<Utc>, end_time: DateTime<Utc>) -> SigmaXResult<ApiRequestStats>;

    /// 获取最近的API请求
    async fn get_recent_api_requests(&self, limit: usize) -> SigmaXResult<Vec<ApiRequestRecord>>;

    /// 删除过期的API请求记录
    async fn delete_expired_api_requests(&self, before: DateTime<Utc>) -> SigmaXResult<u64>;

    // ============================================================================
    // API限流管理
    // ============================================================================

    /// 保存限流记录
    async fn save_rate_limit_record(&self, record: &ApiRateLimitRecord) -> SigmaXResult<()>;

    /// 获取限流记录
    async fn get_rate_limit_record(&self, client_id: &str, endpoint: &str, window_start: DateTime<Utc>) -> SigmaXResult<Option<ApiRateLimitRecord>>;

    /// 更新限流计数
    async fn increment_rate_limit_count(&self, client_id: &str, endpoint: &str, window_start: DateTime<Utc>, window_end: DateTime<Utc>) -> SigmaXResult<u32>;

    /// 清理过期的限流记录
    async fn cleanup_expired_rate_limits(&self, before: DateTime<Utc>) -> SigmaXResult<u64>;
}

/// API请求统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiRequestStats {
    /// 总请求数
    pub total_requests: u64,
    /// 成功请求数（2xx状态码）
    pub successful_requests: u64,
    /// 错误请求数（4xx和5xx状态码）
    pub error_requests: u64,
    /// 平均响应时间（毫秒）
    pub average_response_time_ms: f64,
    /// 最大响应时间（毫秒）
    pub max_response_time_ms: u32,
    /// 最小响应时间（毫秒）
    pub min_response_time_ms: u32,
    /// 按状态码分组的请求数
    pub requests_by_status: std::collections::HashMap<u16, u64>,
    /// 按路径分组的请求数
    pub requests_by_path: std::collections::HashMap<String, u64>,
}
