use std::{collections::HashMap, sync::Arc, time::Duration};
use tokio::sync::RwLock;
use async_trait::async_trait;
use serde_json;
use chrono::Utc;
use uuid;

use sigmax_core::{
    events::{EventHandler, SystemEvent, EventType},
    SigmaXResult, SigmaXError,
};

use crate::{
    websocket::WebSocketServer,
    realtime_events::{RealtimeEventType, OrderEvent, TradeEvent, StrategyEvent, RiskAlertEvent},
};

/// WebSocket实时事件处理器
pub struct WebSocketEventHandler {
    websocket_server: Arc<WebSocketServer>,
    event_filters: Arc<RwLock<HashMap<String, Vec<EventType>>>>,
    batch_processor: Arc<RwLock<RealtimeBatchProcessor>>,
}

impl WebSocketEventHandler {
    /// 创建新的WebSocket事件处理器
    pub fn new(websocket_server: Arc<WebSocketServer>) -> Self {
        let mut event_filters = HashMap::new();
        
        // 配置事件过滤器 - 定义哪些系统事件映射到哪些WebSocket频道
        event_filters.insert("orders".to_string(), vec![
            EventType::OrderCreated,
            EventType::OrderUpdated,
            EventType::OrderCancelled,
        ]);
        
        event_filters.insert("trades".to_string(), vec![
            EventType::TradeExecuted,
        ]);
        
        event_filters.insert("strategy_execution".to_string(), vec![
            EventType::StrategyStarted,
            EventType::StrategyStoped,
        ]);
        
        event_filters.insert("risk_alerts".to_string(), vec![
            EventType::RiskAlert,
        ]);
        
        event_filters.insert("system_status".to_string(), vec![
            EventType::EngineCreated,
            EventType::EngineStarted,
            EventType::EngineStopped,
            EventType::EngineError,
        ]);
        
        Self {
            websocket_server,
            event_filters: Arc::new(RwLock::new(event_filters)),
            batch_processor: Arc::new(RwLock::new(RealtimeBatchProcessor::new())),
        }
    }
    
    /// 将系统事件转换为WebSocket实时事件
    async fn convert_to_realtime_event(&self, event: &SystemEvent) -> Option<RealtimeEventType> {
        match event {
            SystemEvent::OrderCreated(order) => {
                Some(RealtimeEventType::OrderCreated(OrderEvent {
                    order_id: order.id.to_string(),
                    strategy_id: order.strategy_id.map(|id| id.to_string()),
                    symbol: order.trading_pair.symbol(),
                    side: format!("{:?}", order.side),
                    order_type: format!("{:?}", order.order_type),
                    quantity: order.quantity,
                    price: order.price,
                    status: format!("{:?}", order.status),
                    filled_quantity: order.filled_quantity,
                    remaining_quantity: order.quantity - order.filled_quantity,
                    average_price: order.average_price,
                    timestamp: Utc::now(),
                    reason: None,
                }))
            },

            SystemEvent::OrderUpdated(order) => {
                Some(RealtimeEventType::OrderUpdated(OrderEvent {
                    order_id: order.id.to_string(),
                    strategy_id: order.strategy_id.map(|id| id.to_string()),
                    symbol: order.trading_pair.symbol(),
                    side: format!("{:?}", order.side),
                    order_type: format!("{:?}", order.order_type),
                    quantity: order.quantity,
                    price: order.price,
                    status: format!("{:?}", order.status),
                    filled_quantity: order.filled_quantity,
                    remaining_quantity: order.quantity - order.filled_quantity,
                    average_price: order.average_price,
                    timestamp: Utc::now(),
                    reason: None,
                }))
            },

            SystemEvent::OrderCancelled(order_id) => {
                Some(RealtimeEventType::OrderCancelled(OrderEvent {
                    order_id: order_id.to_string(),
                    strategy_id: None,
                    symbol: "UNKNOWN".to_string(), // 需要从订单缓存获取
                    side: "UNKNOWN".to_string(),
                    order_type: "UNKNOWN".to_string(),
                    quantity: rust_decimal::Decimal::ZERO,
                    price: None,
                    status: "CANCELLED".to_string(),
                    filled_quantity: rust_decimal::Decimal::ZERO,
                    remaining_quantity: rust_decimal::Decimal::ZERO,
                    average_price: None,
                    timestamp: Utc::now(),
                    reason: Some("Order cancelled".to_string()),
                }))
            },

            SystemEvent::TradeExecuted(trade) => {
                Some(RealtimeEventType::TradeExecuted(TradeEvent {
                    trade_id: trade.id.to_string(),
                    order_id: trade.order_id.to_string(),
                    strategy_id: None, // Trade结构体没有strategy_id字段
                    symbol: trade.trading_pair.symbol(),
                    side: format!("{:?}", trade.side),
                    quantity: trade.quantity,
                    price: trade.price,
                    fee: trade.fee,
                    fee_asset: trade.fee_asset.clone().unwrap_or_else(|| "UNKNOWN".to_string()),
                    timestamp: trade.executed_at,
                }))
            },

            SystemEvent::StrategyStarted(strategy_id) => {
                Some(RealtimeEventType::StrategyStarted(StrategyEvent {
                    strategy_id: strategy_id.to_string(),
                    strategy_name: "Unknown".to_string(), // 需要从策略服务获取
                    status: "STARTED".to_string(),
                    timestamp: Utc::now(),
                    reason: None,
                    metadata: None,
                }))
            },

            SystemEvent::StrategyStoped(strategy_id) => {
                Some(RealtimeEventType::StrategyStopped(StrategyEvent {
                    strategy_id: strategy_id.to_string(),
                    strategy_name: "Unknown".to_string(),
                    status: "STOPPED".to_string(),
                    timestamp: Utc::now(),
                    reason: None,
                    metadata: None,
                }))
            },

            SystemEvent::RiskAlert(alert) => {
                Some(RealtimeEventType::RiskAlert(RiskAlertEvent {
                    alert_id: uuid::Uuid::new_v4().to_string(),
                    alert_type: alert.alert_type.clone(),
                    severity: format!("{:?}", alert.severity),
                    message: alert.message.clone(),
                    strategy_id: alert.strategy_id.map(|id| id.to_string()),
                    risk_metric: "UNKNOWN".to_string(),
                    current_value: rust_decimal::Decimal::ZERO,
                    threshold: rust_decimal::Decimal::ZERO,
                    timestamp: Utc::now(),
                }))
            },

            _ => None, // 其他事件暂不处理
        }
    }
    
    /// 处理实时事件推送
    async fn handle_realtime_event(&self, realtime_event: RealtimeEventType) -> SigmaXResult<()> {
        let channel = realtime_event.get_channel();
        let event_name = realtime_event.get_event_name();
        
        // 检查是否为高优先级事件
        if realtime_event.is_high_priority() {
            // 高优先级事件立即推送
            self.websocket_server.broadcast_message_to_channel(
                channel,
                event_name,
                serde_json::to_value(&realtime_event)
                    .map_err(|e| SigmaXError::Internal(format!("序列化事件失败: {}", e)))?,
            ).await?;
        } else {
            // 普通事件加入批处理队列
            let mut batch_processor = self.batch_processor.write().await;
            batch_processor.add_event(realtime_event).await?;
            
            // 检查是否需要刷新批处理
            if batch_processor.should_flush() {
                let batched_events = batch_processor.flush().await;
                for (channel, events) in batched_events {
                    for event in events {
                        let event_name = event.get_event_name();
                        self.websocket_server.broadcast_message_to_channel(
                            &channel,
                            event_name,
                            serde_json::to_value(&event)
                                .map_err(|e| SigmaXError::Internal(format!("序列化事件失败: {}", e)))?,
                        ).await?;
                    }
                }
            }
        }
        
        Ok(())
    }
}

#[async_trait]
impl EventHandler for WebSocketEventHandler {
    async fn handle(&self, event: &SystemEvent) -> SigmaXResult<()> {
        // 转换为实时事件
        if let Some(realtime_event) = self.convert_to_realtime_event(event).await {
            // 处理实时事件推送
            self.handle_realtime_event(realtime_event).await?;
        }
        
        Ok(())
    }
    
    fn event_types(&self) -> Vec<EventType> {
        vec![
            EventType::OrderCreated,
            EventType::OrderUpdated,
            EventType::OrderCancelled,
            EventType::TradeExecuted,
            EventType::StrategyStarted,
            EventType::StrategyStoped,
            EventType::RiskAlert,
            EventType::EngineCreated,
            EventType::EngineStarted,
            EventType::EngineStopped,
            EventType::EngineError,
        ]
    }
    
    fn name(&self) -> &str {
        "WebSocketEventHandler"
    }
    
    fn is_enabled(&self) -> bool {
        true
    }
}

/// 实时数据批处理器
pub struct RealtimeBatchProcessor {
    pending_events: HashMap<String, Vec<RealtimeEventType>>,
    batch_size: usize,
    last_flush: std::time::Instant,
    flush_interval: Duration,
}

impl RealtimeBatchProcessor {
    pub fn new() -> Self {
        Self {
            pending_events: HashMap::new(),
            batch_size: 50, // 每批50个事件
            last_flush: std::time::Instant::now(),
            flush_interval: Duration::from_millis(100), // 100ms刷新间隔
        }
    }
    
    /// 添加事件到批处理队列
    pub async fn add_event(&mut self, event: RealtimeEventType) -> SigmaXResult<()> {
        let channel = event.get_channel().to_string();
        self.pending_events
            .entry(channel)
            .or_insert_with(Vec::new)
            .push(event);
        
        Ok(())
    }
    
    /// 检查是否应该刷新批处理
    pub fn should_flush(&self) -> bool {
        // 检查批次大小
        let total_events: usize = self.pending_events.values().map(|v| v.len()).sum();
        if total_events >= self.batch_size {
            return true;
        }
        
        // 检查时间间隔
        if self.last_flush.elapsed() >= self.flush_interval {
            return true;
        }
        
        false
    }
    
    /// 刷新批处理队列
    pub async fn flush(&mut self) -> HashMap<String, Vec<RealtimeEventType>> {
        let events = std::mem::take(&mut self.pending_events);
        self.last_flush = std::time::Instant::now();
        events
    }
}

/// 实时事件统计
#[derive(Debug, Clone)]
pub struct RealtimeEventStats {
    pub total_events: u64,
    pub events_by_type: HashMap<String, u64>,
    pub events_by_channel: HashMap<String, u64>,
    pub high_priority_events: u64,
    pub batched_events: u64,
    pub last_event_time: Option<chrono::DateTime<Utc>>,
}

impl Default for RealtimeEventStats {
    fn default() -> Self {
        Self {
            total_events: 0,
            events_by_type: HashMap::new(),
            events_by_channel: HashMap::new(),
            high_priority_events: 0,
            batched_events: 0,
            last_event_time: None,
        }
    }
}

impl RealtimeEventStats {
    /// 更新统计信息
    pub fn update(&mut self, event: &RealtimeEventType) {
        self.total_events += 1;
        
        let event_name = event.get_event_name();
        *self.events_by_type.entry(event_name.to_string()).or_insert(0) += 1;
        
        let channel = event.get_channel();
        *self.events_by_channel.entry(channel.to_string()).or_insert(0) += 1;
        
        if event.is_high_priority() {
            self.high_priority_events += 1;
        } else {
            self.batched_events += 1;
        }
        
        self.last_event_time = Some(Utc::now());
    }
}
