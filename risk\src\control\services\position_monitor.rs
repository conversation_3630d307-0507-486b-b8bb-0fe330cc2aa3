//! 持仓监控服务
//!
//! 负责监控持仓状态，检查持仓限制和风险

use sigmax_interfaces::risk::{Portfolio, RiskResult};
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use std::collections::HashMap;

/// 持仓监控配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PositionMonitorConfig {
    /// 最大单一资产持仓比例
    pub max_single_asset_ratio: Decimal,
    /// 最大总持仓价值
    pub max_total_position_value: Decimal,
    /// 最大杠杆倍数
    pub max_leverage: Decimal,
    /// 风险阈值
    pub risk_threshold: Decimal,
    /// 是否启用集中度风险检查
    pub enable_concentration_check: bool,
    /// 是否启用相关性风险检查
    pub enable_correlation_check: bool,
}

impl Default for PositionMonitorConfig {
    fn default() -> Self {
        Self {
            max_single_asset_ratio: Decimal::from_str_exact("0.3").unwrap(), // 30%
            max_total_position_value: Decimal::from(10_000_000),
            max_leverage: Decimal::from(5),
            risk_threshold: Decimal::from_str_exact("0.8").unwrap(), // 80%
            enable_concentration_check: true,
            enable_correlation_check: true,
        }
    }
}

/// 持仓监控服务
pub struct PositionMonitor {
    config: PositionMonitorConfig,
}

impl PositionMonitor {
    /// 创建新的持仓监控器
    pub fn new(config: PositionMonitorConfig) -> Self {
        Self { config }
    }

    /// 使用默认配置创建持仓监控器
    pub fn with_default_config() -> Self {
        Self::new(PositionMonitorConfig::default())
    }

    /// 检查持仓状态
    pub async fn check_position_status(&self, portfolio: &Portfolio) -> PositionStatus {
        let mut status = PositionStatus {
            level: PositionRiskLevel::Safe,
            message: "Position status healthy".to_string(),
            details: HashMap::new(),
            checked_at: Utc::now(),
        };

        // 1. 检查单一资产持仓比例
        if let Err(error) = self.check_single_asset_concentration(portfolio) {
            status.level = PositionRiskLevel::Critical;
            status.message = error.to_string();
            status.details.insert(
                "concentration_check".to_string(),
                serde_json::json!({
                    "passed": false,
                    "reason": error.to_string()
                })
            );
            return status;
        }

        // 2. 检查总持仓价值
        if let Err(error) = self.check_total_position_value(portfolio) {
            status.level = PositionRiskLevel::Dangerous;
            status.message = error.to_string();
            status.details.insert(
                "total_value_check".to_string(),
                serde_json::json!({
                    "passed": false,
                    "reason": error.to_string()
                })
            );
            return status;
        }

        // 3. 检查杠杆倍数
        if let Err(error) = self.check_leverage(portfolio) {
            status.level = PositionRiskLevel::Critical;
            status.message = error.to_string();
            status.details.insert(
                "leverage_check".to_string(),
                serde_json::json!({
                    "passed": false,
                    "reason": error.to_string()
                })
            );
            return status;
        }

        // 4. 检查风险指标
        if let Err(error) = self.check_risk_metrics(portfolio).await {
            let risk_score = self.calculate_simple_risk_score(portfolio);
            status.level = if risk_score > Decimal::from_str_exact("0.9").unwrap() {
                PositionRiskLevel::Critical
            } else if risk_score > Decimal::from_str_exact("0.7").unwrap() {
                PositionRiskLevel::Dangerous
            } else {
                PositionRiskLevel::Warning
            };
            status.message = error.to_string();
            status.details.insert(
                "risk_metrics_check".to_string(),
                serde_json::json!({
                    "passed": false,
                    "reason": error.to_string(),
                    "risk_score": risk_score
                })
            );
            return status;
        }

        // 添加成功的检查详情
        status.details.insert(
            "concentration_check".to_string(),
            serde_json::json!({"passed": true})
        );
        status.details.insert(
            "total_value_check".to_string(),
            serde_json::json!({"passed": true})
        );
        status.details.insert(
            "leverage_check".to_string(),
            serde_json::json!({"passed": true})
        );

        status
    }

    /// 检查单一资产持仓集中度
    fn check_single_asset_concentration(&self, portfolio: &Portfolio) -> Result<(), PositionMonitorError> {
        if !self.config.enable_concentration_check {
            return Ok(());
        }

        let total_value = portfolio.total_value;
        if total_value <= Decimal::ZERO {
            return Ok(());
        }

        for balance in &portfolio.balances {
            let asset_value = balance.total * balance.price.unwrap_or(Decimal::ONE);
            let asset_ratio = asset_value / total_value;

            if asset_ratio > self.config.max_single_asset_ratio {
                return Err(PositionMonitorError::MonitoringFailed {
                    reason: format!(
                        "Asset {} concentration {}% exceeds maximum allowed {}%",
                        balance.asset,
                        asset_ratio * Decimal::from(100),
                        self.config.max_single_asset_ratio * Decimal::from(100)
                    ),
                });
            }
        }

        Ok(())
    }

    /// 检查总持仓价值
    fn check_total_position_value(&self, portfolio: &Portfolio) -> Result<(), PositionMonitorError> {
        if portfolio.total_value > self.config.max_total_position_value {
            return Err(PositionMonitorError::MonitoringFailed {
                reason: format!(
                    "Total position value {} exceeds maximum allowed {}",
                    portfolio.total_value, self.config.max_total_position_value
                ),
            });
        }

        Ok(())
    }

    /// 检查杠杆倍数
    fn check_leverage(&self, portfolio: &Portfolio) -> Result<(), PositionMonitorError> {
        // 计算总杠杆倍数
        let total_borrowed = portfolio.balances
            .iter()
            .filter(|b| b.borrowed > Decimal::ZERO)
            .map(|b| b.borrowed * b.price.unwrap_or(Decimal::ONE))
            .sum::<Decimal>();

        let total_equity = portfolio.balances
            .iter()
            .map(|b| b.available * b.price.unwrap_or(Decimal::ONE))
            .sum::<Decimal>();

        if total_equity > Decimal::ZERO {
            let leverage = (total_borrowed + total_equity) / total_equity;

            if leverage > self.config.max_leverage {
                return Err(PositionMonitorError::MonitoringFailed {
                    reason: format!(
                        "Leverage {}x exceeds maximum allowed {}x",
                        leverage, self.config.max_leverage
                    ),
                });
            }
        }

        Ok(())
    }

    /// 检查风险指标
    async fn check_risk_metrics(&self, portfolio: &Portfolio) -> Result<(), PositionMonitorError> {
        // 计算简单的风险指标
        let risk_score = self.calculate_simple_risk_score(portfolio);

        if risk_score > self.config.risk_threshold {
            return Err(PositionMonitorError::MonitoringFailed {
                reason: format!(
                    "Risk score {} exceeds threshold {}",
                    risk_score, self.config.risk_threshold
                ),
            });
        }

        Ok(())
    }

    /// 计算简单风险评分
    fn calculate_simple_risk_score(&self, portfolio: &Portfolio) -> Decimal {
        let mut risk_score = Decimal::ZERO;

        // 基于持仓集中度的风险
        let total_value = portfolio.total_value;
        if total_value > Decimal::ZERO {
            for balance in &portfolio.balances {
                let asset_value = balance.total * balance.price.unwrap_or(Decimal::ONE);
                let asset_ratio = asset_value / total_value;

                // 集中度越高，风险越大
                if asset_ratio > Decimal::from_str_exact("0.1").unwrap() {
                    risk_score += asset_ratio * Decimal::from_str_exact("0.5").unwrap();
                }
            }
        }

        // 基于杠杆的风险
        let total_borrowed = portfolio.balances
            .iter()
            .filter(|b| b.borrowed > Decimal::ZERO)
            .map(|b| b.borrowed * b.price.unwrap_or(Decimal::ONE))
            .sum::<Decimal>();

        let total_equity = portfolio.balances
            .iter()
            .map(|b| b.available * b.price.unwrap_or(Decimal::ONE))
            .sum::<Decimal>();

        if total_equity > Decimal::ZERO {
            let leverage = total_borrowed / total_equity;
            risk_score += leverage * Decimal::from_str_exact("0.1").unwrap();
        }

        // 确保风险评分在 0-1 之间
        risk_score.min(Decimal::ONE).max(Decimal::ZERO)
    }

    /// 获取持仓摘要
    pub fn get_position_summary(&self, portfolio: &Portfolio) -> PositionSummary {
        let total_assets = portfolio.balances.len();
        let total_value = portfolio.total_value;

        let largest_position = portfolio.balances
            .iter()
            .max_by(|a, b| {
                let a_value = a.total * a.price.unwrap_or(Decimal::ONE);
                let b_value = b.total * b.price.unwrap_or(Decimal::ONE);
                a_value.cmp(&b_value)
            });

        let largest_position_ratio = if let Some(position) = largest_position {
            if total_value > Decimal::ZERO {
                let position_value = position.total * position.price.unwrap_or(Decimal::ONE);
                position_value / total_value
            } else {
                Decimal::ZERO
            }
        } else {
            Decimal::ZERO
        };

        PositionSummary {
            total_assets,
            total_value,
            largest_position_asset: largest_position.map(|p| p.asset.clone()),
            largest_position_ratio,
            risk_score: self.calculate_simple_risk_score(portfolio),
            updated_at: Utc::now(),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PositionStatus {
    pub level: PositionRiskLevel,
    pub message: String,
    pub details: HashMap<String, serde_json::Value>,
    pub checked_at: DateTime<Utc>,
}

impl PositionStatus {
    /// 转换为风控结果
    pub fn to_risk_result(&self) -> RiskResult {
        match self.level {
            PositionRiskLevel::Safe => RiskResult::Approved,
            PositionRiskLevel::Warning => RiskResult::RequiresApproval(self.message.clone()),
            PositionRiskLevel::Dangerous | PositionRiskLevel::Critical => {
                RiskResult::Rejected(self.message.clone())
            }
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PositionRiskLevel {
    Safe,
    Warning,
    Dangerous,
    Critical,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PositionSummary {
    pub total_assets: usize,
    pub total_value: Decimal,
    pub largest_position_asset: Option<String>,
    pub largest_position_ratio: Decimal,
    pub risk_score: Decimal,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, thiserror::Error)]
pub enum PositionMonitorError {
    #[error("Position monitoring failed: {reason}")]
    MonitoringFailed { reason: String },

    #[error("Configuration error: {message}")]
    ConfigurationError { message: String },

    #[error("Data unavailable: {missing}")]
    DataUnavailable { missing: String },
}