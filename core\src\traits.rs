//! 核心trait定义
//!
//! 重构后的trait定义，符合设计原则：
//! - 高内聚，低耦合
//! - 关注点分离
//! - 面向接口设计
//! - 可测试性设计
//! - 简洁与可演化性

use async_trait::async_trait;
use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;
use serde::{Serialize, Deserialize};
use uuid::Uuid;

use crate::{
    errors::SigmaXResult, Balance, Candle, ExchangeId, Order, OrderBook, OrderId, OrderStatus, TradingPair,
    Trade, EngineType, Quantity, Amount
};

// ============================================================================
// 🧠 核心风控接口 - 符合设计原则的三层架构
// ============================================================================

/// RiskEngine trait - 领域核心抽象
///
/// 设计原则体现：
/// - 高内聚：风控核心逻辑集中
/// - 关注点分离：只关注风控决策
/// - 面向接口：抽象业务能力
/// - 简洁性：最小化接口设计
#[async_trait]
pub trait RiskEngine: Send + Sync {
    /// 核心风控决策 - 返回详细结果
    async fn check_order_risk(&self, order: &Order, strategy_type: Option<&str>) -> SigmaXResult<RiskCheckResult>;

    /// 持仓风控检查 - 返回详细结果
    async fn check_position_risk(&self, balances: &[Balance], strategy_type: Option<&str>) -> SigmaXResult<RiskCheckResult>;

    /// 规则管理
    async fn reload_rules(&self) -> SigmaXResult<()>;

    /// 风险指标获取
    async fn get_risk_metrics(&self) -> SigmaXResult<RiskMetrics>;
}

/// EngineRiskAdapter trait - 适配器抽象
///
/// 设计原则体现：
/// - 低耦合：解耦引擎与风控
/// - 关注点分离：专注适配逻辑
/// - 面向接口：统一适配器契约
/// - 可演化：易于添加新适配器
#[async_trait]
pub trait EngineRiskAdapter: Send + Sync {
    /// 适配后的风控检查 - 简化返回
    async fn check_order_risk(&self, order: &Order) -> SigmaXResult<bool>;

    /// 适配后的持仓检查 - 简化返回
    async fn check_position_risk(&self, balances: &[Balance]) -> SigmaXResult<bool>;

    /// 引擎类型标识
    fn engine_type(&self) -> EngineType;

    /// 适配器性能指标
    async fn get_metrics(&self) -> SigmaXResult<AdapterMetrics>;

    /// 用于类型转换的方法
    fn as_any(&self) -> &dyn std::any::Any;
}

/// RiskManager trait - 兼容性抽象 (保持向后兼容)
///
/// 设计原则体现：
/// - 简洁性：最小化接口复杂度
/// - 关注点分离：只关注基础检查
/// - 面向接口：稳定的公共契约
/// - 可演化：向后兼容保证
#[async_trait]
pub trait RiskManager: Send + Sync {
    /// 简化的风控检查
    async fn check_order_risk(&self, order: &Order) -> SigmaXResult<bool>;

    /// 简化的持仓检查
    async fn check_position_risk(&self, balances: &[Balance]) -> SigmaXResult<bool>;

    /// 业务辅助方法
    async fn get_max_order_size(&self, trading_pair: &TradingPair) -> SigmaXResult<Quantity>;
}

// ============================================================================
// 🔌 横切关注点服务接口 - 基础设施抽象
// ============================================================================

/// CacheService trait - 缓存抽象
///
/// 设计原则：面向接口设计，支持多种缓存实现
#[async_trait]
pub trait CacheService: Send + Sync {
    /// 获取缓存值
    async fn get<T>(&self, key: &str) -> SigmaXResult<Option<T>>
    where
        T: serde::de::DeserializeOwned + Send;

    /// 设置缓存值
    async fn set<T>(&self, key: &str, value: T, ttl: Duration) -> SigmaXResult<()>
    where
        T: serde::Serialize + Send;

    /// 失效缓存
    async fn invalidate(&self, pattern: &str) -> SigmaXResult<()>;

    /// 清空缓存
    async fn clear(&self) -> SigmaXResult<()>;
}

/// MetricsCollector trait - 指标抽象
///
/// 设计原则：观察者模式，支持多种指标收集器
#[async_trait]
pub trait MetricsCollector: Send + Sync {
    /// 记录风控检查结果
    async fn record_risk_check(&self, passed: bool);

    /// 记录缓存命中
    async fn record_cache_hit(&self);

    /// 记录延迟
    async fn record_latency(&self, operation: &str, duration: Duration);

    /// 获取指标
    async fn get_metrics(&self) -> SigmaXResult<HashMap<String, f64>>;
}

/// ConfigService trait - 配置抽象
///
/// 设计原则：配置管理的统一接口
#[async_trait]
pub trait ConfigService: Send + Sync {
    /// 获取配置
    async fn get_config<T>(&self, key: &str) -> SigmaXResult<Option<T>>
    where
        T: serde::de::DeserializeOwned + Send;

    /// 重新加载配置
    async fn reload_config(&self) -> SigmaXResult<()>;

    /// 验证配置
    async fn validate_config(&self, config: &serde_json::Value) -> SigmaXResult<()>;

    /// 监听配置变更
    async fn watch_changes(&self) -> SigmaXResult<tokio::sync::mpsc::Receiver<ConfigChangeEvent>>;
}

// ============================================================================
// 🎯 风控相关数据类型
// ============================================================================

/// 风控检查结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskCheckResult {
    /// 是否通过检查
    pub passed: bool,
    /// 风险评分 (0.0-1.0)
    pub risk_score: Option<f64>,
    /// 失败的规则列表
    pub failed_rules: Vec<FailedRule>,
    /// 检查耗时 (毫秒)
    pub execution_time_ms: u64,
    /// 执行的规则数量
    pub rules_executed: usize,
    /// 额外信息
    pub metadata: HashMap<String, serde_json::Value>,
}

impl RiskCheckResult {
    /// 创建通过检查的结果
    pub fn pass() -> Self {
        Self {
            passed: true,
            risk_score: Some(0.0),
            failed_rules: Vec::new(),
            execution_time_ms: 0,
            rules_executed: 0,
            metadata: HashMap::new(),
        }
    }

    /// 创建失败检查的结果
    pub fn fail(reason: String) -> Self {
        Self {
            passed: false,
            risk_score: Some(1.0),
            failed_rules: vec![FailedRule {
                rule_id: "unknown".to_string(),
                rule_name: "unknown".to_string(),
                failure_reason: reason,
                priority: 1,
            }],
            execution_time_ms: 0,
            rules_executed: 1,
            metadata: HashMap::new(),
        }
    }
}

/// 失败的规则信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FailedRule {
    /// 规则ID
    pub rule_id: String,
    /// 规则名称
    pub rule_name: String,
    /// 失败原因
    pub failure_reason: String,
    /// 规则优先级
    pub priority: i32,
}

/// 风险指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskMetrics {
    /// 总检查次数
    pub total_checks: u64,
    /// 通过次数
    pub passed_checks: u64,
    /// 失败次数
    pub failed_checks: u64,
    /// 平均执行时间 (毫秒)
    pub avg_execution_time_ms: f64,
    /// 最近更新时间
    pub last_updated: chrono::DateTime<chrono::Utc>,
}

/// 适配器指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AdapterMetrics {
    /// 引擎类型
    pub engine_type: EngineType,
    /// 缓存命中率
    pub cache_hit_rate: f64,
    /// 平均延迟 (毫秒)
    pub avg_latency_ms: f64,
    /// 吞吐量 (每秒请求数)
    pub throughput_rps: f64,
    /// 错误率
    pub error_rate: f64,
}

/// 配置变更事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConfigChangeEvent {
    /// 配置键
    pub key: String,
    /// 旧值
    pub old_value: Option<serde_json::Value>,
    /// 新值
    pub new_value: serde_json::Value,
    /// 变更时间
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

// ============================================================================
// 🏗️ 引擎接口 - 交易引擎抽象
// ============================================================================

/// TradingEngine trait - 引擎接口定义
///
/// 设计原则：统一的引擎生命周期管理
#[async_trait]
pub trait TradingEngine: Send + Sync {
    /// 获取引擎ID
    fn id(&self) -> crate::EngineId;

    /// 获取引擎类型
    fn engine_type(&self) -> EngineType;

    /// 获取引擎状态
    async fn get_status(&self) -> SigmaXResult<crate::EngineStatus>;

    /// 启动引擎
    async fn start(&self) -> SigmaXResult<()>;

    /// 停止引擎
    async fn stop(&self) -> SigmaXResult<()>;

    /// 暂停引擎
    async fn pause(&self) -> SigmaXResult<()>;

    /// 恢复引擎
    async fn resume(&self) -> SigmaXResult<()>;

    /// 获取引擎配置
    async fn get_config(&self) -> SigmaXResult<crate::EngineConfig>;

    /// 更新引擎配置
    async fn update_config(&self, config: crate::EngineConfig) -> SigmaXResult<()>;

    /// 获取引擎统计信息
    async fn get_statistics(&self) -> SigmaXResult<crate::EngineStatistics>;

    /// 用于类型转换的方法
    fn as_any(&self) -> &dyn std::any::Any;
}

// ============================================================================
// 📊 策略接口 - 策略管理抽象
// ============================================================================

/// Strategy trait - 策略接口定义
///
/// 设计原则：使用内部可变性设计，支持并发安全
#[async_trait]
pub trait Strategy: Send + Sync {
    /// 获取策略ID
    fn id(&self) -> crate::StrategyId;

    /// 获取策略名称
    fn name(&self) -> &str;

    /// 获取策略状态
    fn status(&self) -> crate::StrategyStatus;

    /// 初始化策略 - 使用内部可变性，不需要&mut self
    async fn initialize(&self) -> SigmaXResult<()>;

    /// 处理市场数据 - 使用内部可变性，不需要&mut self
    async fn on_market_data(&self, candle: &Candle) -> SigmaXResult<Vec<Order>>;

    /// 处理订单更新 - 使用内部可变性，不需要&mut self
    async fn on_order_update(&self, order: &Order) -> SigmaXResult<()>;

    /// 处理交易更新 - 使用内部可变性，不需要&mut self
    async fn on_trade(&self, trade: &Trade) -> SigmaXResult<()>;

    /// 停止策略 - 使用内部可变性，不需要&mut self
    async fn stop(&self) -> SigmaXResult<()>;

    /// 检查策略是否已初始化
    fn is_initialized(&self) -> bool;

    /// 检查策略是否正在运行
    fn is_running(&self) -> bool;
}

// ============================================================================
// 💾 数据持久化与验证 Traits (保留现有)
// ============================================================================

/// 数据验证trait
///
/// # 移植说明
/// 从 models_old.rs 移植，提供统一的数据验证接口
pub trait Validatable {
    /// 验证数据有效性，若无效则返回错误。
    fn validate(&self) -> SigmaXResult<()>;

    /// 获取所有验证错误的列表。
    fn validation_errors(&self) -> Vec<String>;
}

/// 数据库操作trait
///
/// # 移植说明
/// 从 models_old.rs 移植，提供统一的数据库操作接口
/// 这是一个通用的数据库操作trait，支持CRUD操作
#[async_trait::async_trait]
pub trait DatabaseOperations<T> {
    /// 保存到数据库
    async fn save(&self, db: &dyn DatabaseConnection) -> SigmaXResult<()>;

    /// 从数据库加载
    async fn load(id: Uuid, db: &dyn DatabaseConnection) -> SigmaXResult<Option<T>>;

    /// 更新数据库记录
    async fn update(&self, db: &dyn DatabaseConnection) -> SigmaXResult<()>;

    /// 从数据库删除
    async fn delete(id: Uuid, db: &dyn DatabaseConnection) -> SigmaXResult<()>;
}

/// 数据库连接trait
///
/// # 移植说明
/// 从 models_old.rs 移植，提供统一的数据库连接接口
/// 这是一个抽象的数据库连接接口，支持不同的数据库实现
#[async_trait::async_trait]
pub trait DatabaseConnection: Send + Sync {
    /// 执行查询
    async fn execute_query(&self, query: &str, params: &[String]) -> SigmaXResult<u64>;

    /// 查询单行
    async fn query_one(&self, query: &str, params: &[String]) -> SigmaXResult<Option<HashMap<String, serde_json::Value>>>;

    /// 查询多行
    async fn query_many(&self, query: &str, params: &[String]) -> SigmaXResult<Vec<HashMap<String, serde_json::Value>>>;
}



// ============================================================================
// 🏪 交易所和数据接口 (保留现有)
// ============================================================================

/// 交易所接口
#[async_trait]
pub trait Exchange: Send + Sync {
    /// 获取交易所ID
    fn id(&self) -> ExchangeId;

    /// 获取账户余额
    async fn get_balances(&self) -> SigmaXResult<Vec<Balance>>;

    /// 下单
    async fn place_order(&self, order: &Order) -> SigmaXResult<OrderId>;

    /// 取消订单
    async fn cancel_order(&self, order_id: OrderId) -> SigmaXResult<()>;

    /// 获取订单状态
    async fn get_order(&self, order_id: OrderId) -> SigmaXResult<Order>;

    /// 获取市场深度
    async fn get_order_book(&self, trading_pair: &TradingPair) -> SigmaXResult<OrderBook>;

    /// 获取K线数据
    async fn get_candles(
        &self,
        trading_pair: &TradingPair,
        timeframe: crate::TimeFrame,
        limit: Option<u32>,
    ) -> SigmaXResult<Vec<Candle>>;

    /// 获取交易历史
    async fn get_trades(&self, trading_pair: &TradingPair) -> SigmaXResult<Vec<Trade>>;
}

/// 数据提供者接口
#[async_trait]
pub trait DataProvider: Send + Sync {
    /// 获取实时价格
    async fn get_price(&self, trading_pair: &TradingPair) -> SigmaXResult<crate::Price>;

    /// 获取历史K线数据
    async fn get_historical_candles(
        &self,
        trading_pair: &TradingPair,
        timeframe: crate::TimeFrame,
        start_time: chrono::DateTime<chrono::Utc>,
        end_time: chrono::DateTime<chrono::Utc>,
    ) -> SigmaXResult<Vec<Candle>>;

    /// 订阅实时数据
    async fn subscribe_market_data(&self, trading_pair: &TradingPair) -> SigmaXResult<()>;
}

// ============================================================================
// 🔌 策略插件和服务接口 (保留现有)
// ============================================================================

/// 策略插件接口 - 用于无依赖策略接入
#[async_trait]
pub trait StrategyPlugin: Send + Sync {
    /// 获取策略插件信息
    fn plugin_info(&self) -> StrategyPluginInfo;

    /// 验证策略配置
    fn validate_config(&self, config: &serde_json::Value) -> SigmaXResult<()>;

    /// 创建策略实例
    async fn create_strategy(
        &self,
        config: serde_json::Value,
        services: Arc<dyn StrategyServiceContainer>,
    ) -> SigmaXResult<Arc<dyn Strategy>>;

    /// 获取配置模式（JSON Schema）
    fn config_schema(&self) -> serde_json::Value;

    /// 获取策略描述和帮助信息
    fn description(&self) -> &str;

    /// 获取策略标签
    fn tags(&self) -> Vec<String>;
}

/// 策略插件信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StrategyPluginInfo {
    /// 策略名称（唯一标识）
    pub name: String,
    /// 显示名称
    pub display_name: String,
    /// 版本
    pub version: String,
    /// 作者
    pub author: String,
    /// 策略类型
    pub strategy_type: String,
    /// 风险等级
    pub risk_level: RiskLevel,
    /// 推荐资金规模
    pub recommended_capital: Option<f64>,
    /// 支持的时间框架
    pub supported_timeframes: Vec<String>,
}

/// 风险等级
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum RiskLevel {
    Low,
    Medium,
    High,
    VeryHigh,
}

/// 订单执行接口 - 抽象订单管理功能
#[async_trait]
pub trait OrderExecutor: Send + Sync {
    /// 提交订单
    async fn submit_order(&self, order: Order) -> SigmaXResult<OrderId>;

    /// 取消订单
    async fn cancel_order(&self, order_id: OrderId) -> SigmaXResult<()>;

    /// 获取订单状态
    async fn get_order_status(&self, order_id: OrderId) -> SigmaXResult<OrderStatus>;

    /// 获取活跃订单
    async fn get_active_orders(&self) -> SigmaXResult<Vec<Order>>;
}

/// 策略服务容器接口 - 提供依赖注入
#[async_trait]
pub trait StrategyServiceContainer: Send + Sync {
    /// 获取投资组合管理器
    async fn portfolio_manager(&self) -> SigmaXResult<Arc<dyn PortfolioManager>>;

    /// 获取风险管理器
    async fn risk_manager(&self) -> SigmaXResult<Arc<dyn RiskManager>>;

    /// 获取订单执行器
    async fn order_executor(&self) -> SigmaXResult<Arc<dyn OrderExecutor>>;

    /// 获取数据提供者
    async fn data_provider(&self) -> SigmaXResult<Arc<dyn DataProvider>>;

    /// 获取事件总线
    async fn event_bus(&self) -> SigmaXResult<Arc<crate::EventBus>>;

    /// 获取交易对信息
    fn trading_pair(&self) -> &TradingPair;

    /// 获取交易所ID
    fn exchange_id(&self) -> &ExchangeId;

    /// 检查服务是否可用
    async fn health_check(&self) -> SigmaXResult<()>;
}



/// 投资组合管理接口
#[async_trait]
pub trait PortfolioManager: Send + Sync {
    /// 获取当前余额
    async fn get_balances(&self) -> SigmaXResult<HashMap<String, Balance>>;

    /// 更新余额
    async fn update_balance(&mut self, balance: Balance) -> SigmaXResult<()>;

    /// 计算总资产价值
    async fn calculate_total_value(&self) -> SigmaXResult<crate::Amount>;

    /// 获取PnL
    async fn get_pnl(&self) -> SigmaXResult<crate::Amount>;

    /// 🔥 新增：支持类型转换的方法
    fn as_any(&self) -> &dyn std::any::Any;
}

/// 增强的投资组合管理接口，支持并发安全的更新
#[async_trait]
pub trait EnhancedPortfolioManager: PortfolioManager {
    /// 🔥 根据交易更新投资组合（并发安全）
    async fn update_from_trade(&self, trade: &crate::Trade) -> SigmaXResult<()>;

    /// 🔥 更新当前价格（用于价值计算）
    async fn update_current_price(&self, asset: &str, price: crate::Amount) -> SigmaXResult<()>;

    /// 🔥 批量更新余额（并发安全）
    async fn batch_update_balances(&self, balances: HashMap<String, Balance>) -> SigmaXResult<()>;

    /// 🔥 获取特定资产余额
    async fn get_asset_balance(&self, asset: &str) -> SigmaXResult<Option<Balance>>;

    /// 🔥 检查资金充足性
    async fn check_sufficient_balance(&self, asset: &str, required_amount: crate::Amount) -> SigmaXResult<bool>;
}



/// 服务容器接口
#[async_trait]
pub trait ServiceContainer: Send + Sync {
    /// 获取数据提供者
    fn get_data_provider(&self) -> std::sync::Arc<dyn DataProvider>;

    /// 获取交易所管理器
    fn get_exchange_manager(&self) -> std::sync::Arc<dyn Exchange>;

    /// 获取风险管理器
    fn get_risk_manager(&self) -> std::sync::Arc<dyn RiskManager>;

    /// 创建投资组合管理器
    async fn create_portfolio_manager(&self, initial_capital: crate::Amount) -> SigmaXResult<std::sync::Arc<dyn PortfolioManager>>;

    /// 创建策略管理器
    async fn create_strategy_manager(&self) -> SigmaXResult<std::sync::Arc<dyn StrategyManager>>;
}

/// 策略管理器接口
#[async_trait]
pub trait StrategyManager: Send + Sync {
    /// 添加策略
    async fn add_strategy(&self, strategy: std::sync::Arc<dyn Strategy>) -> SigmaXResult<()>;

    /// 移除策略
    async fn remove_strategy(&self, strategy_id: crate::StrategyId) -> SigmaXResult<()>;

    /// 获取活跃策略
    async fn get_active_strategies(&self) -> SigmaXResult<Vec<std::sync::Arc<dyn Strategy>>>;

    /// 启动所有策略
    async fn start_all(&self) -> SigmaXResult<()>;

    /// 停止所有策略
    async fn stop_all(&self) -> SigmaXResult<()>;
}

// EngineRiskAdapter trait已在上面定义
