# SigmaX Trading System - 前端架构文档

## 概述

SigmaX前端采用现代化的Web技术栈，使用HTML5、CSS3、JavaScript ES6+和Tailwind CSS构建，提供响应式的用户界面和流畅的用户体验。

## 技术栈

- **HTML5**: 语义化标签，提供良好的结构和可访问性
- **CSS3**: 现代CSS特性，包括Grid、Flexbox、动画等
- **Tailwind CSS**: 实用优先的CSS框架，快速构建界面
- **JavaScript ES6+**: 现代JavaScript特性，模块化开发
- **Font Awesome**: 图标库，提供丰富的图标资源

## 文件结构

```
web/static/
├── index.html            # 主页面文件
├── demo.html             # 演示页面
├── health-test.html      # 健康检查测试页面
├── config.html           # 系统配置管理页面
├── css/
│   └── custom.css        # 自定义样式文件
├── js/
│   ├── app.js           # 主应用程序
│   ├── router.js        # 路由管理器
│   ├── components.js    # 页面组件
│   ├── config.js        # 系统配置管理
│   └── utils.js         # 工具函数
└── README_FRONTEND.md   # 前端文档
```

## 核心功能

### 1. 系统健康检查

应用启动时会自动进行系统健康检查：

```javascript
// 自动健康检查
const isReady = await Utils.waitForSystemReady(3, 3000);

// 手动健康检查
const health = await Utils.checkSystemHealth();
```

特性：
- **启动检查**: 页面加载时自动检查系统状态
- **重试机制**: 支持自动重试，最多3次
- **错误处理**: 连接失败时提供重试和离线模式选项
- **状态反馈**: 实时显示系统连接状态

### 2. API配置管理

统一的API路径管理：

```javascript
// API基础路径
Utils.API_BASE_URL = '/api/v1';

// 自动路径处理
Utils.request('/health');        // -> /api/v1/health
Utils.request('health');         // -> /api/v1/health
Utils.request('/api/v1/health'); // -> /api/v1/health (不变)
```

### 3. 响应式布局

- **侧边栏**: 固定位置，包含导航菜单
- **头部区域**: 显示页面标题、系统状态、用户信息
- **主内容区**: 动态加载页面内容
- **移动端适配**: 自动折叠侧边栏，优化移动端体验

### 2. 路由系统

基于Hash路由实现单页应用(SPA)功能：

```javascript
// 路由导航
router.navigate('dashboard');
router.navigate('backtest');

// 路由参数
const params = router.getRouteParams();
```

支持的路由：
- `#dashboard` - 仪表盘
- `#backtest` - 回测系统
- `#live-trading` - 实盘交易
- `#portfolio` - 投资组合
- `#strategies` - 策略配置
- `#risk-management` - 风险管理
- `#performance` - 绩效分析
- `#market-data` - 市场数据
- `#system-config` - 系统配置
- `#logs` - 系统日志

### 3. 组件系统

模块化的页面组件，支持动态加载：

```javascript
// 注册组件
window.PageComponents = {
    dashboard: () => `<div>仪表盘内容</div>`,
    backtest: () => `<div>回测内容</div>`
};
```

### 4. 工具函数

提供常用的工具函数：

```javascript
// 数字格式化
Utils.formatNumber(1234.56, 2); // "1,234.56"

// 货币格式化
Utils.formatCurrency(1234.56); // "$1,234.56"

// 时间格式化
Utils.formatTime(Date.now()); // "2024-01-01 12:00:00"

// 通知显示
Utils.showNotification('操作成功', 'success');

// HTTP请求 (自动添加API基础路径)
const data = await Utils.request('/health');
const data = await Utils.request('system/status');

// 系统健康检查
const health = await Utils.checkSystemHealth();
const isReady = await Utils.waitForSystemReady(5, 2000);
```

## 页面功能

### 仪表盘 (Dashboard)
- 系统状态概览
- 关键指标展示
- 实时行情信息
- 最近交易记录

### 回测系统 (Backtest)
- 回测参数配置
- 策略选择
- 回测结果展示
- 性能指标分析

### 实盘交易 (Live Trading)
- 交易控制面板
- 持仓信息展示
- 实时交易统计
- 风险监控

### 其他页面
- 投资组合管理
- 策略配置
- 风险管理
- 绩效分析
- 市场数据
- 系统配置
- 系统日志

## 样式系统

### 1. Tailwind CSS
使用Tailwind CSS作为主要的CSS框架，提供：
- 响应式设计工具类
- 颜色系统
- 间距系统
- 布局工具类

### 2. 自定义样式
在`css/custom.css`中定义：
- 渐变背景
- 动画效果
- 组件样式
- 响应式断点

### 3. 主题色彩
- 主色调: 蓝色到紫色渐变 (#3b82f6 → #8b5cf6)
- 成功色: 绿色 (#10b981)
- 警告色: 黄色 (#f59e0b)
- 危险色: 红色 (#ef4444)
- 中性色: 灰色系列

## 交互特性

### 1. 动画效果
- 页面切换动画
- 按钮悬停效果
- 加载动画
- 状态指示器脉冲动画

### 2. 响应式交互
- 移动端侧边栏切换
- 触摸友好的按钮大小
- 自适应表格布局

### 3. 用户反馈
- 操作成功/失败通知
- 加载状态指示
- 确认对话框
- 工具提示

## 使用说明

### 1. 页面访问

- **演示页面**: `demo.html` - 查看前端架构演示和功能介绍
- **主应用**: `index.html` - 完整的交易系统界面
- **健康检查**: `health-test.html` - 测试API连接和系统状态
- **系统配置**: `config.html` - 管理系统连接配置

### 2. 系统启动流程

1. **加载页面**: 访问 `index.html`
2. **系统初始化**: 显示初始化界面和进度条
3. **健康检查**: 自动调用 `/api/v1/health` 接口
4. **状态处理**:
   - 成功: 进入正常界面
   - 失败: 显示错误页面，提供重试和离线模式选项

### 3. 配置管理

系统提供了完整的配置管理功能，支持多种配置方式：

#### 默认配置
- **系统地址**: `127.0.0.1:8080`
- **API路径**: `/api/v1`
- **完整URL**: `http://127.0.0.1:8080/api/v1`

#### 配置方式

**1. 图形界面配置**
访问 `config.html` 页面进行可视化配置：
- 服务器地址和端口设置
- API路径配置
- 连接测试功能
- 快速配置预设

**2. 代码配置**
在 `js/config.js` 中修改：
```javascript
window.SigmaXConfig.system.host = '127.0.0.1';
window.SigmaXConfig.system.port = 8080;
window.SigmaXConfig.api.basePath = '/api/v1';
```

**3. URL参数配置**
通过URL参数临时覆盖配置：
```
index.html?host=*************&port=9090&debug=true
```

**4. 本地存储配置**
配置会自动保存到浏览器本地存储，下次访问时自动加载。

### 4. 健康检查接口

系统会调用 `http://127.0.0.1:8080/api/v1/health` 进行健康检查。

期望的响应格式：

```json
// 成功响应
{
  "status": "ok",
  "healthy": true,
  "timestamp": "2024-01-01T12:00:00Z"
}

// 或简单格式
{
  "status": "ok"
}
```

接口要求：
- **URL**: `http://127.0.0.1:8080/api/v1/health`
- **方法**: GET
- **响应**: JSON格式
- **状态码**: 200 表示成功

## 开发指南

### 1. 添加新页面

1. 在`js/components.js`中添加页面组件：
```javascript
window.PageComponents.newPage = () => `
    <div class="bg-white rounded-lg shadow-sm p-6">
        <h3 class="text-lg font-semibold mb-4">新页面</h3>
        <!-- 页面内容 -->
    </div>
`;
```

2. 在`js/router.js`中注册路由：
```javascript
router.register('new-page', async (route) => {
    console.log('加载新页面');
}, { title: '新页面' });
```

3. 在侧边栏菜单中添加链接：
```html
<li>
    <a href="#new-page" class="menu-item" data-page="new-page">
        <i class="fas fa-icon"></i>
        <span>新页面</span>
    </a>
</li>
```

### 2. 自定义样式

在`css/custom.css`中添加自定义样式：
```css
.custom-component {
    /* 自定义样式 */
}
```

### 3. API集成

使用工具函数进行API调用：
```javascript
// GET请求
const data = await Utils.request('/api/endpoint');

// POST请求
const result = await Utils.request('/api/endpoint', {
    method: 'POST',
    body: { key: 'value' }
});
```

## 性能优化

1. **懒加载**: 页面组件按需加载
2. **缓存策略**: 合理使用浏览器缓存
3. **代码分割**: 将功能模块分离
4. **资源压缩**: 生产环境压缩CSS和JS
5. **图片优化**: 使用适当的图片格式和大小

## 浏览器兼容性

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 部署说明

1. 确保所有静态文件在`web/static/`目录下
2. 配置Web服务器正确处理静态文件
3. 设置适当的缓存策略
4. 启用Gzip压缩

## 维护和更新

1. 定期更新依赖库版本
2. 监控性能指标
3. 收集用户反馈
4. 持续优化用户体验

## 联系信息

如有问题或建议，请联系开发团队。
