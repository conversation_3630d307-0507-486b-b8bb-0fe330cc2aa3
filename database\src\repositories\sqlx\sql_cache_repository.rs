//! SQL 缓存配置仓储实现
//!
//! 基于 SQLx 的缓存配置数据访问实现

use std::sync::Arc;
use async_trait::async_trait;
use chrono::{DateTime, Utc};
use sqlx::Row;
use sigmax_core::{SigmaXResult, SigmaXError, CacheConfig};
use uuid::Uuid;

use crate::DatabaseManager;
use crate::repositories::traits::cache_repository::{
    CacheRepository, CacheStatsRecord, CachePerformanceRecord,
    CacheHealthRecord, CacheAlertRecord
};

/// SQL 缓存仓储实现
pub struct SqlCacheRepository {
    db: Arc<DatabaseManager>,
}

impl SqlCacheRepository {
    /// 创建新的 SQL 缓存仓储实例
    pub fn new(db: Arc<DatabaseManager>) -> Self {
        Self { db }
    }

    /// 将数据库行转换为缓存统计记录
    fn row_to_cache_stats(&self, row: sqlx::postgres::PgRow) -> SigmaXResult<CacheStatsRecord> {
        Ok(CacheStatsRecord {
            id: row.try_get("id")
                .map_err(|e| SigmaXError::Database(format!("获取id失败: {}", e)))?,
            timestamp: row.try_get("timestamp")
                .map_err(|e| SigmaXError::Database(format!("获取timestamp失败: {}", e)))?,
            cache_type: row.try_get("cache_type")
                .map_err(|e| SigmaXError::Database(format!("获取cache_type失败: {}", e)))?,
            total_keys: row.try_get::<i64, _>("total_keys")
                .map_err(|e| SigmaXError::Database(format!("获取total_keys失败: {}", e)))? as u64,
            memory_usage_mb: row.try_get("memory_usage_mb")
                .map_err(|e| SigmaXError::Database(format!("获取memory_usage_mb失败: {}", e)))?,
            memory_limit_mb: row.try_get("memory_limit_mb")
                .map_err(|e| SigmaXError::Database(format!("获取memory_limit_mb失败: {}", e)))?,
            memory_usage_percentage: row.try_get("memory_usage_percentage")
                .map_err(|e| SigmaXError::Database(format!("获取memory_usage_percentage失败: {}", e)))?,
            hit_rate: row.try_get("hit_rate")
                .map_err(|e| SigmaXError::Database(format!("获取hit_rate失败: {}", e)))?,
            miss_rate: row.try_get("miss_rate")
                .map_err(|e| SigmaXError::Database(format!("获取miss_rate失败: {}", e)))?,
            total_operations: row.try_get::<i64, _>("total_operations")
                .map_err(|e| SigmaXError::Database(format!("获取total_operations失败: {}", e)))? as u64,
            hits: row.try_get::<i64, _>("hits")
                .map_err(|e| SigmaXError::Database(format!("获取hits失败: {}", e)))? as u64,
            misses: row.try_get::<i64, _>("misses")
                .map_err(|e| SigmaXError::Database(format!("获取misses失败: {}", e)))? as u64,
            evictions: row.try_get::<i64, _>("evictions")
                .map_err(|e| SigmaXError::Database(format!("获取evictions失败: {}", e)))? as u64,
            expired_keys: row.try_get::<i64, _>("expired_keys")
                .map_err(|e| SigmaXError::Database(format!("获取expired_keys失败: {}", e)))? as u64,
            average_ttl_seconds: row.try_get("average_ttl_seconds")
                .map_err(|e| SigmaXError::Database(format!("获取average_ttl_seconds失败: {}", e)))?,
            uptime_seconds: row.try_get::<i64, _>("uptime_seconds")
                .map_err(|e| SigmaXError::Database(format!("获取uptime_seconds失败: {}", e)))? as u64,
            created_at: row.try_get("created_at")
                .map_err(|e| SigmaXError::Database(format!("获取created_at失败: {}", e)))?,
            updated_at: row.try_get("updated_at")
                .map_err(|e| SigmaXError::Database(format!("获取updated_at失败: {}", e)))?,
        })
    }

    /// 将数据库行转换为缓存性能记录
    fn row_to_cache_performance(&self, row: sqlx::postgres::PgRow) -> SigmaXResult<CachePerformanceRecord> {
        Ok(CachePerformanceRecord {
            id: row.try_get("id")
                .map_err(|e| SigmaXError::Database(format!("获取id失败: {}", e)))?,
            timestamp: row.try_get("timestamp")
                .map_err(|e| SigmaXError::Database(format!("获取timestamp失败: {}", e)))?,
            cache_type: row.try_get("cache_type")
                .map_err(|e| SigmaXError::Database(format!("获取cache_type失败: {}", e)))?,
            average_response_time_ms: row.try_get("average_response_time_ms")
                .map_err(|e| SigmaXError::Database(format!("获取average_response_time_ms失败: {}", e)))?,
            p95_response_time_ms: row.try_get("p95_response_time_ms")
                .map_err(|e| SigmaXError::Database(format!("获取p95_response_time_ms失败: {}", e)))?,
            p99_response_time_ms: row.try_get("p99_response_time_ms")
                .map_err(|e| SigmaXError::Database(format!("获取p99_response_time_ms失败: {}", e)))?,
            throughput_ops_per_second: row.try_get("throughput_ops_per_second")
                .map_err(|e| SigmaXError::Database(format!("获取throughput_ops_per_second失败: {}", e)))?,
            error_rate: row.try_get("error_rate")
                .map_err(|e| SigmaXError::Database(format!("获取error_rate失败: {}", e)))?,
            created_at: row.try_get("created_at")
                .map_err(|e| SigmaXError::Database(format!("获取created_at失败: {}", e)))?,
        })
    }

    /// 将数据库行转换为缓存健康记录
    fn row_to_cache_health(&self, row: sqlx::postgres::PgRow) -> SigmaXResult<CacheHealthRecord> {
        Ok(CacheHealthRecord {
            id: row.try_get("id")
                .map_err(|e| SigmaXError::Database(format!("获取id失败: {}", e)))?,
            timestamp: row.try_get("timestamp")
                .map_err(|e| SigmaXError::Database(format!("获取timestamp失败: {}", e)))?,
            status: row.try_get("status")
                .map_err(|e| SigmaXError::Database(format!("获取status失败: {}", e)))?,
            memory_pressure_level: row.try_get("memory_pressure_level")
                .map_err(|e| SigmaXError::Database(format!("获取memory_pressure_level失败: {}", e)))?,
            memory_usage_percentage: row.try_get("memory_usage_percentage")
                .map_err(|e| SigmaXError::Database(format!("获取memory_usage_percentage失败: {}", e)))?,
            eviction_rate: row.try_get("eviction_rate")
                .map_err(|e| SigmaXError::Database(format!("获取eviction_rate失败: {}", e)))?,
            fragmentation_ratio: row.try_get("fragmentation_ratio")
                .map_err(|e| SigmaXError::Database(format!("获取fragmentation_ratio失败: {}", e)))?,
            issues: row.try_get("issues")
                .map_err(|e| SigmaXError::Database(format!("获取issues失败: {}", e)))?,
            recommendations: row.try_get("recommendations")
                .map_err(|e| SigmaXError::Database(format!("获取recommendations失败: {}", e)))?,
            created_at: row.try_get("created_at")
                .map_err(|e| SigmaXError::Database(format!("获取created_at失败: {}", e)))?,
        })
    }

    /// 将数据库行转换为缓存告警记录
    fn row_to_cache_alert(&self, row: sqlx::postgres::PgRow) -> SigmaXResult<CacheAlertRecord> {
        Ok(CacheAlertRecord {
            id: row.try_get("id")
                .map_err(|e| SigmaXError::Database(format!("获取id失败: {}", e)))?,
            timestamp: row.try_get("timestamp")
                .map_err(|e| SigmaXError::Database(format!("获取timestamp失败: {}", e)))?,
            rule_name: row.try_get("rule_name")
                .map_err(|e| SigmaXError::Database(format!("获取rule_name失败: {}", e)))?,
            severity: row.try_get("severity")
                .map_err(|e| SigmaXError::Database(format!("获取severity失败: {}", e)))?,
            condition: row.try_get("condition")
                .map_err(|e| SigmaXError::Database(format!("获取condition失败: {}", e)))?,
            current_value: row.try_get("current_value")
                .map_err(|e| SigmaXError::Database(format!("获取current_value失败: {}", e)))?,
            threshold_value: row.try_get("threshold_value")
                .map_err(|e| SigmaXError::Database(format!("获取threshold_value失败: {}", e)))?,
            description: row.try_get("description")
                .map_err(|e| SigmaXError::Database(format!("获取description失败: {}", e)))?,
            resolved: row.try_get("resolved")
                .map_err(|e| SigmaXError::Database(format!("获取resolved失败: {}", e)))?,
            resolved_at: row.try_get("resolved_at")
                .map_err(|e| SigmaXError::Database(format!("获取resolved_at失败: {}", e)))?,
            created_at: row.try_get("created_at")
                .map_err(|e| SigmaXError::Database(format!("获取created_at失败: {}", e)))?,
            updated_at: row.try_get("updated_at")
                .map_err(|e| SigmaXError::Database(format!("获取updated_at失败: {}", e)))?,
        })
    }

    /// 构建强类型配置
    async fn build_typed_config(&self, namespace: &str) -> SigmaXResult<CacheConfig> {
        let pool = self.db.pool();

        let rows = sqlx::query(
            "SELECT key, value FROM system_config WHERE key LIKE $1"
        )
        .bind(format!("{}%", namespace))
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::Database(e.to_string()))?;

        if rows.is_empty() {
            return Ok(CacheConfig::default());
        }

        // 构建配置对象
        let mut config_map = serde_json::Map::new();

        // 🔥 修复：确保必需字段存在
        // 如果数据库中没有name字段，使用默认值
        config_map.insert("name".to_string(), serde_json::Value::String("default".to_string()));
        config_map.insert("description".to_string(), serde_json::Value::String("系统缓存配置".to_string()));
        config_map.insert("enabled".to_string(), serde_json::Value::Bool(true));

        for row in rows {
            let key: String = row.try_get("key")
                .map_err(|e| SigmaXError::Database(format!("获取key失败: {}", e)))?;
            let value: serde_json::Value = row.try_get("value")
                .map_err(|e| SigmaXError::Database(format!("获取value失败: {}", e)))?;

            // 移除命名空间前缀
            let field_name = key.strip_prefix(&format!("{}.", namespace))
                .unwrap_or(&key);
            config_map.insert(field_name.to_string(), value);
        }

        // 🔥 修复：确保所有必需字段都有默认值
        let default_config = CacheConfig::default();
        let default_value = serde_json::to_value(&default_config)
            .map_err(|e| SigmaXError::Config(format!("序列化默认配置失败: {}", e)))?;

        if let serde_json::Value::Object(default_map) = default_value {
            // 为缺失的字段填充默认值
            for (key, value) in default_map {
                config_map.entry(key).or_insert(value);
            }
        }

        let config_value = serde_json::Value::Object(config_map);
        serde_json::from_value(config_value)
            .map_err(|e| SigmaXError::Config(format!("反序列化{}配置失败: {}", namespace, e)))
    }

    /// 保存强类型配置到数据库
    async fn save_typed_config(&self, namespace: &str, config: &CacheConfig, description_prefix: &str) -> SigmaXResult<()> {
        let config_value = serde_json::to_value(config)
            .map_err(|e| SigmaXError::Config(format!("序列化{}配置失败: {}", namespace, e)))?;

        if let serde_json::Value::Object(map) = config_value {
            let pool = self.db.pool();
            let mut tx = pool.begin().await
                .map_err(|e| SigmaXError::Database(format!("开始事务失败: {}", e)))?;

            // 删除现有配置
            sqlx::query("DELETE FROM system_config WHERE key LIKE $1")
                .bind(format!("{}%", namespace))
                .execute(&mut *tx)
                .await
                .map_err(|e| SigmaXError::Database(format!("删除现有配置失败: {}", e)))?;

            // 插入新配置
            for (key, value) in map {
                let full_key = format!("{}.{}", namespace, key);
                let description = Some(format!("{} - {}", description_prefix, key));
                
                sqlx::query(
                    "INSERT INTO system_config (key, value, description, is_encrypted, created_at, updated_at) 
                     VALUES ($1, $2, $3, $4, NOW(), NOW())"
                )
                .bind(&full_key)
                .bind(&value)
                .bind(&description)
                .bind(false)
                .execute(&mut *tx)
                .await
                .map_err(|e| SigmaXError::Database(format!("插入配置失败: {}", e)))?;
            }

            tx.commit().await
                .map_err(|e| SigmaXError::Database(format!("提交事务失败: {}", e)))?;
        }

        Ok(())
    }
}

#[async_trait]
impl CacheRepository for SqlCacheRepository {
    // ============================================================================
    // 缓存配置管理
    // ============================================================================

    async fn get_config(&self) -> SigmaXResult<CacheConfig> {
        self.build_typed_config("cache").await
    }

    async fn save_config(&self, config: &CacheConfig) -> SigmaXResult<()> {
        self.save_typed_config("cache", config, "缓存配置").await
    }

    async fn update_config(&self, config: &CacheConfig) -> SigmaXResult<()> {
        self.save_config(config).await
    }

    async fn delete_config(&self) -> SigmaXResult<()> {
        let pool = self.db.pool();
        
        sqlx::query("DELETE FROM system_config WHERE key LIKE 'cache%'")
            .execute(pool)
            .await
            .map_err(|e| SigmaXError::Database(format!("删除缓存配置失败: {}", e)))?;

        Ok(())
    }

    // ============================================================================
    // 缓存统计管理
    // ============================================================================

    async fn record_stats(&self, stats: &CacheStatsRecord) -> SigmaXResult<()> {
        let pool = self.db.pool();

        sqlx::query(
            r#"
            INSERT INTO cache_stats (
                id, timestamp, cache_type, total_keys, memory_usage_mb, memory_limit_mb,
                memory_usage_percentage, hit_rate, miss_rate, total_operations, hits, misses,
                evictions, expired_keys, average_ttl_seconds, uptime_seconds, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18)
            "#
        )
        .bind(&stats.id)
        .bind(&stats.timestamp)
        .bind(&stats.cache_type)
        .bind(stats.total_keys as i64)
        .bind(&stats.memory_usage_mb)
        .bind(&stats.memory_limit_mb)
        .bind(&stats.memory_usage_percentage)
        .bind(&stats.hit_rate)
        .bind(&stats.miss_rate)
        .bind(stats.total_operations as i64)
        .bind(stats.hits as i64)
        .bind(stats.misses as i64)
        .bind(stats.evictions as i64)
        .bind(stats.expired_keys as i64)
        .bind(&stats.average_ttl_seconds)
        .bind(stats.uptime_seconds as i64)
        .bind(&stats.created_at)
        .bind(&stats.updated_at)
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("记录缓存统计失败: {}", e)))?;

        Ok(())
    }

    async fn get_latest_stats(&self, cache_type: Option<&str>) -> SigmaXResult<Vec<CacheStatsRecord>> {
        let pool = self.db.pool();

        let query = if let Some(cache_type) = cache_type {
            sqlx::query(
                r#"
                SELECT * FROM cache_stats
                WHERE cache_type = $1
                ORDER BY timestamp DESC
                LIMIT 1
                "#
            )
            .bind(cache_type)
        } else {
            sqlx::query(
                r#"
                SELECT DISTINCT ON (cache_type) * FROM cache_stats
                ORDER BY cache_type, timestamp DESC
                "#
            )
        };

        let rows = query
            .fetch_all(pool)
            .await
            .map_err(|e| SigmaXError::Database(format!("获取最新缓存统计失败: {}", e)))?;

        let mut stats = Vec::new();
        for row in rows {
            stats.push(self.row_to_cache_stats(row)?);
        }

        Ok(stats)
    }

    async fn get_stats_history(
        &self,
        cache_type: Option<&str>,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
        limit: Option<u32>,
    ) -> SigmaXResult<Vec<CacheStatsRecord>> {
        let pool = self.db.pool();
        let limit = limit.unwrap_or(1000);

        let query = if let Some(cache_type) = cache_type {
            sqlx::query(
                r#"
                SELECT * FROM cache_stats
                WHERE cache_type = $1 AND timestamp BETWEEN $2 AND $3
                ORDER BY timestamp DESC
                LIMIT $4
                "#
            )
            .bind(cache_type)
            .bind(start_time)
            .bind(end_time)
            .bind(limit as i64)
        } else {
            sqlx::query(
                r#"
                SELECT * FROM cache_stats
                WHERE timestamp BETWEEN $1 AND $2
                ORDER BY timestamp DESC
                LIMIT $3
                "#
            )
            .bind(start_time)
            .bind(end_time)
            .bind(limit as i64)
        };

        let rows = query
            .fetch_all(pool)
            .await
            .map_err(|e| SigmaXError::Database(format!("获取缓存统计历史失败: {}", e)))?;

        let mut stats = Vec::new();
        for row in rows {
            stats.push(self.row_to_cache_stats(row)?);
        }

        Ok(stats)
    }

    async fn cleanup_old_stats(&self, retention_days: u32) -> SigmaXResult<u64> {
        let pool = self.db.pool();

        let result = sqlx::query(
            "DELETE FROM cache_stats WHERE created_at < NOW() - INTERVAL '1 day' * $1"
        )
        .bind(retention_days as i32)
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("清理过期缓存统计失败: {}", e)))?;

        Ok(result.rows_affected())
    }

    // ============================================================================
    // 缓存性能监控
    // ============================================================================

    async fn record_performance(&self, performance: &CachePerformanceRecord) -> SigmaXResult<()> {
        let pool = self.db.pool();

        sqlx::query(
            r#"
            INSERT INTO cache_performance (
                id, timestamp, cache_type, average_response_time_ms, p95_response_time_ms,
                p99_response_time_ms, throughput_ops_per_second, error_rate, created_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            "#
        )
        .bind(&performance.id)
        .bind(&performance.timestamp)
        .bind(&performance.cache_type)
        .bind(&performance.average_response_time_ms)
        .bind(&performance.p95_response_time_ms)
        .bind(&performance.p99_response_time_ms)
        .bind(&performance.throughput_ops_per_second)
        .bind(&performance.error_rate)
        .bind(&performance.created_at)
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("记录缓存性能数据失败: {}", e)))?;

        Ok(())
    }

    async fn get_performance_history(
        &self,
        cache_type: Option<&str>,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
        limit: Option<u32>,
    ) -> SigmaXResult<Vec<CachePerformanceRecord>> {
        let pool = self.db.pool();
        let limit = limit.unwrap_or(1000);

        let query = if let Some(cache_type) = cache_type {
            sqlx::query(
                r#"
                SELECT * FROM cache_performance
                WHERE cache_type = $1 AND timestamp BETWEEN $2 AND $3
                ORDER BY timestamp DESC
                LIMIT $4
                "#
            )
            .bind(cache_type)
            .bind(start_time)
            .bind(end_time)
            .bind(limit as i64)
        } else {
            sqlx::query(
                r#"
                SELECT * FROM cache_performance
                WHERE timestamp BETWEEN $1 AND $2
                ORDER BY timestamp DESC
                LIMIT $3
                "#
            )
            .bind(start_time)
            .bind(end_time)
            .bind(limit as i64)
        };

        let rows = query
            .fetch_all(pool)
            .await
            .map_err(|e| SigmaXError::Database(format!("获取缓存性能历史失败: {}", e)))?;

        let mut performance_records = Vec::new();
        for row in rows {
            performance_records.push(self.row_to_cache_performance(row)?);
        }

        Ok(performance_records)
    }

    async fn record_health_check(&self, health: &CacheHealthRecord) -> SigmaXResult<()> {
        let pool = self.db.pool();

        sqlx::query(
            r#"
            INSERT INTO cache_health (
                id, timestamp, status, memory_pressure_level, memory_usage_percentage,
                eviction_rate, fragmentation_ratio, issues, recommendations, created_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
            "#
        )
        .bind(&health.id)
        .bind(&health.timestamp)
        .bind(&health.status)
        .bind(&health.memory_pressure_level)
        .bind(&health.memory_usage_percentage)
        .bind(&health.eviction_rate)
        .bind(&health.fragmentation_ratio)
        .bind(&health.issues)
        .bind(&health.recommendations)
        .bind(&health.created_at)
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("记录缓存健康检查失败: {}", e)))?;

        Ok(())
    }

    async fn get_latest_health(&self) -> SigmaXResult<Option<CacheHealthRecord>> {
        let pool = self.db.pool();

        let row = sqlx::query(
            "SELECT * FROM cache_health ORDER BY timestamp DESC LIMIT 1"
        )
        .fetch_optional(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("获取最新健康状态失败: {}", e)))?;

        if let Some(row) = row {
            Ok(Some(self.row_to_cache_health(row)?))
        } else {
            Ok(None)
        }
    }

    async fn get_health_history(
        &self,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
        limit: Option<u32>,
    ) -> SigmaXResult<Vec<CacheHealthRecord>> {
        let pool = self.db.pool();
        let limit = limit.unwrap_or(1000);

        let rows = sqlx::query(
            r#"
            SELECT * FROM cache_health
            WHERE timestamp BETWEEN $1 AND $2
            ORDER BY timestamp DESC
            LIMIT $3
            "#
        )
        .bind(start_time)
        .bind(end_time)
        .bind(limit as i64)
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("获取健康检查历史失败: {}", e)))?;

        let mut health_records = Vec::new();
        for row in rows {
            health_records.push(self.row_to_cache_health(row)?);
        }

        Ok(health_records)
    }

    async fn create_alert(&self, alert: &CacheAlertRecord) -> SigmaXResult<()> {
        let pool = self.db.pool();

        sqlx::query(
            r#"
            INSERT INTO cache_alerts (
                id, timestamp, rule_name, severity, condition, current_value,
                threshold_value, description, resolved, resolved_at, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
            "#
        )
        .bind(&alert.id)
        .bind(&alert.timestamp)
        .bind(&alert.rule_name)
        .bind(&alert.severity)
        .bind(&alert.condition)
        .bind(&alert.current_value)
        .bind(&alert.threshold_value)
        .bind(&alert.description)
        .bind(&alert.resolved)
        .bind(&alert.resolved_at)
        .bind(&alert.created_at)
        .bind(&alert.updated_at)
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("创建缓存告警失败: {}", e)))?;

        Ok(())
    }

    async fn resolve_alert(&self, alert_id: Uuid) -> SigmaXResult<()> {
        let pool = self.db.pool();

        sqlx::query(
            "UPDATE cache_alerts SET resolved = true, resolved_at = NOW(), updated_at = NOW() WHERE id = $1"
        )
        .bind(alert_id)
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("解决缓存告警失败: {}", e)))?;

        Ok(())
    }

    async fn get_active_alerts(&self) -> SigmaXResult<Vec<CacheAlertRecord>> {
        let pool = self.db.pool();

        let rows = sqlx::query(
            "SELECT * FROM cache_alerts WHERE resolved = false ORDER BY timestamp DESC"
        )
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("获取活跃告警失败: {}", e)))?;

        let mut alerts = Vec::new();
        for row in rows {
            alerts.push(self.row_to_cache_alert(row)?);
        }

        Ok(alerts)
    }

    async fn get_alert_history(
        &self,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
        limit: Option<u32>,
    ) -> SigmaXResult<Vec<CacheAlertRecord>> {
        let pool = self.db.pool();
        let limit = limit.unwrap_or(1000);

        let rows = sqlx::query(
            r#"
            SELECT * FROM cache_alerts
            WHERE timestamp BETWEEN $1 AND $2
            ORDER BY timestamp DESC
            LIMIT $3
            "#
        )
        .bind(start_time)
        .bind(end_time)
        .bind(limit as i64)
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("获取告警历史失败: {}", e)))?;

        let mut alerts = Vec::new();
        for row in rows {
            alerts.push(self.row_to_cache_alert(row)?);
        }

        Ok(alerts)
    }

    async fn clear_cache_data(&self, _cache_types: Option<Vec<String>>) -> SigmaXResult<u64> {
        Ok(0)
    }

    async fn get_cache_keys(
        &self,
        _cache_type: Option<&str>,
        _pattern: Option<&str>,
        _limit: Option<u32>,
        _offset: Option<u32>,
    ) -> SigmaXResult<Vec<serde_json::Value>> {
        Ok(Vec::new())
    }

    async fn warm_cache(
        &self,
        _cache_types: Vec<String>,
        _priority_keys: Option<Vec<String>>,
    ) -> SigmaXResult<()> {
        Ok(())
    }
}
