//! 策略管理API处理器

use axum::{
    extract::{Path, Query, State},
    Json,
};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
// use chrono::{DateTime, Utc};
use tracing::info;

use crate::{
    state::AppState,
    error::{Api<PERSON>rror, ApiResult, PaginationQuery, ApiResponse},
};
use crate::common_types::{StrategyPerformanceResponse};

// ============================================================================
// 请求和响应数据结构
// ============================================================================

/// 策略创建请求
#[derive(Debug, Deserialize)]
pub struct CreateStrategyRequest {
    pub strategy_type: String,
    pub name: String,
    pub description: Option<String>,
    pub trading_pairs: Vec<String>,
    pub initial_capital: String,
    pub parameters: serde_json::Value,
    pub risk_config: Option<StrategyRiskConfigRequest>,
    pub enabled: Option<bool>,
}

/// 风控配置请求
#[derive(Debug, Deserialize)]
pub struct StrategyRiskConfigRequest {
    pub max_position_size: Option<String>,
    pub max_daily_loss: Option<String>,
    pub max_drawdown: Option<String>,
    pub stop_loss_percentage: Option<String>,
    pub take_profit_percentage: Option<String>,
    pub max_leverage: Option<String>,
}

/// 策略更新请求
#[derive(Debug, Deserialize)]
pub struct UpdateStrategyRequest {
    pub name: Option<String>,
    pub description: Option<String>,
    pub parameters: Option<serde_json::Value>,
    pub risk_config: Option<StrategyRiskConfigRequest>,
    pub enabled: Option<bool>,
}

/// 策略响应
#[derive(Debug, Serialize)]
pub struct StrategyResponse {
    pub id: String,
    pub strategy_type: String,
    pub name: String,
    pub description: Option<String>,
    pub trading_pairs: Vec<String>,
    pub initial_capital: String,
    pub parameters: serde_json::Value,
    pub risk_config: StrategyRiskConfigResponse,
    pub status: String,
    pub enabled: bool,
    pub performance: StrategyPerformanceResponse,
    pub created_at: String,
    pub updated_at: String,
}

/// 风控配置响应
#[derive(Debug, Serialize)]
pub struct StrategyRiskConfigResponse {
    pub max_position_size: String,
    pub max_daily_loss: String,
    pub max_drawdown: String,
    pub stop_loss_percentage: String,
    pub take_profit_percentage: String,
    pub max_leverage: String,
}

/// 策略列表响应
#[derive(Debug, Serialize)]
pub struct StrategiesResponse {
    pub strategies: Vec<StrategyResponse>,
    pub total_count: usize,
    pub page: usize,
    pub per_page: usize,
}

// 🗑️ 已移除 StrategyStatusResponse 结构体
// 原因：在单策略架构下，策略状态通过引擎状态查询获得

// ============================================================================
// API处理器实现
// ============================================================================

/// 创建策略
pub async fn create_strategy(
    State(state): State<AppState>,
    Json(request): Json<CreateStrategyRequest>,
) -> ApiResult<Json<ApiResponse<StrategyResponse>>> {
    info!("创建策略: {}", request.name);

    let strategy = state.strategy_service.create_strategy(request).await?;

    info!("策略创建成功，ID: {}", strategy.id);
    let response = ApiResponse::success(strategy, "策略创建成功");
    Ok(Json(response))
}

/// 获取所有策略
pub async fn get_strategies(
    State(state): State<AppState>,
    Query(pagination): Query<PaginationQuery>,
) -> ApiResult<Json<ApiResponse<StrategiesResponse>>> {
    info!("获取策略列表，页码: {}", pagination.page);

    let (strategies, total_count) = state.strategy_service.get_strategies(pagination.page, pagination.per_page).await?;

    let response_data = StrategiesResponse {
        strategies,
        total_count,
        page: pagination.page,
        per_page: pagination.per_page,
    };

    let response = ApiResponse::success(response_data, "获取策略列表成功");
    Ok(Json(response))
}

/// 获取单个策略
pub async fn get_strategy(
    State(state): State<AppState>,
    Path(strategy_id): Path<String>,
) -> ApiResult<Json<ApiResponse<StrategyResponse>>> {
    let strategy_id = Uuid::parse_str(&strategy_id)
        .map_err(|_| ApiError::BadRequest("Invalid strategy ID format".to_string()))?;

    info!("获取策略详情，ID: {}", strategy_id);

    let strategy = state.strategy_service.get_strategy_by_id(strategy_id).await?;

    let response = ApiResponse::success(strategy, "获取策略详情成功");
    Ok(Json(response))
}

/// 更新策略
pub async fn update_strategy(
    State(state): State<AppState>,
    Path(strategy_id): Path<String>,
    Json(request): Json<UpdateStrategyRequest>,
) -> ApiResult<Json<ApiResponse<StrategyResponse>>> {
    let strategy_id = Uuid::parse_str(&strategy_id)
        .map_err(|_| ApiError::BadRequest("Invalid strategy ID format".to_string()))?;

    info!("更新策略，ID: {}", strategy_id);

    let strategy = state.strategy_service.update_strategy(strategy_id, request).await?;

    let response = ApiResponse::success(strategy, "策略更新成功");
    Ok(Json(response))
}

/// 删除策略
pub async fn delete_strategy(
    State(state): State<AppState>,
    Path(strategy_id): Path<String>,
) -> ApiResult<Json<ApiResponse<()>>> {
    let strategy_id = Uuid::parse_str(&strategy_id)
        .map_err(|_| ApiError::BadRequest("Invalid strategy ID format".to_string()))?;

    info!("删除策略，ID: {}", strategy_id);

    state.strategy_service.delete_strategy(strategy_id).await?;

    let response = ApiResponse::success((), "策略删除成功");
    Ok(Json(response))
}

// 🗑️ 已移除策略级别的start/stop/status处理器
//
// 原因：在单策略架构下，这些API变得冗余：
// - 策略执行由引擎控制，而非独立的API调用
// - 引擎只能运行一个策略，策略控制应该在引擎级别
// - 避免API状态与实际执行状态不同步的问题
//
// 替代方案：
// - 使用引擎级别的start/stop API
// - 策略状态通过引擎状态查询获得
