[package]
name = "sigmax-interfaces"
version = "0.1.0"
edition = "2021"

[dependencies]
# Core dependencies
sigmax-core = { path = "../core" }
async-trait = "0.1"
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
uuid = { version = "1.0", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }
rust_decimal = { version = "1.36", features = ["serde-with-str"] }
thiserror = "1.0"
tracing = "0.1"

[dev-dependencies]
tokio-test = "0.4"