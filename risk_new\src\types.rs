//! 统一的风险管理核心类型定义

use async_trait::async_trait;
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use sigmax_core::{Order, Balance, TradingPair, SigmaXResult};
use std::collections::HashMap;
use uuid::Uuid;

// ============================================================================
// 核心风险检查类型
// ============================================================================

/// 统一的风险检查结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskCheckResult {
    /// 检查是否通过
    pub passed: bool,
    /// 风险等级
    pub risk_level: RiskLevel,
    /// 风险评分 (0.0-1.0)
    pub risk_score: f64,
    /// 检查类型
    pub check_type: RiskCheckType,
    /// 结果消息
    pub message: Option<String>,
    /// 警告信息
    pub warnings: Vec<String>,
    /// 建议信息
    pub suggestions: Vec<String>,
    /// 违反的规则详情
    pub violated_rules: Vec<ViolatedRule>,
    /// 检查时间戳
    pub timestamp: DateTime<Utc>,
    /// 执行时间（毫秒）
    pub execution_time_ms: u64,
}

impl RiskCheckResult {
    /// 创建一个通过的风险检查结果
    pub fn passed() -> Self {
        Self {
            passed: true,
            risk_level: RiskLevel::Low,
            risk_score: 0.0,
            check_type: RiskCheckType::Order,
            message: None,
            warnings: Vec::new(),
            suggestions: Vec::new(),
            violated_rules: Vec::new(),
            timestamp: Utc::now(),
            execution_time_ms: 0,
        }
    }

    /// 创建一个失败的风险检查结果
    pub fn failed(risk_level: RiskLevel, message: String) -> Self {
        Self {
            passed: false,
            risk_level,
            risk_score: match risk_level {
                RiskLevel::Low => 0.25,
                RiskLevel::Medium => 0.5,
                RiskLevel::High => 0.75,
                RiskLevel::Critical => 1.0,
            },
            check_type: RiskCheckType::Order,
            message: Some(message),
            warnings: Vec::new(),
            suggestions: Vec::new(),
            violated_rules: Vec::new(),
            timestamp: Utc::now(),
            execution_time_ms: 0,
        }
    }

    /// 获取执行的规则数量（为了兼容性）
    pub fn rules_executed(&self) -> usize {
        // 返回违反规则数量作为执行规则的指示
        self.violated_rules.len()
    }

    /// 获取失败的规则（为了兼容性）
    pub fn failed_rules(&self) -> &Vec<ViolatedRule> {
        &self.violated_rules
    }
}

/// 风险等级枚举
#[derive(Debug, Clone, Copy, Serialize, Deserialize, PartialEq, Eq, PartialOrd, Ord)]
pub enum RiskLevel {
    Low,
    Medium,
    High,
    Critical,
}

/// 风险检查类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum RiskCheckType {
    Order,
    Position,
    Portfolio,
    Strategy,
    System,
}

/// 违反的规则详情
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ViolatedRule {
    pub rule_id: Uuid,
    pub rule_name: String,
    pub rule_type: RuleType,
    pub violation_reason: String,
    pub severity: RiskLevel,
    pub suggested_action: Option<String>,
}

// ============================================================================
// 风险规则类型
// ============================================================================

/// 风险规则定义
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskRule {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub rule_type: RuleType,
    pub parameters: RuleParameters,
    pub conditions: RuleConditions,
    pub enabled: bool,
    pub priority: i32,
    pub strategy_types: Vec<String>,
    pub trading_pairs: Vec<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 规则类型枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum RuleType {
    PositionLimit,
    OrderSize,
    DailyLoss,
    Volatility,
    Concentration,
    Liquidity,
    TimeWindow,
    Custom(String),
}

/// 强类型规则参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RuleParameters {
    PositionLimit {
        max_position_ratio: f64,
        max_absolute_amount: Option<Decimal>,
    },
    OrderSize {
        max_order_ratio: f64,
        max_absolute_size: Option<Decimal>,
        min_order_size: Option<Decimal>,
    },
    DailyLoss {
        max_loss_ratio: f64,
        max_absolute_loss: Option<Decimal>,
        reset_time: String,
    },
    Volatility {
        max_volatility: f64,
        lookback_period_days: u32,
    },
    Concentration {
        max_single_asset_ratio: f64,
        max_sector_ratio: f64,
        max_strategy_ratio: f64,
    },
    Liquidity {
        min_daily_volume: Decimal,
        max_spread_ratio: f64,
        min_market_depth: Option<Decimal>,
    },
    TimeWindow {
        allowed_hours: Vec<u8>,
        timezone: String,
        trading_days: Vec<u8>,
    },
    Custom {
        parameters: HashMap<String, serde_json::Value>,
    },
}

/// 规则执行条件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RuleConditions {
    pub market_conditions: Option<MarketConditionFilter>,
    pub strategy_conditions: Option<StrategyConditionFilter>,
    pub time_conditions: Option<TimeConditionFilter>,
    pub custom_conditions: Option<HashMap<String, serde_json::Value>>,
}

/// 市场条件过滤器
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MarketConditionFilter {
    pub volatility_range: Option<(f64, f64)>,
    pub volume_range: Option<(Decimal, Decimal)>,
    pub price_change_range: Option<(f64, f64)>,
}

/// 策略条件过滤器
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StrategyConditionFilter {
    pub strategy_types: Vec<String>,
    pub performance_threshold: Option<f64>,
    pub drawdown_threshold: Option<f64>,
}

/// 时间条件过滤器
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TimeConditionFilter {
    pub trading_hours: Option<(String, String)>,
    pub trading_days: Option<Vec<u8>>,
    pub timezone: String,
}

// ============================================================================
// 风险检查上下文
// ============================================================================

/// 风险检查上下文
#[derive(Debug, Clone)]
pub struct RiskContext {
    pub strategy_type: Option<String>,
    pub trading_pair: Option<TradingPair>,
    pub market_conditions: Option<MarketConditions>,
    pub user_preferences: Option<UserRiskPreferences>,
    pub portfolio_state: Option<PortfolioState>,
    pub timestamp: DateTime<Utc>,
}

/// 市场条件
#[derive(Debug, Clone)]
pub struct MarketConditions {
    pub volatility: f64,
    pub volume_24h: Decimal,
    pub price_change_24h: f64,
    pub bid_ask_spread: f64,
    pub market_sentiment: MarketSentiment,
}

/// 市场情绪
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MarketSentiment {
    Bullish,
    Neutral,
    Bearish,
    Volatile,
}

/// 用户风险偏好
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserRiskPreferences {
    pub risk_tolerance: RiskTolerance,
    pub max_daily_loss: Option<Decimal>,
    pub max_position_size: Option<Decimal>,
    pub preferred_assets: Vec<String>,
    pub excluded_assets: Vec<String>,
}

/// 风险承受能力
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RiskTolerance {
    Conservative,
    Moderate,
    Aggressive,
    Custom { score: f64 },
}

/// 投资组合状态
#[derive(Debug, Clone)]
pub struct PortfolioState {
    pub total_value: Decimal,
    pub available_balance: Decimal,
    pub positions: Vec<Position>,
    pub daily_pnl: Decimal,
    pub unrealized_pnl: Decimal,
}

/// 持仓信息
#[derive(Debug, Clone)]
pub struct Position {
    pub trading_pair: TradingPair,
    pub quantity: Decimal,
    pub average_price: Decimal,
    pub current_price: Decimal,
    pub unrealized_pnl: Decimal,
    pub position_ratio: f64,
}

// ============================================================================
// 风险指标类型
// ============================================================================

/// 风险指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskMetrics {
    pub var_95: Decimal,
    pub var_99: Decimal,
    pub cvar_95: Decimal,
    pub cvar_99: Decimal,
    pub volatility: f64,
    pub sharpe_ratio: f64,
    pub max_drawdown: f64,
    pub beta: Option<f64>,
    pub calculated_at: DateTime<Utc>,
}

/// 单个资产风险指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AssetRiskMetrics {
    pub trading_pair: TradingPair,
    pub volatility: f64,
    pub beta: Option<f64>,
    pub correlation_with_portfolio: f64,
    pub liquidity_score: f64,
    pub risk_contribution: f64,
    pub calculated_at: DateTime<Utc>,
}
