//! SigmaX-R 重构后的风险管理模块
//!
//! 这是重构后的风险管理模块，遵循最佳实践和清晰的架构设计。

// 模块声明
pub mod types;
pub mod engine;
pub mod rules;
pub mod metrics;
pub mod repository;
pub mod config;
// 注意：compat 模块暂时禁用，因为旧的风险模块已被移除
// pub mod compat;

// 重新导出核心类型
pub use types::*;
pub use engine::*;
pub use rules::*;
pub use metrics::*;
pub use repository::*;
pub use config::*;

// 测试工具（仅在测试时可用）
#[cfg(any(test, feature = "test-utils"))]
pub mod test_utils;

#[cfg(any(test, feature = "test-utils"))]
pub use test_utils::*;
