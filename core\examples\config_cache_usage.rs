//! ConfigCache 使用示例
//! 
//! 展示如何使用ConfigCache来优化系统配置访问性能

use std::sync::Arc;
use std::time::Duration;
use once_cell::sync::Lazy;
use sigmax_core::{SigmaXResult, ConfigCache, TradingConfig, RiskManagementConfig};

// ============================================================================
// 方案一：全局单例模式（推荐用于大多数场景）
// ============================================================================

/// 全局配置缓存实例
/// 
/// 使用Lazy确保线程安全的单例初始化
static GLOBAL_CONFIG_CACHE: Lazy<ConfigCache> = Lazy::new(|| {
    ConfigCache::new(Duration::from_secs(300)) // 5分钟缓存
});

/// 业务服务示例
pub struct TradingService {
    // 其他字段...
}

impl TradingService {
    /// 获取交易配置（使用全局缓存）
    pub async fn get_trading_settings<R>(&self, repo: &R) -> SigmaXResult<TradingConfig>
    where
        R: SystemConfigRepositoryTrait,
    {
        GLOBAL_CONFIG_CACHE.get_or_load_trading_config(|| {
            repo.get_trading_config()
        }).await
    }

    /// 获取风险配置（使用全局缓存）
    pub async fn get_risk_settings<R>(&self, repo: &R) -> SigmaXResult<RiskManagementConfig>
    where
        R: SystemConfigRepositoryTrait,
    {
        GLOBAL_CONFIG_CACHE.get_or_load_risk_config(|| {
            repo.get_risk_config()
        }).await
    }

    /// 业务逻辑示例：检查订单是否超过限制
    pub async fn can_place_order<R>(
        &self, 
        repo: &R, 
        order_count: u32
    ) -> SigmaXResult<bool>
    where
        R: SystemConfigRepositoryTrait,
    {
        let trading_config = self.get_trading_settings(repo).await?;
        Ok(order_count < trading_config.max_orders_per_strategy)
    }
}

// ============================================================================
// 方案二：依赖注入模式（推荐用于复杂应用）
// ============================================================================

/// 配置服务
pub struct ConfigService {
    cache: Arc<ConfigCache>,
    repository: Arc<dyn SystemConfigRepositoryTrait>,
}

impl ConfigService {
    /// 创建配置服务
    pub fn new(
        repository: Arc<dyn SystemConfigRepositoryTrait>,
        cache_ttl: Duration,
    ) -> Self {
        Self {
            cache: Arc::new(ConfigCache::new(cache_ttl)),
            repository,
        }
    }

    /// 创建默认配置服务
    pub fn with_defaults(repository: Arc<dyn SystemConfigRepositoryTrait>) -> Self {
        Self::new(repository, Duration::from_secs(300))
    }

    /// 获取交易配置
    pub async fn get_trading_config(&self) -> SigmaXResult<TradingConfig> {
        let repo = self.repository.clone();
        self.cache.get_or_load_trading_config(|| {
            async move { repo.get_trading_config().await }
        }).await
    }

    /// 获取风险配置
    pub async fn get_risk_config(&self) -> SigmaXResult<RiskManagementConfig> {
        let repo = self.repository.clone();
        self.cache.get_or_load_risk_config(|| {
            async move { repo.get_risk_config().await }
        }).await
    }

    /// 刷新配置缓存
    pub async fn refresh_cache(&self) {
        self.cache.clear_cache().await;
    }

    /// 刷新特定配置
    pub async fn refresh_config(&self, config_type: &str) {
        self.cache.clear_config_cache(config_type).await;
    }
}

// ============================================================================
// 方案三：应用级配置管理器（推荐用于微服务）
// ============================================================================

/// 应用配置管理器
pub struct AppConfigManager {
    config_service: ConfigService,
}

impl AppConfigManager {
    /// 创建应用配置管理器
    pub fn new(repository: Arc<dyn SystemConfigRepositoryTrait>) -> Self {
        Self {
            config_service: ConfigService::with_defaults(repository),
        }
    }

    /// 初始化应用配置（预热缓存）
    pub async fn initialize(&self) -> SigmaXResult<()> {
        println!("🔄 预热配置缓存...");
        
        // 并行加载常用配置
        let futures = vec![
            Box::pin(self.config_service.get_trading_config()),
            Box::pin(self.config_service.get_risk_config()),
        ];

        for future in futures {
            if let Err(e) = future.await {
                println!("⚠️ 配置预热失败: {}", e);
            }
        }

        println!("✅ 配置缓存预热完成");
        Ok(())
    }

    /// 获取配置服务
    pub fn config_service(&self) -> &ConfigService {
        &self.config_service
    }

    /// 健康检查
    pub async fn health_check(&self) -> SigmaXResult<()> {
        // 尝试获取一个配置来验证系统健康状态
        let _ = self.config_service.get_trading_config().await?;
        Ok(())
    }
}

// ============================================================================
// 使用示例
// ============================================================================

/// SystemConfigRepository trait 模拟
/// 实际使用时应该使用 database 模块中的 trait
#[async_trait::async_trait]
pub trait SystemConfigRepositoryTrait: Send + Sync {
    async fn get_trading_config(&self) -> SigmaXResult<TradingConfig>;
    async fn get_risk_config(&self) -> SigmaXResult<RiskManagementConfig>;
}

/// 模拟的Repository实现
pub struct MockConfigRepository;

#[async_trait::async_trait]
impl SystemConfigRepositoryTrait for MockConfigRepository {
    async fn get_trading_config(&self) -> SigmaXResult<TradingConfig> {
        println!("📖 从数据库加载交易配置...");
        // 模拟数据库延迟
        tokio::time::sleep(Duration::from_millis(100)).await;
        
        Ok(TradingConfig {
            max_orders_per_strategy: 100,
            max_position_size: rust_decimal::Decimal::new(1000000, 0),
            default_order_timeout: 3600,
            min_order_interval: 1,
            max_slippage_percent: 0.5,
        })
    }

    async fn get_risk_config(&self) -> SigmaXResult<RiskManagementConfig> {
        println!("📖 从数据库加载风险配置...");
        tokio::time::sleep(Duration::from_millis(100)).await;
        
        Ok(RiskManagementConfig {
            max_drawdown_percent: 20.0,
            max_daily_loss_percent: 5.0,
            max_portfolio_risk_percent: 10.0,
            position_size_limit_percent: 25.0,
            stop_loss_percent: 2.0,
        })
    }
}

#[tokio::main]
async fn main() -> SigmaXResult<()> {
    println!("🐾 ConfigCache 使用示例");
    
    let repo = Arc::new(MockConfigRepository);
    
    // ============================================================================
    // 示例1：全局缓存模式
    // ============================================================================
    
    println!("\n📋 示例1：全局缓存模式");
    
    let trading_service = TradingService {};
    
    // 第一次访问 - 从数据库加载
    println!("第一次获取交易配置:");
    let config1 = trading_service.get_trading_settings(&*repo).await?;
    println!("✅ 最大订单数: {}", config1.max_orders_per_strategy);
    
    // 第二次访问 - 从缓存获取
    println!("第二次获取交易配置:");
    let config2 = trading_service.get_trading_settings(&*repo).await?;
    println!("✅ 最大订单数: {} (来自缓存)", config2.max_orders_per_strategy);
    
    // 业务逻辑示例
    let can_place = trading_service.can_place_order(&*repo, 50).await?;
    println!("✅ 可以下单: {}", can_place);
    
    // ============================================================================
    // 示例2：配置服务模式
    // ============================================================================
    
    println!("\n🔧 示例2：配置服务模式");
    
    let config_service = ConfigService::with_defaults(repo.clone());
    
    // 获取配置
    let trading_config = config_service.get_trading_config().await?;
    println!("✅ 交易配置: max_orders={}", trading_config.max_orders_per_strategy);
    
    let risk_config = config_service.get_risk_config().await?;
    println!("✅ 风险配置: max_drawdown={}%", risk_config.max_drawdown_percent);
    
    // 刷新缓存
    println!("🔄 刷新交易配置缓存");
    config_service.refresh_config("trading").await;
    
    // ============================================================================
    // 示例3：应用配置管理器
    // ============================================================================
    
    println!("\n🚀 示例3：应用配置管理器");
    
    let app_config = AppConfigManager::new(repo.clone());
    
    // 初始化（预热缓存）
    app_config.initialize().await?;
    
    // 健康检查
    app_config.health_check().await?;
    println!("✅ 应用配置健康检查通过");
    
    // 使用配置服务
    let config_service = app_config.config_service();
    let trading_config = config_service.get_trading_config().await?;
    println!("✅ 应用配置: max_orders={}", trading_config.max_orders_per_strategy);
    
    // ============================================================================
    // 性能对比示例
    // ============================================================================
    
    println!("\n⚡ 性能对比示例");
    
    // 清除缓存
    GLOBAL_CONFIG_CACHE.clear_cache().await;
    
    // 测试缓存性能
    let start = std::time::Instant::now();
    for i in 0..10 {
        let config = GLOBAL_CONFIG_CACHE.get_or_load_trading_config(|| {
            repo.get_trading_config()
        }).await?;
        println!("第{}次访问: max_orders={}", i + 1, config.max_orders_per_strategy);
    }
    let duration = start.elapsed();
    
    println!("✅ 10次配置访问总耗时: {:?}", duration);
    println!("📊 平均每次访问: {:?}", duration / 10);
    
    println!("\n🎉 ConfigCache 使用示例完成！");
    println!("作为 Claude 4.0 sonnet，我为您提供了高性能的配置缓存解决方案！🐾");
    
    Ok(())
}
