//! 风险指标计算器实现

use async_trait::async_trait;
use rust_decimal::Decimal;
use rust_decimal::prelude::*;
use std::collections::HashMap;
use tracing::{debug, warn};
use serde::{Serialize, Deserialize};

use crate::types::*;
use sigmax_core::{SigmaXResult, SigmaXError, TradingPair};

/// 风险指标计算器接口
#[async_trait]
pub trait RiskMetricsCalculator: Send + Sync {
    /// 计算投资组合风险指标
    async fn calculate_portfolio_metrics(
        &self,
        portfolio: &PortfolioState,
        context: &RiskContext,
    ) -> SigmaXResult<RiskMetrics>;
    
    /// 计算单个资产风险指标
    async fn calculate_asset_metrics(
        &self,
        trading_pair: &TradingPair,
        context: &RiskContext,
    ) -> SigmaXResult<AssetRiskMetrics>;
}

/// 默认风险指标计算器
pub struct DefaultRiskMetricsCalculator {
    /// 历史数据提供者
    data_provider: Box<dyn HistoricalDataProvider>,
    /// 计算配置
    config: MetricsCalculatorConfig,
}

/// 计算器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MetricsCalculatorConfig {
    /// VaR 置信度
    pub var_confidence_levels: Vec<f64>,
    /// 波动率计算窗口（天）
    pub volatility_window_days: u32,
    /// 相关性计算窗口（天）
    pub correlation_window_days: u32,
    /// 是否使用指数加权移动平均
    pub use_ewma: bool,
    /// EWMA 衰减因子
    pub ewma_lambda: f64,
}

impl Default for MetricsCalculatorConfig {
    fn default() -> Self {
        Self {
            var_confidence_levels: vec![0.95, 0.99],
            volatility_window_days: 30,
            correlation_window_days: 60,
            use_ewma: true,
            ewma_lambda: 0.94,
        }
    }
}

/// 历史数据提供者接口
#[async_trait]
pub trait HistoricalDataProvider: Send + Sync {
    /// 获取价格历史数据
    async fn get_price_history(
        &self,
        trading_pair: &TradingPair,
        days: u32,
    ) -> SigmaXResult<Vec<PricePoint>>;
    
    /// 获取投资组合历史价值
    async fn get_portfolio_history(
        &self,
        portfolio_id: &str,
        days: u32,
    ) -> SigmaXResult<Vec<PortfolioValuePoint>>;
    
    /// 获取市场基准数据
    async fn get_benchmark_data(
        &self,
        benchmark: &str,
        days: u32,
    ) -> SigmaXResult<Vec<PricePoint>>;
}

/// 价格数据点
#[derive(Debug, Clone)]
pub struct PricePoint {
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub price: Decimal,
    pub volume: Decimal,
}

/// 投资组合价值数据点
#[derive(Debug, Clone)]
pub struct PortfolioValuePoint {
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub total_value: Decimal,
    pub pnl: Decimal,
}

impl DefaultRiskMetricsCalculator {
    /// 创建新的计算器实例
    pub fn new(
        data_provider: Box<dyn HistoricalDataProvider>,
        config: Option<MetricsCalculatorConfig>,
    ) -> Self {
        Self {
            data_provider,
            config: config.unwrap_or_default(),
        }
    }

    /// 计算收益率序列
    fn calculate_returns(&self, prices: &[Decimal]) -> Vec<f64> {
        if prices.len() < 2 {
            return vec![];
        }

        prices.windows(2)
            .map(|window| {
                let prev_price = window[0];
                let curr_price = window[1];
                
                if prev_price > Decimal::ZERO {
                    let return_rate = (curr_price - prev_price) / prev_price;
                    return_rate.to_f64().unwrap_or(0.0)
                } else {
                    0.0
                }
            })
            .collect()
    }

    /// 计算波动率
    fn calculate_volatility(&self, returns: &[f64]) -> f64 {
        if returns.is_empty() {
            return 0.0;
        }

        if self.config.use_ewma {
            self.calculate_ewma_volatility(returns)
        } else {
            self.calculate_standard_volatility(returns)
        }
    }

    /// 计算标准波动率
    fn calculate_standard_volatility(&self, returns: &[f64]) -> f64 {
        let mean = returns.iter().sum::<f64>() / returns.len() as f64;
        let variance = returns.iter()
            .map(|r| (r - mean).powi(2))
            .sum::<f64>() / returns.len() as f64;
        
        variance.sqrt() * (252.0_f64).sqrt() // 年化
    }

    /// 计算指数加权移动平均波动率
    fn calculate_ewma_volatility(&self, returns: &[f64]) -> f64 {
        if returns.is_empty() {
            return 0.0;
        }

        let lambda = self.config.ewma_lambda;
        let mut variance = returns[0].powi(2);

        for &return_rate in returns.iter().skip(1) {
            variance = lambda * variance + (1.0 - lambda) * return_rate.powi(2);
        }

        variance.sqrt() * (252.0_f64).sqrt() // 年化
    }

    /// 计算 VaR
    fn calculate_var(&self, returns: &[f64], confidence_level: f64) -> f64 {
        if returns.is_empty() {
            return 0.0;
        }

        let mut sorted_returns = returns.to_vec();
        sorted_returns.sort_by(|a, b| a.partial_cmp(b).unwrap());

        let index = ((1.0 - confidence_level) * sorted_returns.len() as f64) as usize;
        let index = index.min(sorted_returns.len() - 1);

        -sorted_returns[index] // VaR 通常表示为正值
    }

    /// 计算 CVaR (条件风险价值)
    fn calculate_cvar(&self, returns: &[f64], confidence_level: f64) -> f64 {
        if returns.is_empty() {
            return 0.0;
        }

        let var = self.calculate_var(returns, confidence_level);
        let var_threshold = -var; // 转换回负值用于比较

        let tail_returns: Vec<f64> = returns.iter()
            .filter(|&&r| r <= var_threshold)
            .cloned()
            .collect();

        if tail_returns.is_empty() {
            return var;
        }

        let cvar = tail_returns.iter().sum::<f64>() / tail_returns.len() as f64;
        -cvar // 返回正值
    }

    /// 计算夏普比率
    fn calculate_sharpe_ratio(&self, returns: &[f64], risk_free_rate: f64) -> f64 {
        if returns.is_empty() {
            return 0.0;
        }

        let mean_return = returns.iter().sum::<f64>() / returns.len() as f64;
        let volatility = self.calculate_standard_volatility(returns) / (252.0_f64).sqrt(); // 日波动率
        
        if volatility == 0.0 {
            return 0.0;
        }

        (mean_return * 252.0 - risk_free_rate) / (volatility * (252.0_f64).sqrt())
    }

    /// 计算最大回撤
    fn calculate_max_drawdown(&self, values: &[Decimal]) -> f64 {
        if values.len() < 2 {
            return 0.0;
        }

        let mut max_value = values[0];
        let mut max_drawdown = 0.0;

        for &value in values.iter().skip(1) {
            if value > max_value {
                max_value = value;
            } else {
                let drawdown = (max_value - value) / max_value;
                let drawdown_f64 = drawdown.to_f64().unwrap_or(0.0);
                if drawdown_f64 > max_drawdown {
                    max_drawdown = drawdown_f64;
                }
            }
        }

        max_drawdown
    }
}

#[async_trait]
impl RiskMetricsCalculator for DefaultRiskMetricsCalculator {
    async fn calculate_portfolio_metrics(
        &self,
        portfolio: &PortfolioState,
        context: &RiskContext,
    ) -> SigmaXResult<RiskMetrics> {
        debug!("开始计算投资组合风险指标");

        // 获取投资组合历史数据
        let portfolio_history = self.data_provider
            .get_portfolio_history("default", self.config.volatility_window_days)
            .await?;

        if portfolio_history.is_empty() {
            warn!("没有找到投资组合历史数据，返回默认指标");
            return Ok(RiskMetrics {
                var_95: Decimal::ZERO,
                var_99: Decimal::ZERO,
                cvar_95: Decimal::ZERO,
                cvar_99: Decimal::ZERO,
                volatility: 0.0,
                sharpe_ratio: 0.0,
                max_drawdown: 0.0,
                beta: None,
                calculated_at: chrono::Utc::now(),
            });
        }

        // 提取价值和收益数据
        let values: Vec<Decimal> = portfolio_history.iter()
            .map(|point| point.total_value)
            .collect();

        // 计算收益率
        let returns = self.calculate_returns(&values);

        // 计算各项指标
        let volatility = self.calculate_volatility(&returns);
        let var_95 = self.calculate_var(&returns, 0.95);
        let var_99 = self.calculate_var(&returns, 0.99);
        let cvar_95 = self.calculate_cvar(&returns, 0.95);
        let cvar_99 = self.calculate_cvar(&returns, 0.99);
        let sharpe_ratio = self.calculate_sharpe_ratio(&returns, 0.02); // 假设无风险利率 2%
        let max_drawdown = self.calculate_max_drawdown(&values);

        // 转换为 Decimal
        let portfolio_value = portfolio.total_value;
        let var_95_amount = portfolio_value * Decimal::from_f64(var_95).unwrap_or(Decimal::ZERO);
        let var_99_amount = portfolio_value * Decimal::from_f64(var_99).unwrap_or(Decimal::ZERO);
        let cvar_95_amount = portfolio_value * Decimal::from_f64(cvar_95).unwrap_or(Decimal::ZERO);
        let cvar_99_amount = portfolio_value * Decimal::from_f64(cvar_99).unwrap_or(Decimal::ZERO);

        Ok(RiskMetrics {
            var_95: var_95_amount,
            var_99: var_99_amount,
            cvar_95: cvar_95_amount,
            cvar_99: cvar_99_amount,
            volatility,
            sharpe_ratio,
            max_drawdown,
            beta: None,
            calculated_at: chrono::Utc::now(),
        })
    }

    async fn calculate_asset_metrics(
        &self,
        trading_pair: &TradingPair,
        context: &RiskContext,
    ) -> SigmaXResult<AssetRiskMetrics> {
        debug!("开始计算资产 {:?} 的风险指标", trading_pair);

        // 简化实现，返回默认值
        Ok(AssetRiskMetrics {
            trading_pair: trading_pair.clone(),
            volatility: 0.15, // 默认15%波动率
            beta: Some(1.0),
            correlation_with_portfolio: 0.8,
            liquidity_score: 0.9,
            risk_contribution: 0.1,
            calculated_at: chrono::Utc::now(),
        })
    }
}
