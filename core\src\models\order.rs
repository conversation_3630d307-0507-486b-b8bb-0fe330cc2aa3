//! 交易订单模型

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use validator::{Validate, ValidationError};

use crate::{
    ExchangeId, OrderId, OrderSide, OrderStatus, OrderType, Price, Quantity, StrategyId,
    SigmaXResult,
    traits::{DatabaseConnection, DatabaseOperations}
};

/// SQLx查询结果的强类型结构（未来使用）
///
/// 这个结构体展示了如何为SQLx编译时检查准备强类型结构。
/// 当我们完成schema匹配后，可以使用这种方式获得类型安全。
///
/// # 未来改进
/// 1. 调整字段以匹配实际数据库schema
/// 2. 使用sqlx::FromRow derive宏
/// 3. 实现自动类型转换
#[allow(dead_code)]
#[derive(Debug)]
pub struct OrderRowFuture {
    pub id: Uuid,
    pub strategy_id: Option<Uuid>,
    pub trading_pair_id: i32,  // 实际schema使用的是ID
    pub exchange_id: i32,      // 实际schema使用的是ID
    pub side: String,          // 实际是PostgreSQL enum
    pub order_type: String,    // 实际是PostgreSQL enum
    pub quantity: rust_decimal::Decimal,
    pub price: Option<rust_decimal::Decimal>,
    pub stop_price: Option<rust_decimal::Decimal>,
    pub status: String,        // 实际是PostgreSQL enum
    pub filled_quantity: rust_decimal::Decimal,
    pub average_price: Option<rust_decimal::Decimal>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 交易订单
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct Order {
    pub id: OrderId,
    pub strategy_id: Option<StrategyId>,
    pub exchange_id: ExchangeId,
    #[validate(custom = "validate_trading_pair")]
    pub trading_pair: crate::TradingPair,
    pub side: OrderSide,
    pub order_type: OrderType,
    #[validate(custom = "validate_positive_quantity")]
    pub quantity: Quantity,
    pub price: Option<Price>,
    pub stop_price: Option<Price>,
    pub status: OrderStatus,
    #[validate(custom = "validate_filled_quantity")]
    pub filled_quantity: Quantity,
    pub average_price: Option<Price>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

impl Order {
    /// 执行完整的订单验证（包括跨字段验证）
    pub fn validate_complete(&self) -> SigmaXResult<()> {
        // 首先执行基础验证（通过validator derive宏）
        if let Err(validation_errors) = <Self as Validate>::validate(self) {
            return Err(crate::SigmaXError::from(validation_errors));
        }

        // 然后执行跨字段验证
        // 验证价格（限价单必须有价格）
        if matches!(self.order_type, OrderType::Limit | OrderType::StopLimit) && self.price.is_none() {
            return Err(crate::SigmaXError::ValidationError(
                "Limit orders must have a price".to_string()
            ));
        }

        // 验证成交数量不能超过订单数量
        if self.filled_quantity > self.quantity {
            return Err(crate::SigmaXError::ValidationError(
                "Filled quantity cannot exceed order quantity".to_string()
            ));
        }

        // 验证交易所ID
        if self.exchange_id.to_string().is_empty() {
            return Err(crate::SigmaXError::ValidationError(
                "Exchange ID cannot be empty".to_string()
            ));
        }

        Ok(())
    }

    /// 创建一个新的订单.
    pub fn new(
        exchange_id: ExchangeId,
        trading_pair: crate::TradingPair,
        side: OrderSide,
        order_type: OrderType,
        quantity: Quantity,
        price: Option<Price>,
    ) -> Self {
        let now = Utc::now();
        Self {
            id: Uuid::new_v4(),
            strategy_id: None,
            exchange_id,
            trading_pair,
            side,
            order_type,
            quantity,
            price,
            stop_price: None,
            status: OrderStatus::Pending,
            filled_quantity: Quantity::ZERO,
            average_price: None,
            created_at: now,
            updated_at: now,
        }
    }

    /// 更新订单状态.
    pub fn update_status(&mut self, status: OrderStatus) {
        self.status = status;
        self.updated_at = Utc::now();
    }

    /// 更新成交信息.
    pub fn update_fill(&mut self, filled_quantity: Quantity, average_price: Option<Price>) {
        self.filled_quantity = filled_quantity;
        self.average_price = average_price;
        self.updated_at = Utc::now();

        // 根据成交情况更新状态
        if filled_quantity >= self.quantity {
            self.status = OrderStatus::Filled;
        } else if filled_quantity > Quantity::ZERO {
            self.status = OrderStatus::PartiallyFilled;
        }
    }
}

impl Default for Order {
    fn default() -> Self {
        let now = Utc::now();
        Self {
            id: Uuid::new_v4(),
            strategy_id: None,
            exchange_id: "binance".to_string().into(),
            trading_pair: crate::TradingPair::new("BTC".to_string(), "USDT".to_string()),
            side: OrderSide::Buy,
            order_type: OrderType::Limit,
            quantity: rust_decimal::Decimal::new(1, 3), // 0.001
            price: Some(rust_decimal::Decimal::new(50000, 0)), // 50000
            stop_price: None,
            status: OrderStatus::Pending,
            filled_quantity: Quantity::ZERO,
            average_price: None,
            created_at: now,
            updated_at: now,
        }
    }
}

impl Order {
    /// 取消订单.
    pub fn cancel(&mut self) {
        self.status = OrderStatus::Cancelled;
        self.updated_at = Utc::now();
    }

    /// 获取剩余未成交数量.
    pub fn remaining_quantity(&self) -> Quantity {
        self.quantity - self.filled_quantity
    }

    /// 检查订单是否已完成（最终状态）.
    pub fn is_completed(&self) -> bool {
        matches!(self.status, OrderStatus::Filled | OrderStatus::Cancelled | OrderStatus::Rejected | OrderStatus::Expired)
    }
}

#[async_trait::async_trait]
impl DatabaseOperations<Order> for Order {
    /// 将订单信息保存或更新到数据库.
    ///
    /// # 迁移策略
    /// 当前保持与现有DatabaseConnection trait的兼容性，
    /// 同时为未来的SQLx迁移做准备。
    ///
    /// # 未来改进
    /// 可以通过以下方式使用SQLx编译时检查：
    /// 1. 创建SqlxQueryHelper实例
    /// 2. 调用helper.save_order(self)
    /// 3. 获得编译时SQL验证和类型安全
    async fn save(&self, db: &dyn DatabaseConnection) -> SigmaXResult<()> {
        // 🔥 改进：减少硬编码，但保持兼容性
        let query = r#"
            INSERT INTO orders (
                id, strategy_id, exchange_id, trading_pair_base, trading_pair_quote,
                side, order_type, quantity, price, stop_price, status,
                filled_quantity, average_price, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
            ON CONFLICT (id) DO UPDATE SET
                status = EXCLUDED.status,
                filled_quantity = EXCLUDED.filled_quantity,
                average_price = EXCLUDED.average_price,
                updated_at = EXCLUDED.updated_at
        "#;

        // 改进的参数处理 - 减少字符串转换
        let params = vec![
            self.id.to_string(),
            self.strategy_id.map(|id| id.to_string()).unwrap_or_else(|| "NULL".to_string()),
            self.exchange_id.to_string(),
            self.trading_pair.base.clone(),
            self.trading_pair.quote.clone(),
            format!("{:?}", self.side),
            format!("{:?}", self.order_type),
            self.quantity.to_string(),
            self.price.map(|p| p.to_string()).unwrap_or_else(|| "NULL".to_string()),
            self.stop_price.map(|p| p.to_string()).unwrap_or_else(|| "NULL".to_string()),
            format!("{:?}", self.status),
            self.filled_quantity.to_string(),
            self.average_price.map(|p| p.to_string()).unwrap_or_else(|| "NULL".to_string()),
            self.created_at.to_rfc3339(),
            self.updated_at.to_rfc3339(),
        ];

        db.execute_query(query, &params).await?;
        Ok(())
    }

    /// 从数据库加载订单.
    ///
    /// # 改进的错误处理
    /// 使用更健壮的数据解析逻辑，减少运行时错误
    ///
    /// # 未来SQLx迁移
    /// 可以通过SqlxQueryHelper.load_order(id)获得编译时检查
    async fn load(id: Uuid, db: &dyn DatabaseConnection) -> SigmaXResult<Option<Order>> {
        let query = r#"
            SELECT id, strategy_id, exchange_id, trading_pair_base, trading_pair_quote,
                   side, order_type, status, quantity, price, stop_price,
                   filled_quantity, average_price, created_at, updated_at
            FROM orders WHERE id = $1
        "#;
        let params = vec![id.to_string()];

        if let Some(row) = db.query_one(query, &params).await? {
            // 🔥 改进：更健壮的数据解析
            let order = Order {
                id: Uuid::parse_str(row.get("id").unwrap().as_str().unwrap())
                    .map_err(|e| crate::SigmaXError::InvalidParameter(format!("Invalid UUID: {}", e)))?,
                strategy_id: row.get("strategy_id").and_then(|v| v.as_str())
                    .and_then(|s| if s == "NULL" { None } else { Some(Uuid::parse_str(s).ok()?) }),
                exchange_id: ExchangeId::from(row.get("exchange_id").unwrap().as_str().unwrap()),
                trading_pair: crate::TradingPair::new(
                    row.get("trading_pair_base").unwrap().as_str().unwrap().to_string(),
                    row.get("trading_pair_quote").unwrap().as_str().unwrap().to_string(),
                ),
                side: match row.get("side").unwrap().as_str().unwrap() {
                    "Buy" => OrderSide::Buy,
                    "Sell" => OrderSide::Sell,
                    _ => return Err(crate::SigmaXError::InvalidParameter("Invalid order side".to_string())),
                },
                order_type: match row.get("order_type").unwrap().as_str().unwrap() {
                    "Market" => OrderType::Market,
                    "Limit" => OrderType::Limit,
                    "StopLoss" => OrderType::StopLoss,
                    "StopLimit" => OrderType::StopLimit,
                    _ => return Err(crate::SigmaXError::InvalidParameter("Invalid order type".to_string())),
                },
                status: match row.get("status").unwrap().as_str().unwrap() {
                    "Pending" => OrderStatus::Pending,
                    "Filled" => OrderStatus::Filled,
                    "PartiallyFilled" => OrderStatus::PartiallyFilled,
                    "Cancelled" => OrderStatus::Cancelled,
                    "Rejected" => OrderStatus::Rejected,
                    "Expired" => OrderStatus::Expired,
                    _ => return Err(crate::SigmaXError::InvalidParameter("Invalid order status".to_string())),
                },
                quantity: std::str::FromStr::from_str(row.get("quantity").unwrap().as_str().unwrap())
                    .map_err(|e| crate::SigmaXError::InvalidParameter(format!("Invalid quantity: {}", e)))?,
                price: row.get("price").and_then(|v| v.as_str())
                    .map(|s| std::str::FromStr::from_str(s))
                    .transpose()
                    .map_err(|e| crate::SigmaXError::InvalidParameter(format!("Invalid price: {}", e)))?,
                stop_price: row.get("stop_price").and_then(|v| v.as_str())
                    .map(|s| std::str::FromStr::from_str(s))
                    .transpose()
                    .map_err(|e| crate::SigmaXError::InvalidParameter(format!("Invalid stop_price: {}", e)))?,
                filled_quantity: std::str::FromStr::from_str(row.get("filled_quantity").unwrap().as_str().unwrap())
                    .map_err(|e| crate::SigmaXError::InvalidParameter(format!("Invalid filled_quantity: {}", e)))?,
                average_price: row.get("average_price").and_then(|v| v.as_str())
                    .map(|s| std::str::FromStr::from_str(s))
                    .transpose()
                    .map_err(|e| crate::SigmaXError::InvalidParameter(format!("Invalid average_price: {}", e)))?,
                created_at: chrono::DateTime::parse_from_rfc3339(row.get("created_at").unwrap().as_str().unwrap())
                    .map_err(|e| crate::SigmaXError::InvalidParameter(format!("Invalid created_at: {}", e)))?
                    .with_timezone(&chrono::Utc),
                updated_at: chrono::DateTime::parse_from_rfc3339(row.get("updated_at").unwrap().as_str().unwrap())
                    .map_err(|e| crate::SigmaXError::InvalidParameter(format!("Invalid updated_at: {}", e)))?
                    .with_timezone(&chrono::Utc),
            };
            Ok(Some(order))
        } else {
            Ok(None)
        }
    }

    /// 更新数据库中的订单记录.
    async fn update(&self, db: &dyn DatabaseConnection) -> SigmaXResult<()> {
        let query = r#"
            UPDATE orders SET
                status = $2,
                filled_quantity = $3,
                average_price = $4,
                updated_at = $5
            WHERE id = $1
        "#;

        let status_str = format!("{:?}", self.status);
        let filled_quantity_str = self.filled_quantity.to_string();
        let average_price_str = self.average_price.map(|p| p.to_string()).unwrap_or_else(|| "NULL".to_string());
        let updated_at_str = self.updated_at.to_rfc3339();

        let params = vec![
            self.id.to_string(),
            status_str,
            filled_quantity_str,
            average_price_str,
            updated_at_str,
        ];

        db.execute_query(query, &params).await?;
        Ok(())
    }

    /// 从数据库删除订单.
    async fn delete(id: Uuid, db: &dyn DatabaseConnection) -> SigmaXResult<()> {
        let query = "DELETE FROM orders WHERE id = $1";
        let params = vec![id.to_string()];
        db.execute_query(query, &params).await?;
        Ok(())
    }
}



// ============================================================================
// 自定义验证函数 (用于validator derive宏)
// ============================================================================

/// 验证交易对
fn validate_trading_pair(trading_pair: &crate::TradingPair) -> Result<(), ValidationError> {
    if trading_pair.base.is_empty() || trading_pair.quote.is_empty() {
        return Err(ValidationError::new("Trading pair base and quote cannot be empty"));
    }
    Ok(())
}

/// 验证数量为正数
fn validate_positive_quantity(quantity: &Quantity) -> Result<(), ValidationError> {
    if *quantity <= Quantity::ZERO {
        return Err(ValidationError::new("Quantity must be positive"));
    }
    Ok(())
}



/// 验证成交数量
fn validate_filled_quantity(filled_quantity: &Quantity) -> Result<(), ValidationError> {
    if *filled_quantity < Quantity::ZERO {
        return Err(ValidationError::new("Filled quantity cannot be negative"));
    }
    Ok(())
}