//! FIFO分析错误类型定义

use std::fmt;

/// FIFO分析过程中可能出现的错误类型
#[derive(Debug, Clone, PartialEq)]
pub enum FifoAnalysisError {
    /// 输入数据无效
    InvalidInput(String),
    
    /// 计算错误
    CalculationError(String),
    
    /// 数据不一致
    DataInconsistency(String),
    
    /// 内部错误
    InternalError(String),
}

impl fmt::Display for FifoAnalysisError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            Self::InvalidInput(msg) => write!(f, "Invalid input: {}", msg),
            Self::CalculationError(msg) => write!(f, "Calculation error: {}", msg),
            Self::DataInconsistency(msg) => write!(f, "Data inconsistency: {}", msg),
            Self::InternalError(msg) => write!(f, "Internal error: {}", msg),
        }
    }
}

impl std::error::Error for FifoAnalysisError {}

/// 从SigmaXError转换为FifoAnalysisError
impl From<sigmax_core::SigmaXError> for FifoAnalysisError {
    fn from(err: sigmax_core::SigmaXError) -> Self {
        Self::InternalError(err.to_string())
    }
}

/// 从rust_decimal::Error转换为FifoAnalysisError
impl From<rust_decimal::Error> for FifoAnalysisError {
    fn from(err: rust_decimal::Error) -> Self {
        Self::CalculationError(format!("Decimal calculation error: {}", err))
    }
}
