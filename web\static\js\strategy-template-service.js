/**
 * 策略模板服务
 * 负责管理策略模板的获取、选择和配置
 */
class StrategyTemplateService {
    constructor(config) {
        this.config = config;
        this.templates = [];
        this.selectedTemplate = null;
        this.customConfig = {};
    }

    /**
     * 获取所有策略模板
     * @param {Object} filters 过滤条件
     * @returns {Promise<Array>} 策略模板列表
     */
    async getTemplates(filters = {}) {
        try {
            // 首先尝试使用新的策略支持API
            const url = `/api/v1/strategies/supported?include_details=true`;

            this.config.log('debug', '获取策略模板列表:', url);

            const response = await fetch(url);
            const result = await response.json();

            if (result.code === 200 && result.data && result.data.strategies) {
                // 转换API数据格式为模板格式
                this.templates = result.data.strategies.map(strategy => ({
                    id: strategy.strategy_type,
                    name: strategy.name,
                    description: strategy.description,
                    strategy_type: strategy.strategy_type,
                    category: this.getCategoryFromStrategyType(strategy.strategy_type),
                    difficulty_level: this.getDifficultyFromStrategy(strategy),
                    risk_level: this.getRiskLevelFromStrategy(strategy),
                    recommended_capital: this.getRecommendedCapital(strategy.strategy_type),
                    available: strategy.available,
                    default_config: strategy.config_template || {},
                    required_parameters: strategy.required_parameters || [],
                    optional_parameters: strategy.optional_parameters || [],
                    icon: this.getStrategyIcon(strategy.strategy_type),
                    color: this.getStrategyColor(strategy.strategy_type),
                    tags: this.getStrategyTags(strategy.strategy_type),
                    recommended_timeframes: ['1h', '4h', '1d'] // 默认时间框架
                }));

                // 应用过滤器
                let filteredTemplates = this.templates;
                if (filters.category) {
                    filteredTemplates = filteredTemplates.filter(t => t.category === filters.category);
                }
                if (filters.difficulty) {
                    filteredTemplates = filteredTemplates.filter(t => t.difficulty_level === filters.difficulty);
                }

                this.config.log('info', `获取到 ${this.templates.length} 个策略模板`);
                return { templates: filteredTemplates, total: filteredTemplates.length };
            } else {
                throw new Error(result.message || '获取策略模板失败');
            }
        } catch (error) {
            this.config.log('error', '获取策略模板失败:', error.message);
            // 回退到本地模板
            this.templates = this.getLocalTemplates();
            return { templates: this.templates, total: this.templates.length };
        }
    }

    /**
     * 根据ID获取策略模板详情
     * @param {string} templateId 模板ID
     * @returns {Promise<Object>} 策略模板详情
     */
    async getTemplateById(templateId) {
        try {
            const url = `/api/v1/strategy-templates/${templateId}`;

            this.config.log('debug', '获取策略模板详情:', templateId);

            const response = await fetch(url);
            const result = await response.json();

            if (result.code === 200) {
                this.config.log('info', '获取策略模板详情成功:', result.data.name);
                return result.data;
            } else {
                throw new Error(result.message || '获取策略模板详情失败');
            }
        } catch (error) {
            this.config.log('error', '获取策略模板详情失败:', error.message);
            throw error;
        }
    }

    /**
     * 选择策略模板
     * @param {string} templateId 模板ID
     */
    async selectTemplate(templateId) {
        try {
            this.selectedTemplate = await this.getTemplateById(templateId);
            this.customConfig = { ...this.selectedTemplate.default_config };

            this.config.log('info', '选择策略模板:', this.selectedTemplate.name);
            return this.selectedTemplate;
        } catch (error) {
            this.config.log('error', '选择策略模板失败:', error.message);
            throw error;
        }
    }

    /**
     * 更新自定义配置
     * @param {Object} config 配置更新
     */
    updateCustomConfig(config) {
        this.customConfig = { ...this.customConfig, ...config };
        this.config.log('debug', '更新自定义配置:', this.customConfig);
    }

    /**
     * 获取最终的策略配置
     * @returns {Object} 最终配置
     */
    getFinalConfig() {
        if (!this.selectedTemplate) {
            throw new Error('未选择策略模板');
        }

        return {
            strategy_type: this.selectedTemplate.strategy_type,
            template_id: this.selectedTemplate.id,
            parameters: this.customConfig
        };
    }

    /**
     * 验证策略配置
     * @param {Object} config 配置对象
     * @returns {Promise<Object>} 验证结果
     */
    async validateConfig(config = null) {
        if (!this.selectedTemplate) {
            throw new Error('未选择策略模板');
        }

        const configToValidate = config || this.customConfig;

        try {
            const url = `/api/v1/strategy-templates/${this.selectedTemplate.id}/validate`;

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ config: configToValidate })
            });

            const result = await response.json();

            if (result.code === 200) {
                this.config.log('info', '策略配置验证完成:', result.data);
                return result.data;
            } else {
                throw new Error(result.message || '配置验证失败');
            }
        } catch (error) {
            this.config.log('error', '配置验证失败:', error.message);
            throw error;
        }
    }

    /**
     * 获取推荐配置
     * @param {string} templateId 模板ID
     * @returns {Promise<Object>} 推荐配置
     */
    async getRecommendedConfig(templateId) {
        try {
            const url = `/api/v1/strategy-templates/${templateId}/recommended-config`;

            const response = await fetch(url);
            const result = await response.json();

            if (result.code === 200) {
                this.config.log('info', '获取推荐配置成功');
                return result.data;
            } else {
                throw new Error(result.message || '获取推荐配置失败');
            }
        } catch (error) {
            this.config.log('error', '获取推荐配置失败:', error.message);
            throw error;
        }
    }

    /**
     * 根据难度等级过滤模板
     * @param {string} difficulty 难度等级
     * @returns {Array} 过滤后的模板列表
     */
    filterByDifficulty(difficulty) {
        return this.templates.filter(template => template.difficulty_level === difficulty);
    }

    /**
     * 根据类别过滤模板
     * @param {string} category 类别
     * @returns {Array} 过滤后的模板列表
     */
    filterByCategory(category) {
        return this.templates.filter(template => template.category === category);
    }

    /**
     * 根据风险等级过滤模板
     * @param {string} riskLevel 风险等级
     * @returns {Array} 过滤后的模板列表
     */
    filterByRiskLevel(riskLevel) {
        return this.templates.filter(template => template.risk_level === riskLevel);
    }

    /**
     * 获取适合新手的模板
     * @returns {Array} 新手友好的模板列表
     */
    getBeginnerFriendlyTemplates() {
        return this.templates.filter(template =>
            template.difficulty_level === 'beginner' &&
            (template.risk_level === 'conservative' || template.risk_level === 'moderate')
        );
    }

    /**
     * 根据资金规模推荐模板
     * @param {number} capital 资金规模
     * @returns {Array} 推荐的模板列表
     */
    getTemplatesByCapital(capital) {
        return this.templates.filter(template => {
            if (!template.recommended_capital) return true;
            return capital >= template.recommended_capital * 0.5; // 允许50%的弹性
        });
    }

    /**
     * 获取当前选择的模板
     * @returns {Object|null} 当前选择的模板
     */
    getSelectedTemplate() {
        return this.selectedTemplate;
    }

    /**
     * 获取当前自定义配置
     * @returns {Object} 当前自定义配置
     */
    getCustomConfig() {
        return { ...this.customConfig };
    }

    /**
     * 重置选择
     */
    reset() {
        this.selectedTemplate = null;
        this.customConfig = {};
        this.config.log('info', '策略模板选择已重置');
    }

    /**
     * 格式化模板显示信息
     * @param {Object} template 模板对象
     * @returns {Object} 格式化后的显示信息
     */
    formatTemplateDisplay(template) {
        const difficultyLabels = {
            'beginner': '新手',
            'intermediate': '中级',
            'advanced': '高级',
            'expert': '专家'
        };

        const riskLabels = {
            'conservative': '保守',
            'moderate': '稳健',
            'aggressive': '激进',
            'highrisk': '高风险'
        };

        return {
            ...template,
            difficulty_label: difficultyLabels[template.difficulty_level] || template.difficulty_level,
            risk_label: riskLabels[template.risk_level] || template.risk_level,
            capital_display: template.recommended_capital ?
                `${template.recommended_capital.toLocaleString()} USDT` : '不限',
            timeframes_display: template.recommended_timeframes.join(', ') || '不限'
        };
    }

    /**
     * 检查模板是否适合当前用户
     * @param {Object} template 模板对象
     * @param {Object} userProfile 用户档案
     * @returns {Object} 适合性分析
     */
    checkTemplateSuitability(template, userProfile = {}) {
        const suitability = {
            suitable: true,
            score: 100,
            warnings: [],
            suggestions: []
        };

        // 检查资金要求
        if (template.recommended_capital && userProfile.capital) {
            if (userProfile.capital < template.recommended_capital * 0.3) {
                suitability.suitable = false;
                suitability.score -= 50;
                suitability.warnings.push(`资金不足，建议至少 ${template.recommended_capital} USDT`);
            } else if (userProfile.capital < template.recommended_capital) {
                suitability.score -= 20;
                suitability.suggestions.push(`建议增加资金到 ${template.recommended_capital} USDT 以获得更好效果`);
            }
        }

        // 检查经验等级
        if (userProfile.experience) {
            const experienceScore = {
                'beginner': 1,
                'intermediate': 2,
                'advanced': 3,
                'expert': 4
            };

            const userScore = experienceScore[userProfile.experience] || 1;
            const templateScore = experienceScore[template.difficulty_level] || 1;

            if (userScore < templateScore) {
                suitability.score -= (templateScore - userScore) * 15;
                suitability.warnings.push('该策略可能超出您的经验水平');
            }
        }

        // 检查风险承受能力
        if (userProfile.risk_tolerance && template.risk_level) {
            const riskScore = {
                'conservative': 1,
                'moderate': 2,
                'aggressive': 3,
                'highrisk': 4
            };

            const userRisk = riskScore[userProfile.risk_tolerance] || 2;
            const templateRisk = riskScore[template.risk_level] || 2;

            if (templateRisk > userRisk + 1) {
                suitability.score -= 30;
                suitability.warnings.push('该策略风险等级可能超出您的承受能力');
            }
        }

        return suitability;
    }

    /**
     * 根据策略类型推断分类
     * @param {string} strategyType - 策略类型
     * @returns {string} 分类
     */
    getCategoryFromStrategyType(strategyType) {
        if (strategyType.includes('grid')) return 'grid';
        if (strategyType.includes('dca')) return 'dca';
        if (strategyType.includes('momentum')) return 'momentum';
        if (strategyType.includes('mean_reversion')) return 'mean_reversion';
        return 'other';
    }

    /**
     * 根据策略信息推断难度等级
     * @param {Object} strategy - 策略信息
     * @returns {string} 难度等级
     */
    getDifficultyFromStrategy(strategy) {
        const strategyType = strategy.strategy_type.toLowerCase();

        if (strategyType.includes('dca')) return 'beginner';
        if (strategyType.includes('grid')) return 'intermediate';
        if (strategyType.includes('momentum')) return 'advanced';
        if (strategyType.includes('mean_reversion')) return 'intermediate';

        return 'intermediate'; // 默认中级
    }

    /**
     * 根据策略信息推断风险等级
     * @param {Object} strategy - 策略信息
     * @returns {string} 风险等级
     */
    getRiskLevelFromStrategy(strategy) {
        const strategyType = strategy.strategy_type.toLowerCase();

        if (strategyType.includes('dca')) return 'conservative';
        if (strategyType.includes('grid')) return 'moderate';
        if (strategyType.includes('momentum')) return 'aggressive';
        if (strategyType.includes('mean_reversion')) return 'moderate';

        return 'moderate'; // 默认稳健
    }

    /**
     * 根据策略类型获取推荐资金
     * @param {string} strategyType - 策略类型
     * @returns {number} 推荐资金
     */
    getRecommendedCapital(strategyType) {
        const mapping = {
            'dca_strategy': 15000,
            'grid': 10000,
            'asymmetric_volatility_grid_strategy': 20000,
            'momentum_strategy': 8000,
            'mean_reversion_strategy': 6000
        };
        return mapping[strategyType] || 10000;
    }

    /**
     * 根据策略类型获取图标
     * @param {string} strategyType - 策略类型
     * @returns {string} 图标类名
     */
    getStrategyIcon(strategyType) {
        const mapping = {
            'dca_strategy': 'fas fa-chart-line',
            'grid': 'fas fa-th',
            'asymmetric_volatility_grid_strategy': 'fas fa-chart-area',
            'momentum_strategy': 'fas fa-rocket',
            'mean_reversion_strategy': 'fas fa-balance-scale'
        };
        return mapping[strategyType] || 'fas fa-chart-bar';
    }

    /**
     * 根据策略类型获取颜色
     * @param {string} strategyType - 策略类型
     * @returns {string} 颜色
     */
    getStrategyColor(strategyType) {
        const mapping = {
            'dca_strategy': 'green',
            'grid': 'blue',
            'asymmetric_volatility_grid_strategy': 'purple',
            'momentum_strategy': 'orange',
            'mean_reversion_strategy': 'teal'
        };
        return mapping[strategyType] || 'blue';
    }

    /**
     * 根据策略类型获取标签
     * @param {string} strategyType - 策略类型
     * @returns {Array} 标签列表
     */
    getStrategyTags(strategyType) {
        const mapping = {
            'dca_strategy': ['长期投资', '新手友好', '低风险'],
            'grid': ['震荡行情', '稳健', '中等风险'],
            'asymmetric_volatility_grid_strategy': ['高级策略', '波动率', '非对称'],
            'momentum_strategy': ['趋势跟随', '高风险', '短期'],
            'mean_reversion_strategy': ['均值回归', '震荡行情', '中等风险']
        };
        return mapping[strategyType] || ['通用策略'];
    }

    /**
     * 获取本地模板列表（作为回退方案）
     * @returns {Array} 本地模板列表
     */
    getLocalTemplates() {
        return [
            {
                id: 'asymmetric_volatility_grid_strategy',
                name: '非对称波动率网格策略',
                description: '基于波动率的非对称网格策略，下跌时密集吸筹，上涨时稀疏止盈',
                strategy_type: 'asymmetric_volatility_grid_strategy',
                category: 'grid',
                difficulty_level: 'advanced',
                risk_level: 'moderate',
                recommended_capital: 20000,
                available: true,
                default_config: {
                    trading_pair: 'BTCUSDT',
                    base_price: 0.0,
                    order_amount: 200.0,
                    down_range_start: -0.02,
                    down_range_end: -0.05,
                    down_grid_count: 10,
                    down_base_quantity: 0.1,
                    up_range_start: 0.02,
                    up_range_end: 0.08,
                    up_grid_count: 6,
                    up_base_quantity: 0.1,
                    volatility_window_hours: 24,
                    volatility_multiplier: 1.2,
                    enable_dynamic_volatility: true,
                    max_position_amount: 2000.0,
                    max_daily_trades: 50,
                    stop_loss_percent: 0.2,
                    strategy_preset: 'Balanced'
                },
                icon: 'fas fa-chart-area',
                color: 'purple',
                tags: ['高级策略', '波动率', '非对称'],
                recommended_timeframes: ['1h', '4h', '1d']
            },
            {
                id: 'dca_strategy',
                name: 'DCA定投策略',
                description: '定期定额投资策略，分散投资风险',
                strategy_type: 'dca_strategy',
                category: 'dca',
                difficulty_level: 'beginner',
                risk_level: 'conservative',
                recommended_capital: 15000,
                available: true,
                default_config: {
                    trading_pair: 'BTCUSDT',
                    investment_amount: 100,
                    investment_interval: '1d',
                    max_investment_count: 30
                },
                icon: 'fas fa-chart-line',
                color: 'green',
                tags: ['长期投资', '新手友好', '低风险'],
                recommended_timeframes: ['1d', '1w']
            }
        ];
    }
}

// 导出服务类
window.StrategyTemplateService = StrategyTemplateService;

// 兼容CommonJS
if (typeof module !== 'undefined' && module.exports) {
    module.exports = StrategyTemplateService;
}
