//! 分析模型
//!
//! 实现各种风险分析模型

use sigmax_interfaces::risk::Portfolio;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;
use rust_decimal::prelude::*;
use std::collections::HashMap;

// ============================================================================
// 相关性模型
// ============================================================================

/// 相关性模型配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CorrelationModelConfig {
    /// 历史数据窗口大小
    pub window_size: u32,
    /// 最小相关性阈值
    pub min_correlation_threshold: Decimal,
    /// 是否使用指数加权
    pub use_exponential_weighting: bool,
    /// 衰减因子
    pub decay_factor: Decimal,
}

impl Default for CorrelationModelConfig {
    fn default() -> Self {
        Self {
            window_size: 252, // 一年交易日
            min_correlation_threshold: Decimal::from_str_exact("-0.95").unwrap(),
            use_exponential_weighting: true,
            decay_factor: Decimal::from_str_exact("0.94").unwrap(),
        }
    }
}

/// 相关性模型
pub struct CorrelationModel {
    config: CorrelationModelConfig,
    historical_data: HashMap<String, Vec<Decimal>>, // 资产价格历史数据
}

impl CorrelationModel {
    /// 创建新的相关性模型
    pub fn new(config: CorrelationModelConfig) -> Self {
        Self {
            config,
            historical_data: HashMap::new(),
        }
    }

    /// 使用默认配置创建相关性模型
    pub fn with_default_config() -> Self {
        Self::new(CorrelationModelConfig::default())
    }

    /// 添加历史价格数据
    pub fn add_price_data(&mut self, asset: String, prices: Vec<Decimal>) {
        self.historical_data.insert(asset, prices);
    }

    /// 计算投资组合相关性
    pub fn calculate_portfolio_correlation(&self, portfolio: &Portfolio) -> CorrelationResult {
        let mut correlation_matrix = HashMap::new();
        let assets: Vec<String> = portfolio.balances.iter().map(|b| b.asset.clone()).collect();

        for asset_i in &assets {
            let mut correlations = HashMap::new();

            for asset_j in &assets {
                let correlation = if asset_i == asset_j {
                    Decimal::ONE
                } else {
                    self.calculate_pairwise_correlation(asset_i, asset_j)
                };

                correlations.insert(asset_j.clone(), correlation);
            }

            correlation_matrix.insert(asset_i.clone(), correlations);
        }

        // 计算投资组合整体相关性风险
        let portfolio_correlation_risk = self.calculate_portfolio_correlation_risk(portfolio, &correlation_matrix);

        // 复制correlation_matrix用于后续计算
        let correlation_matrix_for_calc = correlation_matrix.clone();
        
        CorrelationResult {
            calculated_at: Utc::now(),
            correlation_matrix,
            portfolio_correlation_risk,
            average_correlation: self.calculate_average_correlation(&correlation_matrix_for_calc),
            max_correlation: self.calculate_max_correlation(&correlation_matrix_for_calc),
            min_correlation: self.calculate_min_correlation(&correlation_matrix_for_calc),
        }
    }

    /// 计算两个资产间的相关性
    fn calculate_pairwise_correlation(&self, asset1: &str, asset2: &str) -> Decimal {
        // 如果有历史数据，使用历史数据计算
        if let (Some(prices1), Some(prices2)) = (
            self.historical_data.get(asset1),
            self.historical_data.get(asset2)
        ) {
            self.calculate_correlation_from_prices(prices1, prices2)
        } else {
            // 使用预定义的相关性
            self.get_predefined_correlation(asset1, asset2)
        }
    }

    /// 从价格数据计算相关性
    fn calculate_correlation_from_prices(&self, prices1: &[Decimal], prices2: &[Decimal]) -> Decimal {
        let min_len = prices1.len().min(prices2.len());
        if min_len < 2 {
            return Decimal::ZERO;
        }

        // 计算收益率
        let returns1: Vec<Decimal> = prices1.windows(2)
            .take(min_len - 1)
            .map(|w| (w[1] - w[0]) / w[0])
            .collect();

        let returns2: Vec<Decimal> = prices2.windows(2)
            .take(min_len - 1)
            .map(|w| (w[1] - w[0]) / w[0])
            .collect();

        // 计算皮尔逊相关系数
        self.calculate_pearson_correlation(&returns1, &returns2)
    }

    /// 计算皮尔逊相关系数
    fn calculate_pearson_correlation(&self, x: &[Decimal], y: &[Decimal]) -> Decimal {
        let n = x.len().min(y.len());
        if n < 2 {
            return Decimal::ZERO;
        }

        let mean_x = x.iter().sum::<Decimal>() / Decimal::from(n as u32);
        let mean_y = y.iter().sum::<Decimal>() / Decimal::from(n as u32);

        let mut numerator = Decimal::ZERO;
        let mut sum_sq_x = Decimal::ZERO;
        let mut sum_sq_y = Decimal::ZERO;

        for i in 0..n {
            let diff_x = x[i] - mean_x;
            let diff_y = y[i] - mean_y;

            numerator += diff_x * diff_y;
            sum_sq_x += diff_x * diff_x;
            sum_sq_y += diff_y * diff_y;
        }

        let denominator = (sum_sq_x * sum_sq_y).sqrt().unwrap_or(Decimal::ZERO);

        if denominator > Decimal::ZERO {
            (numerator / denominator).max(self.config.min_correlation_threshold).min(Decimal::ONE)
        } else {
            Decimal::ZERO
        }
    }

    /// 获取预定义相关性
    fn get_predefined_correlation(&self, asset1: &str, asset2: &str) -> Decimal {
        match (asset1.to_uppercase().as_str(), asset2.to_uppercase().as_str()) {
            ("BTC", "ETH") | ("ETH", "BTC") => Decimal::from_str_exact("0.7").unwrap(),
            ("USDT", "USDC") | ("USDC", "USDT") => Decimal::from_str_exact("0.95").unwrap(),
            ("USDT", "DAI") | ("DAI", "USDT") => Decimal::from_str_exact("0.9").unwrap(),
            ("USDC", "DAI") | ("DAI", "USDC") => Decimal::from_str_exact("0.92").unwrap(),
            (a, b) if a.contains("USD") && b.contains("USD") => Decimal::from_str_exact("0.85").unwrap(),
            (a, b) if (a == "BTC" || a == "ETH") && b.contains("USD") => Decimal::from_str_exact("-0.1").unwrap(),
            (a, b) if a.contains("USD") && (b == "BTC" || b == "ETH") => Decimal::from_str_exact("-0.1").unwrap(),
            _ => Decimal::from_str_exact("0.3").unwrap(), // 默认正相关
        }
    }

    /// 计算投资组合相关性风险
    fn calculate_portfolio_correlation_risk(&self, portfolio: &Portfolio, correlation_matrix: &HashMap<String, HashMap<String, Decimal>>) -> Decimal {
        let mut risk_score = Decimal::ZERO;
        let total_value = portfolio.total_value;

        if total_value <= Decimal::ZERO {
            return risk_score;
        }

        // 计算加权相关性风险
        for balance_i in &portfolio.balances {
            let weight_i = (balance_i.total * balance_i.price.unwrap_or(Decimal::ONE)) / total_value;

            for balance_j in &portfolio.balances {
                if balance_i.asset != balance_j.asset {
                    let weight_j = (balance_j.total * balance_j.price.unwrap_or(Decimal::ONE)) / total_value;

                    if let Some(correlations) = correlation_matrix.get(&balance_i.asset) {
                        if let Some(&correlation) = correlations.get(&balance_j.asset) {
                            // 高正相关增加风险
                            if correlation > Decimal::from_str_exact("0.5").unwrap() {
                                risk_score += weight_i * weight_j * correlation;
                            }
                        }
                    }
                }
            }
        }

        risk_score.min(Decimal::ONE)
    }

    /// 计算平均相关性
    fn calculate_average_correlation(&self, correlation_matrix: &HashMap<String, HashMap<String, Decimal>>) -> Decimal {
        let mut sum = Decimal::ZERO;
        let mut count = 0u32;

        for (asset_i, correlations) in correlation_matrix {
            for (asset_j, &correlation) in correlations {
                if asset_i != asset_j {
                    sum += correlation;
                    count += 1;
                }
            }
        }

        if count > 0 {
            sum / Decimal::from(count)
        } else {
            Decimal::ZERO
        }
    }

    /// 计算最大相关性
    fn calculate_max_correlation(&self, correlation_matrix: &HashMap<String, HashMap<String, Decimal>>) -> Decimal {
        let mut max_correlation = Decimal::from(-1);

        for (asset_i, correlations) in correlation_matrix {
            for (asset_j, &correlation) in correlations {
                if asset_i != asset_j && correlation > max_correlation {
                    max_correlation = correlation;
                }
            }
        }

        max_correlation
    }

    /// 计算最小相关性
    fn calculate_min_correlation(&self, correlation_matrix: &HashMap<String, HashMap<String, Decimal>>) -> Decimal {
        let mut min_correlation = Decimal::ONE;

        for (asset_i, correlations) in correlation_matrix {
            for (asset_j, &correlation) in correlations {
                if asset_i != asset_j && correlation < min_correlation {
                    min_correlation = correlation;
                }
            }
        }

        min_correlation
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CorrelationResult {
    pub calculated_at: DateTime<Utc>,
    pub correlation_matrix: HashMap<String, HashMap<String, Decimal>>,
    pub portfolio_correlation_risk: Decimal,
    pub average_correlation: Decimal,
    pub max_correlation: Decimal,
    pub min_correlation: Decimal,
}

// ============================================================================
// 波动率模型
// ============================================================================

/// 波动率模型配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VolatilityModelConfig {
    /// 历史数据窗口大小
    pub window_size: u32,
    /// 是否使用GARCH模型
    pub use_garch: bool,
    /// GARCH参数
    pub garch_alpha: Decimal,
    pub garch_beta: Decimal,
    /// 是否使用指数加权移动平均
    pub use_ewma: bool,
    /// EWMA衰减因子
    pub ewma_lambda: Decimal,
}

impl Default for VolatilityModelConfig {
    fn default() -> Self {
        Self {
            window_size: 30,
            use_garch: false,
            garch_alpha: Decimal::from_str_exact("0.1").unwrap(),
            garch_beta: Decimal::from_str_exact("0.85").unwrap(),
            use_ewma: true,
            ewma_lambda: Decimal::from_str_exact("0.94").unwrap(),
        }
    }
}

/// 波动率模型
pub struct VolatilityModel {
    config: VolatilityModelConfig,
    historical_data: HashMap<String, Vec<Decimal>>,
    volatility_cache: HashMap<String, VolatilityData>,
}

impl VolatilityModel {
    /// 创建新的波动率模型
    pub fn new(config: VolatilityModelConfig) -> Self {
        Self {
            config,
            historical_data: HashMap::new(),
            volatility_cache: HashMap::new(),
        }
    }

    /// 使用默认配置创建波动率模型
    pub fn with_default_config() -> Self {
        Self::new(VolatilityModelConfig::default())
    }

    /// 添加历史价格数据
    pub fn add_price_data(&mut self, asset: String, prices: Vec<Decimal>) {
        self.historical_data.insert(asset, prices);
    }

    /// 计算投资组合波动率
    pub fn calculate_portfolio_volatility(&mut self, portfolio: &Portfolio) -> VolatilityResult {
        let mut asset_volatilities = HashMap::new();
        let mut portfolio_volatility = Decimal::ZERO;

        // 计算每个资产的波动率
        for balance in &portfolio.balances {
            let volatility = self.calculate_asset_volatility(&balance.asset);
            asset_volatilities.insert(balance.asset.clone(), volatility);
        }

        // 计算投资组合整体波动率
        portfolio_volatility = self.calculate_weighted_portfolio_volatility(portfolio, &asset_volatilities);

        // 计算波动率聚类
        let volatility_clustering = self.detect_volatility_clustering(&asset_volatilities);

        // 复制asset_volatilities用于后续计算
        let asset_volatilities_for_calc = asset_volatilities.clone();
        
        VolatilityResult {
            calculated_at: Utc::now(),
            asset_volatilities,
            portfolio_volatility,
            volatility_clustering,
            volatility_forecast: self.forecast_volatility(portfolio),
            risk_adjusted_returns: self.calculate_risk_adjusted_returns(portfolio, &asset_volatilities_for_calc),
        }
    }

    /// 计算单个资产的波动率
    fn calculate_asset_volatility(&mut self, asset: &str) -> Decimal {
        // 检查缓存
        if let Some(cached_data) = self.volatility_cache.get(asset) {
            if cached_data.is_recent() {
                return cached_data.volatility;
            }
        }

        let volatility = if let Some(prices) = self.historical_data.get(asset) {
            if self.config.use_ewma {
                self.calculate_ewma_volatility(prices)
            } else if self.config.use_garch {
                self.calculate_garch_volatility(prices)
            } else {
                self.calculate_simple_volatility(prices)
            }
        } else {
            self.get_default_volatility(asset)
        };

        // 更新缓存
        self.volatility_cache.insert(asset.to_string(), VolatilityData {
            volatility,
            calculated_at: Utc::now(),
        });

        volatility
    }

    /// 计算简单历史波动率
    fn calculate_simple_volatility(&self, prices: &[Decimal]) -> Decimal {
        if prices.len() < 2 {
            return self.get_default_volatility("UNKNOWN");
        }

        // 计算收益率
        let returns: Vec<Decimal> = prices.windows(2)
            .map(|w| (w[1] - w[0]) / w[0])
            .collect();

        if returns.is_empty() {
            return self.get_default_volatility("UNKNOWN");
        }

        // 计算标准差
        let mean = returns.iter().sum::<Decimal>() / Decimal::from(returns.len() as u32);
        let variance = returns.iter()
            .map(|r| (*r - mean) * (*r - mean))
            .sum::<Decimal>() / Decimal::from(returns.len() as u32);

        variance.sqrt().unwrap_or(self.get_default_volatility("UNKNOWN"))
    }

    /// 计算EWMA波动率
    fn calculate_ewma_volatility(&self, prices: &[Decimal]) -> Decimal {
        if prices.len() < 2 {
            return self.get_default_volatility("UNKNOWN");
        }

        let returns: Vec<Decimal> = prices.windows(2)
            .map(|w| (w[1] - w[0]) / w[0])
            .collect();

        if returns.is_empty() {
            return self.get_default_volatility("UNKNOWN");
        }

        let lambda = self.config.ewma_lambda;
        let mut variance = returns[0] * returns[0]; // 初始方差

        for return_val in returns.iter().skip(1) {
            variance = lambda * variance + (Decimal::ONE - lambda) * return_val * return_val;
        }

        variance.sqrt().unwrap_or(self.get_default_volatility("UNKNOWN"))
    }

    /// 计算GARCH波动率（简化版）
    fn calculate_garch_volatility(&self, prices: &[Decimal]) -> Decimal {
        if prices.len() < 10 {
            return self.calculate_simple_volatility(prices);
        }

        let returns: Vec<Decimal> = prices.windows(2)
            .map(|w| (w[1] - w[0]) / w[0])
            .collect();

        let alpha = self.config.garch_alpha;
        let beta = self.config.garch_beta;
        let omega = Decimal::from_str_exact("0.000001").unwrap(); // 常数项

        let mut variance = returns[0] * returns[0];

        for return_val in returns.iter().skip(1) {
            variance = omega + alpha * return_val * return_val + beta * variance;
        }

        variance.sqrt().unwrap_or(self.get_default_volatility("UNKNOWN"))
    }

    /// 计算加权投资组合波动率
    fn calculate_weighted_portfolio_volatility(&self, portfolio: &Portfolio, asset_volatilities: &HashMap<String, Decimal>) -> Decimal {
        if portfolio.total_value <= Decimal::ZERO {
            return Decimal::ZERO;
        }

        let mut weighted_variance = Decimal::ZERO;

        for balance in &portfolio.balances {
            let asset_value = balance.total * balance.price.unwrap_or(Decimal::ONE);
            let weight = asset_value / portfolio.total_value;
            let volatility = asset_volatilities.get(&balance.asset).copied().unwrap_or_default();

            weighted_variance += weight * weight * volatility * volatility;
        }

        // 简化：不考虑相关性
        weighted_variance.sqrt().unwrap_or(Decimal::ZERO)
    }

    /// 检测波动率聚类
    fn detect_volatility_clustering(&self, asset_volatilities: &HashMap<String, Decimal>) -> VolatilityClustering {
        let volatilities: Vec<Decimal> = asset_volatilities.values().copied().collect();

        if volatilities.is_empty() {
            return VolatilityClustering {
                has_clustering: false,
                high_volatility_assets: vec![],
                low_volatility_assets: vec![],
                clustering_score: Decimal::ZERO,
            };
        }

        let mean_volatility = volatilities.iter().sum::<Decimal>() / Decimal::from(volatilities.len() as u32);
        let threshold_high = mean_volatility * Decimal::from_str_exact("1.5").unwrap();
        let threshold_low = mean_volatility * Decimal::from_str_exact("0.5").unwrap();

        let mut high_volatility_assets = Vec::new();
        let mut low_volatility_assets = Vec::new();

        for (asset, &volatility) in asset_volatilities {
            if volatility > threshold_high {
                high_volatility_assets.push(asset.clone());
            } else if volatility < threshold_low {
                low_volatility_assets.push(asset.clone());
            }
        }

        let clustering_score = if volatilities.len() > 1 {
            let variance = volatilities.iter()
                .map(|v| (*v - mean_volatility) * (*v - mean_volatility))
                .sum::<Decimal>() / Decimal::from(volatilities.len() as u32);
            variance / (mean_volatility * mean_volatility)
        } else {
            Decimal::ZERO
        };

        VolatilityClustering {
            has_clustering: clustering_score > Decimal::from_str_exact("0.5").unwrap(),
            high_volatility_assets,
            low_volatility_assets,
            clustering_score,
        }
    }

    /// 预测波动率
    fn forecast_volatility(&self, portfolio: &Portfolio) -> HashMap<String, Decimal> {
        let mut forecasts = HashMap::new();

        for balance in &portfolio.balances {
            // 简化的预测：基于当前波动率和趋势
            let current_volatility = self.get_default_volatility(&balance.asset);
            let forecast = current_volatility * Decimal::from_str_exact("1.1").unwrap(); // 假设略有上升
            forecasts.insert(balance.asset.clone(), forecast);
        }

        forecasts
    }

    /// 计算风险调整收益
    fn calculate_risk_adjusted_returns(&self, portfolio: &Portfolio, asset_volatilities: &HashMap<String, Decimal>) -> HashMap<String, Decimal> {
        let mut risk_adjusted_returns = HashMap::new();

        for balance in &portfolio.balances {
            let volatility = asset_volatilities.get(&balance.asset).copied().unwrap_or_default();
            // 假设年化收益率为10%
            let expected_return = Decimal::from_str_exact("0.1").unwrap();

            let risk_adjusted_return = if volatility > Decimal::ZERO {
                expected_return / volatility // 简化的夏普比率
            } else {
                Decimal::ZERO
            };

            risk_adjusted_returns.insert(balance.asset.clone(), risk_adjusted_return);
        }

        risk_adjusted_returns
    }

    /// 获取默认波动率
    fn get_default_volatility(&self, asset: &str) -> Decimal {
        match asset.to_uppercase().as_str() {
            "BTC" => Decimal::from_str_exact("0.4").unwrap(),
            "ETH" => Decimal::from_str_exact("0.5").unwrap(),
            "USDT" | "USDC" | "DAI" => Decimal::from_str_exact("0.01").unwrap(),
            _ => Decimal::from_str_exact("0.6").unwrap(),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VolatilityResult {
    pub calculated_at: DateTime<Utc>,
    pub asset_volatilities: HashMap<String, Decimal>,
    pub portfolio_volatility: Decimal,
    pub volatility_clustering: VolatilityClustering,
    pub volatility_forecast: HashMap<String, Decimal>,
    pub risk_adjusted_returns: HashMap<String, Decimal>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VolatilityClustering {
    pub has_clustering: bool,
    pub high_volatility_assets: Vec<String>,
    pub low_volatility_assets: Vec<String>,
    pub clustering_score: Decimal,
}

#[derive(Debug, Clone)]
struct VolatilityData {
    volatility: Decimal,
    calculated_at: DateTime<Utc>,
}

impl VolatilityData {
    fn is_recent(&self) -> bool {
        let now = Utc::now();
        let age = now - self.calculated_at;
        age.num_minutes() < 30 // 30分钟内的数据认为是新鲜的
    }
}