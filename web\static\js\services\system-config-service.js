/**
 * SigmaX Trading System - 系统配置管理服务
 * 
 * 提供完整的系统配置管理功能，包括：
 * - 系统配置 CRUD 操作
 * - 配置验证和缓存
 * - 批量操作支持
 * - 错误处理和重试机制
 */

class SystemConfigService {
    constructor() {
        this.apiConfig = window.SigmaXAPIConfig;
        this.cache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5分钟缓存

        // 🔥 更新：配置类型定义 - 使用后端真实字段
        this.configTypes = {
            system: {
                endpoint: '/config/system',
                name: '系统配置',
                fields: ['maintenance_mode', 'max_concurrent_strategies', 'data_retention_days', 'backup_enabled', 'log_level']
            },
            api: {
                endpoint: '/config/api',
                name: 'API配置',
                fields: ['rate_limit_per_minute', 'max_request_size', 'timeout_seconds', 'cors_enabled']
            },
            cache: {
                endpoint: '/config/cache',
                name: '缓存配置',
                fields: ['name', 'description', 'enabled', 'redis_enabled', 'default_ttl_seconds', 'max_memory_mb', 'max_entries', 'cleanup_interval_seconds', 'memory_threshold_percentage']
            },
            monitoring: {
                endpoint: '/config/monitoring',
                name: '监控配置',
                fields: ['name', 'description', 'enabled', 'metrics_enabled', 'health_check_interval', 'data_collection_interval', 'data_retention_days']
            }
        };

        console.log('🔧 SystemConfigService initialized');
    }



    /**
     * 获取指定类型的配置
     * @param {string} configType 配置类型 (system, api, cache, monitoring)
     * @param {boolean} useCache 是否使用缓存
     * @returns {Promise<Object>} 配置数据
     */
    async getConfig(configType, useCache = true) {
        try {
            // 检查缓存
            if (useCache && this.isCacheValid(configType)) {
                console.log(`📦 从缓存获取${this.configTypes[configType].name}`);
                return this.cache.get(configType).data;
            }

            const configInfo = this.configTypes[configType];
            if (!configInfo) {
                throw new Error(`未知的配置类型: ${configType}`);
            }

            console.log(`🔍 获取${configInfo.name}...`);
            const url = this.apiConfig.getUrl(configInfo.endpoint);

            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
                signal: AbortSignal.timeout(this.apiConfig.request.timeout)
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();

            if (result.code !== 200) {
                throw new Error(result.message || '获取配置失败');
            }

            // 🔥 更新：直接使用后端数据，不进行适配
            const configData = result.data;

            // 更新缓存
            this.updateCache(configType, configData);

            console.log(`✅ ${configInfo.name}获取成功`);
            console.log(`📊 后端数据:`, configData);

            return configData;

        } catch (error) {
            console.error(`❌ 获取${this.configTypes[configType]?.name || configType}失败:`, error);
            throw new Error(`获取配置失败: ${error.message}`);
        }
    }

    /**
     * 保存指定类型的配置
     * @param {string} configType 配置类型
     * @param {Object} configData 后端格式的配置数据
     * @returns {Promise<void>}
     */
    async saveConfig(configType, configData) {
        try {
            const configInfo = this.configTypes[configType];
            if (!configInfo) {
                throw new Error(`未知的配置类型: ${configType}`);
            }

            // 验证配置数据
            this.validateConfig(configType, configData);

            console.log(`💾 保存${configInfo.name}...`);
            console.log(`📊 保存数据:`, configData);

            const url = this.apiConfig.getUrl(configInfo.endpoint);

            const response = await fetch(url, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(configData), // 直接发送后端格式的数据
                signal: AbortSignal.timeout(this.apiConfig.request.timeout)
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();

            if (result.code !== 200) {
                throw new Error(result.message || '保存配置失败');
            }

            // 更新缓存
            this.updateCache(configType, configData);

            console.log(`✅ ${configInfo.name}保存成功`);

        } catch (error) {
            console.error(`❌ 保存${this.configTypes[configType]?.name || configType}失败:`, error);
            throw new Error(`保存配置失败: ${error.message}`);
        }
    }

    /**
     * 批量获取所有配置
     * @returns {Promise<Object>} 所有配置数据
     */
    async getAllConfigs() {
        try {
            console.log('🔄 批量获取所有配置...');
            
            const configPromises = Object.keys(this.configTypes).map(async (configType) => {
                try {
                    const config = await this.getConfig(configType);
                    return { type: configType, data: config, success: true };
                } catch (error) {
                    console.warn(`⚠️ 获取${configType}配置失败:`, error.message);
                    return { type: configType, error: error.message, success: false };
                }
            });

            const results = await Promise.all(configPromises);
            
            const configs = {};
            const errors = [];
            
            results.forEach(result => {
                if (result.success) {
                    configs[result.type] = result.data;
                } else {
                    errors.push(`${result.type}: ${result.error}`);
                }
            });

            if (errors.length > 0) {
                console.warn('⚠️ 部分配置获取失败:', errors);
            }

            console.log('✅ 批量配置获取完成');
            return { configs, errors };

        } catch (error) {
            console.error('❌ 批量获取配置失败:', error);
            throw new Error(`批量获取配置失败: ${error.message}`);
        }
    }

    /**
     * 批量保存所有配置
     * @param {Object} allConfigs 所有配置数据
     * @returns {Promise<Object>} 保存结果
     */
    async saveAllConfigs(allConfigs) {
        try {
            console.log('💾 批量保存所有配置...');
            
            const savePromises = Object.entries(allConfigs).map(async ([configType, configData]) => {
                try {
                    await this.saveConfig(configType, configData);
                    return { type: configType, success: true };
                } catch (error) {
                    console.warn(`⚠️ 保存${configType}配置失败:`, error.message);
                    return { type: configType, error: error.message, success: false };
                }
            });

            const results = await Promise.all(savePromises);
            
            const successes = results.filter(r => r.success).map(r => r.type);
            const errors = results.filter(r => !r.success).map(r => `${r.type}: ${r.error}`);

            console.log('✅ 批量配置保存完成');
            return { successes, errors };

        } catch (error) {
            console.error('❌ 批量保存配置失败:', error);
            throw new Error(`批量保存配置失败: ${error.message}`);
        }
    }

    /**
     * 验证配置数据
     * @param {string} configType 配置类型
     * @param {Object} configData 配置数据
     */
    validateConfig(configType, configData) {
        if (!configData || typeof configData !== 'object') {
            throw new Error('配置数据必须是对象');
        }

        // 🔥 更新：基础字段验证 - 使用后端真实字段
        switch (configType) {
            case 'system':
                if (configData.max_concurrent_strategies && (configData.max_concurrent_strategies < 1 || configData.max_concurrent_strategies > 1000)) {
                    throw new Error('最大并发策略数必须在1-1000之间');
                }
                if (configData.data_retention_days && (configData.data_retention_days < 1 || configData.data_retention_days > 3650)) {
                    throw new Error('数据保留天数必须在1-3650之间');
                }
                if (configData.log_level && !['ERROR', 'WARN', 'INFO', 'DEBUG', 'TRACE'].includes(configData.log_level)) {
                    throw new Error('日志级别必须是ERROR、WARN、INFO、DEBUG或TRACE之一');
                }
                break;

            case 'api':
                if (configData.timeout_seconds && (configData.timeout_seconds < 1 || configData.timeout_seconds > 300)) {
                    throw new Error('API超时时间必须在1-300秒之间');
                }
                if (configData.rate_limit_per_minute && (configData.rate_limit_per_minute < 1 || configData.rate_limit_per_minute > 100000)) {
                    throw new Error('每分钟请求限制必须在1-100000之间');
                }
                if (configData.max_request_size && configData.max_request_size < 1024) {
                    throw new Error('最大请求大小必须大于1024字节');
                }
                break;

            case 'cache':
                if (configData.default_ttl_seconds && configData.default_ttl_seconds < 1) {
                    throw new Error('缓存TTL必须大于0');
                }
                if (configData.max_memory_mb && configData.max_memory_mb < 1) {
                    throw new Error('最大内存必须大于0');
                }
                if (configData.max_entries && configData.max_entries < 1) {
                    throw new Error('最大条目数必须大于0');
                }
                if (configData.memory_threshold_percentage && (configData.memory_threshold_percentage < 1 || configData.memory_threshold_percentage > 100)) {
                    throw new Error('内存阈值必须在1-100之间');
                }
                break;

            case 'monitoring':
                if (configData.health_check_interval && configData.health_check_interval < 1) {
                    throw new Error('健康检查间隔必须大于0');
                }
                if (configData.data_collection_interval && configData.data_collection_interval < 1) {
                    throw new Error('数据收集间隔必须大于0');
                }
                if (configData.data_retention_days && configData.data_retention_days < 1) {
                    throw new Error('数据保留天数必须大于0');
                }
                break;
        }
    }

    /**
     * 检查缓存是否有效
     * @param {string} configType 配置类型
     * @returns {boolean} 缓存是否有效
     */
    isCacheValid(configType) {
        const cached = this.cache.get(configType);
        if (!cached) return false;

        return (Date.now() - cached.timestamp) < this.cacheTimeout;
    }

    /**
     * 更新缓存
     * @param {string} configType 配置类型
     * @param {Object} data 配置数据
     */
    updateCache(configType, data) {
        this.cache.set(configType, {
            data: data,
            timestamp: Date.now()
        });
    }

    /**
     * 清除缓存
     * @param {string} configType 配置类型，不传则清除所有
     */
    clearCache(configType = null) {
        if (configType) {
            this.cache.delete(configType);
            console.log(`🗑️ 已清除${configType}配置缓存`);
        } else {
            this.cache.clear();
            console.log('🗑️ 已清除所有配置缓存');
        }
    }

    /**
     * 获取配置类型信息
     * @returns {Object} 配置类型信息
     */
    getConfigTypes() {
        return this.configTypes;
    }
}

// 创建全局实例
window.SystemConfigService = new SystemConfigService();

console.log('✅ SystemConfigService 已加载并初始化');
