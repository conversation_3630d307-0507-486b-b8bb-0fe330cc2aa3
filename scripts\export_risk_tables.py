#!/usr/bin/env python3
"""
导出线上数据库中 risk 相关表的结构
"""

import os
import asyncio
import asyncpg
from datetime import datetime

# 数据库连接配置
DATABASE_URL = os.getenv('DATABASE_URL', '****************************************************/mx')

async def get_table_structure(conn, table_name):
    """获取表结构"""

    # 获取表的基本信息
    table_info_query = """
    SELECT
        c.column_name,
        c.data_type,
        c.character_maximum_length,
        c.numeric_precision,
        c.numeric_scale,
        c.is_nullable,
        c.column_default,
        c.udt_name
    FROM information_schema.columns c
    WHERE c.table_name = $1
    AND c.table_schema = 'public'
    ORDER BY c.ordinal_position;
    """

    columns = await conn.fetch(table_info_query, table_name)

    if not columns:
        return None

    # 获取主键信息
    pk_query = """
    SELECT kcu.column_name
    FROM information_schema.table_constraints tc
    JOIN information_schema.key_column_usage kcu
        ON tc.constraint_name = kcu.constraint_name
    WHERE tc.table_name = $1
    AND tc.constraint_type = 'PRIMARY KEY'
    AND tc.table_schema = 'public';
    """

    primary_keys = await conn.fetch(pk_query, table_name)
    pk_columns = [row['column_name'] for row in primary_keys]

    # 获取索引信息
    index_query = """
    SELECT
        i.indexname,
        i.indexdef
    FROM pg_indexes i
    WHERE i.tablename = $1
    AND i.schemaname = 'public'
    AND i.indexname != $1 || '_pkey';
    """

    indexes = await conn.fetch(index_query, table_name)

    # 获取约束信息
    constraint_query = """
    SELECT
        tc.constraint_name,
        tc.constraint_type,
        cc.check_clause
    FROM information_schema.table_constraints tc
    LEFT JOIN information_schema.check_constraints cc
        ON tc.constraint_name = cc.constraint_name
    WHERE tc.table_name = $1
    AND tc.table_schema = 'public'
    AND tc.constraint_type != 'PRIMARY KEY';
    """

    constraints = await conn.fetch(constraint_query, table_name)

    return {
        'table_name': table_name,
        'columns': columns,
        'primary_keys': pk_columns,
        'indexes': indexes,
        'constraints': constraints
    }

def format_column_type(column):
    """格式化列类型"""
    data_type = column['data_type']
    udt_name = column['udt_name']

    if data_type == 'character varying':
        if column['character_maximum_length']:
            return f"VARCHAR({column['character_maximum_length']})"
        else:
            return "VARCHAR"
    elif data_type == 'numeric':
        if column['numeric_precision'] and column['numeric_scale']:
            return f"DECIMAL({column['numeric_precision']},{column['numeric_scale']})"
        else:
            return "DECIMAL"
    elif data_type == 'timestamp with time zone':
        return "TIMESTAMPTZ"
    elif data_type == 'uuid':
        return "UUID"
    elif data_type == 'boolean':
        return "BOOLEAN"
    elif data_type == 'integer':
        return "INTEGER"
    elif data_type == 'bigint':
        return "BIGINT"
    elif data_type == 'text':
        return "TEXT"
    elif udt_name == 'jsonb':
        return "JSONB"
    else:
        return data_type.upper()

def generate_create_table_sql(table_info):
    """生成 CREATE TABLE SQL"""

    table_name = table_info['table_name']
    columns = table_info['columns']
    primary_keys = table_info['primary_keys']

    sql_lines = [
        f"-- {table_name} 表结构（从线上数据库导出）",
        f"-- 导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
        "",
        f'DROP TABLE IF EXISTS "public"."{table_name}" CASCADE;',
        "",
        f'CREATE TABLE "public"."{table_name}" ('
    ]

    # 添加列定义
    column_definitions = []
    for col in columns:
        col_name = col['column_name']
        col_type = format_column_type(col)

        # 构建列定义
        col_def = f'  "{col_name}" {col_type}'

        # 添加 NOT NULL
        if col['is_nullable'] == 'NO':
            col_def += ' NOT NULL'

        # 添加默认值
        if col['column_default']:
            default_val = col['column_default']
            # 处理特殊的默认值
            if 'gen_random_uuid()' in default_val:
                col_def += ' DEFAULT gen_random_uuid()'
            elif 'now()' in default_val.lower():
                col_def += ' DEFAULT NOW()'
            elif default_val.startswith("'"):
                col_def += f' DEFAULT {default_val}'
            else:
                col_def += f' DEFAULT {default_val}'

        column_definitions.append(col_def)

    # 添加主键约束
    if primary_keys:
        pk_columns = ', '.join([f'"{pk}"' for pk in primary_keys])
        pk_def = f'  PRIMARY KEY ({pk_columns})'
        column_definitions.append(pk_def)

    sql_lines.extend([',\n'.join(column_definitions)])
    sql_lines.append(');')

    return '\n'.join(sql_lines)

def generate_indexes_sql(table_info):
    """生成索引 SQL"""

    indexes = table_info['indexes']
    if not indexes:
        return ""

    sql_lines = [
        "",
        "-- 索引",
        ""
    ]

    for idx in indexes:
        sql_lines.append(f"{idx['indexdef']};")

    return '\n'.join(sql_lines)

def generate_constraints_sql(table_info):
    """生成约束 SQL"""

    constraints = table_info['constraints']
    if not constraints:
        return ""

    sql_lines = [
        "",
        "-- 约束",
        ""
    ]

    table_name = table_info['table_name']

    for constraint in constraints:
        constraint_name = constraint['constraint_name']
        constraint_type = constraint['constraint_type']

        if constraint_type == 'CHECK' and constraint['check_clause']:
            sql_lines.append(f'ALTER TABLE "public"."{table_name}" ADD CONSTRAINT "{constraint_name}" CHECK {constraint["check_clause"]};')
        elif constraint_type == 'UNIQUE':
            # 唯一约束通常已经通过索引创建了
            pass
        elif constraint_type == 'FOREIGN KEY':
            # 外键约束需要更复杂的查询来获取详细信息
            pass

    return '\n'.join(sql_lines) if len(sql_lines) > 3 else ""

async def export_table_structure(table_name):
    """导出单个表结构"""

    conn = await asyncpg.connect(DATABASE_URL)

    try:
        table_info = await get_table_structure(conn, table_name)

        if not table_info:
            print(f"表 {table_name} 不存在")
            return None

        # 生成完整的 SQL
        sql_content = []
        sql_content.append(generate_create_table_sql(table_info))
        sql_content.append(generate_indexes_sql(table_info))
        sql_content.append(generate_constraints_sql(table_info))
        sql_content.append("")
        sql_content.append(f'ALTER TABLE "public"."{table_name}" OWNER TO "neondb_owner";')

        return '\n'.join(sql_content)

    finally:
        await conn.close()

async def main():
    """主函数"""

    # 要导出的 risk 相关表
    risk_tables = [
        'risk_rules',
        'risk_checks',
        'risk_violations',
        'risk_statistics',
        'risk_config',
        'risk_budget_usage'
    ]

    print("🔍 开始导出线上 risk 相关表结构...")

    for table_name in risk_tables:
        print(f"📋 导出表: {table_name}")

        try:
            sql_content = await export_table_structure(table_name)

            if sql_content:
                # 保存到文件
                filename = f"database/sql/{table_name}.sql"
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(sql_content)
                print(f"✅ 已保存到: {filename}")
            else:
                print(f"⚠️  表 {table_name} 不存在，跳过")

        except Exception as e:
            print(f"❌ 导出表 {table_name} 失败: {e}")

    print("🎉 导出完成！")

if __name__ == "__main__":
    asyncio.run(main())