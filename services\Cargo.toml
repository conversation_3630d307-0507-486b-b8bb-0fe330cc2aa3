[package]
name = "services"
version = "0.1.0"
edition = "2021"

[dependencies]
# 核心依赖
core = { path = "../core" }
tokio = { version = "1.0", features = ["full"] }
async-trait = "0.1"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
uuid = { version = "1.0", features = ["v4"] }
chrono = { version = "0.4", features = ["serde"] }

# 缓存相关
redis = { version = "0.24", features = ["tokio-comp", "connection-manager"] }
moka = { version = "0.12", features = ["future"] }

# 指标相关
prometheus = "0.13"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# 配置相关
config = "0.14"
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "postgres", "uuid", "chrono"] }

# 错误处理
thiserror = "1.0"
anyhow = "1.0"

[dev-dependencies]
tokio-test = "0.4"
mockall = "0.12"
