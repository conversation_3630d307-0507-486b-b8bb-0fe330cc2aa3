# SigmaX API 验证中心

专门用于SigmaX回测API完整验证的独立空间，提供系统化、专业化的API验证服务。

## 📁 目录结构

```
api_validation/
├── config/                 # 验证配置
│   ├── environments.json   # 环境配置
│   ├── test_scenarios.json # 测试场景
│   ├── validation_rules.json # 验证规则
│   └── performance_thresholds.json # 性能阈值
├── data/                   # 测试数据
│   ├── test_datasets/      # 测试数据集
│   ├── mock_responses/     # 模拟响应
│   └── reference_data/     # 参考数据
├── tests/                  # 验证测试
│   ├── basic/             # 基础验证
│   ├── functional/        # 功能验证
│   ├── performance/       # 性能验证
│   ├── integration/       # 集成验证
│   └── e2e/              # 端到端验证
├── reports/               # 验证报告
│   ├── daily/            # 日报告
│   ├── weekly/           # 周报告
│   └── archives/         # 历史报告
├── logs/                  # 日志文件
│   ├── validation/       # 验证日志
│   ├── performance/      # 性能日志
│   └── errors/           # 错误日志
├── tools/                 # 验证工具
│   ├── validators/       # 验证器
│   ├── generators/       # 数据生成器
│   └── analyzers/        # 分析工具
└── docs/                  # 验证文档
    ├── api_specs/        # API规范
    ├── test_plans/       # 测试计划
    └── guides/           # 使用指南
```

## 🎯 验证层次

### 1. 基础验证 (Basic Validation)
- API连通性测试
- 基本CRUD操作验证
- 错误响应验证
- 认证和授权测试

### 2. 功能验证 (Functional Validation)
- 完整业务流程验证
- 数据一致性检查
- 业务规则验证
- 边界条件测试

### 3. 性能验证 (Performance Validation)
- 响应时间测试
- 并发能力验证
- 负载测试
- 资源使用监控

### 4. 集成验证 (Integration Validation)
- 数据库集成测试
- WebSocket通信验证
- 第三方服务集成
- 系统间数据流验证

### 5. 端到端验证 (E2E Validation)
- 完整用户场景
- 多系统协作验证
- 真实环境测试
- 用户体验验证

## 🚀 快速开始

### 环境准备
```bash
# 1. 进入验证目录
cd api_validation

# 2. 安装依赖
pip install -r requirements.txt

# 3. 配置环境
cp config/environments.example.json config/environments.json

# 4. 运行基础验证
python tools/validators/basic_validator.py
```

### 验证命令
```bash
# 运行所有验证
python validate.py --all

# 运行特定层次验证
python validate.py --level basic
python validate.py --level functional
python validate.py --level performance

# 运行特定场景
python validate.py --scenario backtest_flow
python validate.py --scenario concurrent_engines

# 生成验证报告
python tools/analyzers/report_generator.py

# 运行专业验证中心
python3 api_validation/quick_start.py

# 运行WebSocket测试
python3 scripts/websocket_backtest_monitor.py

# 运行完整集成测试
python3 scripts/comprehensive_backtest_integration_test.py

# 运行所有验证
cd /Users/<USER>/Program/Rust/SigmaX/api_validation && python3 validate.py --all
```

## 📊 验证覆盖

### API端点覆盖
- ✅ 回测数据管理 (4个端点)
- ✅ 引擎管理 (6个端点)
- ✅ 回测配置和结果 (6个端点)
- ✅ 策略管理 (7个端点)
- ✅ WebSocket通信 (实时监控)

### 验证维度
- ✅ 功能正确性
- ✅ 性能指标
- ✅ 稳定性
- ✅ 安全性
- ✅ 兼容性
- ✅ 可用性

## 🔧 验证工具

### 自动化验证
- 持续集成验证
- 定时验证任务
- 回归测试
- 性能基准测试

### 手动验证
- 探索性测试
- 用户体验测试
- 边界条件验证
- 异常场景测试

## 📈 报告系统

### 实时监控
- 验证状态仪表板
- 性能指标监控
- 错误率统计
- 趋势分析

### 报告类型
- 验证摘要报告
- 详细测试报告
- 性能分析报告
- 问题跟踪报告

## 🎯 验证目标

### 质量目标
- API功能正确率: 100%
- 性能达标率: ≥95%
- 稳定性指标: ≥99%
- 错误恢复率: 100%

### 覆盖目标
- API端点覆盖: 100%
- 业务场景覆盖: ≥90%
- 错误场景覆盖: ≥80%
- 性能场景覆盖: ≥85%

---

**开始您的API验证之旅！** 🚀
