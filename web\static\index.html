<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SigmaX Trading System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/custom.css">
    <style>
        /* 自定义样式 */
        .sidebar-transition {
            transition: transform 0.3s ease-in-out;
        }
        .content-transition {
            transition: margin-left 0.3s ease-in-out;
        }
        .active-menu-item {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .menu-item:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .header-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar-gradient {
            background: linear-gradient(180deg, #2d3748 0%, #1a202c 100%);
        }
    </style>
</head>
<body class="bg-gray-100 font-sans">
    <!-- 侧边栏 -->
    <div id="sidebar" class="fixed left-0 top-0 h-full w-64 sidebar-gradient text-white z-50 sidebar-transition">
        <!-- Logo区域 -->
        <div class="p-6 border-b border-gray-600">
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                    <i class="fas fa-chart-line text-white text-lg"></i>
                </div>
                <div>
                    <h1 class="text-xl font-bold">SigmaX</h1>
                    <p class="text-sm text-gray-300">Trading System</p>
                </div>
            </div>
        </div>

        <!-- 导航菜单 -->
        <nav class="mt-6">
            <div class="px-4">
                <h3 class="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3">主要功能</h3>
                <ul class="space-y-2">
                    <li>
                        <a href="#dashboard" class="menu-item flex items-center px-4 py-3 rounded-lg transition-all duration-200" data-page="dashboard">
                            <i class="fas fa-tachometer-alt w-5 h-5 mr-3"></i>
                            <span>仪表盘</span>
                        </a>
                    </li>
                    <li>
                        <a href="#backtest" class="menu-item flex items-center px-4 py-3 rounded-lg transition-all duration-200" data-page="backtest">
                            <i class="fas fa-history w-5 h-5 mr-3"></i>
                            <span>回测系统</span>
                        </a>
                    </li>
                    <li>
                        <a href="#live-trading" class="menu-item flex items-center px-4 py-3 rounded-lg transition-all duration-200" data-page="live-trading">
                            <i class="fas fa-chart-candlestick w-5 h-5 mr-3"></i>
                            <span>实盘交易</span>
                        </a>
                    </li>
                    <li>
                        <a href="#portfolio" class="menu-item flex items-center px-4 py-3 rounded-lg transition-all duration-200" data-page="portfolio">
                            <i class="fas fa-wallet w-5 h-5 mr-3"></i>
                            <span>投资组合</span>
                        </a>
                    </li>
                </ul>
            </div>

            <div class="px-4 mt-8">
                <h3 class="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3">策略管理</h3>
                <ul class="space-y-2">
                    <li>
                        <a href="#strategies" class="menu-item flex items-center px-4 py-3 rounded-lg transition-all duration-200" data-page="strategies">
                            <i class="fas fa-brain w-5 h-5 mr-3"></i>
                            <span>策略配置</span>
                        </a>
                    </li>
                    <li>
                        <a href="#strategy-validation" class="menu-item flex items-center px-4 py-3 rounded-lg transition-all duration-200" data-page="strategy-validation">
                            <i class="fas fa-check-circle w-5 h-5 mr-3"></i>
                            <span>策略验证</span>
                        </a>
                    </li>
                    <li>
                        <a href="#risk-management" class="menu-item flex items-center px-4 py-3 rounded-lg transition-all duration-200" data-page="risk-management">
                            <i class="fas fa-shield-alt w-5 h-5 mr-3"></i>
                            <span>风险管理</span>
                        </a>
                    </li>
                    <li>
                        <a href="#performance" class="menu-item flex items-center px-4 py-3 rounded-lg transition-all duration-200" data-page="performance">
                            <i class="fas fa-chart-bar w-5 h-5 mr-3"></i>
                            <span>绩效分析</span>
                        </a>
                    </li>
                </ul>
            </div>

            <div class="px-4 mt-8">
                <h3 class="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3">系统管理</h3>
                <ul class="space-y-2">
                    <li>
                        <a href="#market-data" class="menu-item flex items-center px-4 py-3 rounded-lg transition-all duration-200" data-page="market-data">
                            <i class="fas fa-database w-5 h-5 mr-3"></i>
                            <span>市场数据</span>
                        </a>
                    </li>
                    <li>
                        <a href="#system-config" class="menu-item flex items-center px-4 py-3 rounded-lg transition-all duration-200" data-page="system-config">
                            <i class="fas fa-cog w-5 h-5 mr-3"></i>
                            <span>系统配置</span>
                        </a>
                    </li>
                    <li>
                        <a href="#logs" class="menu-item flex items-center px-4 py-3 rounded-lg transition-all duration-200" data-page="logs">
                            <i class="fas fa-file-alt w-5 h-5 mr-3"></i>
                            <span>系统日志</span>
                        </a>
                    </li>
                </ul>
            </div>
        </nav>
    </div>

    <!-- 主内容区域 -->
    <div id="main-content" class="ml-64 content-transition">
        <!-- 头部区域 -->
        <header class="header-gradient text-white shadow-lg">
            <div class="flex items-center justify-between px-6 py-4">
                <div class="flex items-center space-x-4">
                    <button id="sidebar-toggle" class="lg:hidden text-white hover:text-gray-200">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                    <div>
                        <h2 id="page-title" class="text-2xl font-bold">仪表盘</h2>
                        <p id="page-subtitle" class="text-sm text-gray-200">系统概览和实时状态</p>
                    </div>
                </div>

                <div class="flex items-center space-x-4">
                    <!-- 系统状态指示器 -->
                    <div class="flex items-center space-x-2">
                        <div id="system-status" class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                        <span class="text-sm">系统正常</span>
                    </div>

                    <!-- 通知按钮 -->
                    <button class="relative p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors">
                        <i class="fas fa-bell text-xl"></i>
                        <span class="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-xs rounded-full flex items-center justify-center">3</span>
                    </button>

                    <!-- 用户菜单 -->
                    <div class="relative">
                        <button class="flex items-center space-x-2 p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors">
                            <div class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-sm"></i>
                            </div>
                            <span class="text-sm">管理员</span>
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main id="page-content" class="p-6">
            <!-- 页面内容将通过JavaScript动态加载 -->
            <div id="loading" class="flex items-center justify-center h-64">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                <span class="ml-4 text-gray-600">正在加载...</span>
            </div>
        </main>
    </div>

    <!-- JavaScript - 重构后的模块化架构 -->
    <!-- 统一API配置必须最先加载 -->
    <script src="js/api-config.js"></script>
    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/router.js"></script>
    <script src="js/components.js"></script>

    <!-- 重构后的主应用模块 - MVC架构 -->
    <script src="js/app-service.js"></script>
    <script src="js/app-view.js"></script>
    <script src="js/app-controller.js"></script>
    <script src="js/app.js"></script>

    <!-- 重构后的回测模块 - MVC架构 -->
    <script src="js/backtest-config-settings.js"></script>
    <script src="js/strategy-template-service.js"></script>
    <script src="js/strategy-selector.js"></script>
    <script src="js/backtest-service.js"></script>
    <script src="js/backtest-view.js"></script>
    <script src="js/backtest-controller.js"></script>
    <script src="js/backtest-manager.js"></script>

    <!-- 策略验证模块 - MVC架构 -->
    <script src="js/strategy-validation-service.js"></script>
    <script src="js/strategy-validation-view.js"></script>
    <script src="js/strategy-validation-controller.js"></script>
</body>
</html>
