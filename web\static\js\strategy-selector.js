/**
 * 策略选择器组件
 * 提供简洁的策略模板选择界面
 */
class StrategySelector {
    constructor(config, container) {
        this.config = config;
        this.container = container;
        this.selectedStrategy = null;
        this.templates = [];
        this.init();
    }

    /**
     * 初始化组件
     */
    async init() {
        try {
            await this.loadTemplates();
            this.render();
            this.bindEvents();
            this.config.log('info', '策略选择器初始化完成');
        } catch (error) {
            this.config.log('error', '策略选择器初始化失败:', error.message);
        }
    }

    /**
     * 加载策略模板
     */
    async loadTemplates() {
        try {
            // 使用策略验证服务获取支持的策略类型
            if (!this.strategyValidationService) {
                this.strategyValidationService = new StrategyValidationService();
            }

            const strategies = await this.strategyValidationService.getSupportedStrategyTypes(true);

            // 转换为模板格式
            this.templates = strategies.map(strategy => ({
                id: strategy.id,
                name: strategy.name,
                description: strategy.description,
                strategy_type: strategy.id,
                category: strategy.category,
                difficulty_level: this.getDifficultyFromRisk(strategy.riskLevel),
                risk_level: this.mapRiskLevel(strategy.riskLevel),
                recommended_capital: this.getRecommendedCapital(strategy.category),
                available: strategy.available,
                default_config: strategy.defaultConfig,
                required_parameters: strategy.requiredParameters,
                optional_parameters: strategy.optionalParameters,
                icon: this.getStrategyIcon(strategy.category),
                color: this.getStrategyColor(strategy.category),
                tags: this.getStrategyTags(strategy.category, strategy.riskLevel)
            }));

            this.config.log('info', `加载了 ${this.templates.length} 个策略模板`);
        } catch (error) {
            this.config.log('error', '加载策略模板失败:', error.message);
            // 使用本地配置作为后备
            this.templates = this.getLocalTemplates();
        }
    }

    /**
     * 获取本地策略模板配置
     */
    getLocalTemplates() {
        const templates = this.config.get('strategyTemplates');
        if (templates) {
            return Object.values(templates).map(template => ({
                ...template,
                difficulty_level: template.difficulty,
                risk_level: template.riskLevel,
                recommended_capital: template.recommendedCapital
            }));
        }

        // 如果配置中没有策略模板，返回默认模板
        return this.getDefaultTemplates();
    }

    /**
     * 获取默认策略模板
     */
    getDefaultTemplates() {
        return [
            {
                id: 'asymmetric_volatility_grid_strategy',
                name: '非对称波动率网格策略',
                description: '基于波动率的非对称网格策略，下跌时密集吸筹，上涨时稀疏止盈',
                strategy_type: 'asymmetric_volatility_grid_strategy',
                category: 'grid',
                difficulty_level: '高级',
                risk_level: '中高风险',
                recommended_capital: 20000,
                available: true,
                icon: 'fas fa-chart-line',
                color: 'blue',
                tags: ['非对称网格', '波动率策略', '高级策略'],
                default_config: {
                    trading_pair: 'BTCUSDT',
                    base_price: 0.0,
                    order_amount: 200.0,
                    down_range_start: -0.02,
                    down_range_end: -0.05,
                    down_grid_count: 10,
                    down_base_quantity: 0.1,
                    up_range_start: 0.02,
                    up_range_end: 0.08,
                    up_grid_count: 6,
                    up_base_quantity: 0.1,
                    volatility_window_hours: 24,
                    volatility_multiplier: 1.2,
                    enable_dynamic_volatility: true,
                    max_position_amount: 2000.0,
                    max_daily_trades: 50,
                    stop_loss_percent: 0.2,
                    strategy_preset: 'Balanced'
                }
            },
            {
                id: 'dca_strategy',
                name: 'DCA定投策略',
                description: '定期定额投资策略，分散投资风险',
                strategy_type: 'dca_strategy',
                category: 'dca',
                difficulty_level: '新手友好',
                risk_level: '低风险',
                recommended_capital: 15000,
                available: true,
                icon: 'fas fa-chart-line',
                color: 'green',
                tags: ['长期投资', '新手友好', '固定参数'],
                default_config: {
                    trading_pair: 'BTCUSDT',
                    investment_amount: 100,
                    investment_interval: '1d',
                    max_investment_count: 30
                }
            }
        ];
    }

    /**
     * 根据风险等级推断难度
     */
    getDifficultyFromRisk(riskLevel) {
        const mapping = {
            'low': '新手友好',
            'medium': '中级',
            'high': '高级'
        };
        return mapping[riskLevel] || '中级';
    }

    /**
     * 映射风险等级
     */
    mapRiskLevel(riskLevel) {
        const mapping = {
            'low': '低风险',
            'medium': '中等风险',
            'high': '高风险'
        };
        return mapping[riskLevel] || '中等风险';
    }

    /**
     * 根据分类获取推荐资金
     */
    getRecommendedCapital(category) {
        const mapping = {
            'dca': 15000,
            'grid': 10000,
            'momentum': 8000,
            'mean_reversion': 6000
        };
        return mapping[category] || 10000;
    }

    /**
     * 根据分类获取图标
     */
    getStrategyIcon(category) {
        const mapping = {
            'dca': 'fas fa-chart-line',
            'grid': 'fas fa-th',
            'momentum': 'fas fa-rocket',
            'mean_reversion': 'fas fa-balance-scale'
        };
        return mapping[category] || 'fas fa-chart-bar';
    }

    /**
     * 根据分类获取颜色
     */
    getStrategyColor(category) {
        const mapping = {
            'dca': 'green',
            'grid': 'blue',
            'momentum': 'orange',
            'mean_reversion': 'purple'
        };
        return mapping[category] || 'blue';
    }

    /**
     * 根据分类和风险等级获取标签
     */
    getStrategyTags(category, riskLevel) {
        const baseTags = {
            'dca': ['长期投资', '新手友好'],
            'grid': ['震荡行情', '稳健'],
            'momentum': ['趋势跟随', '中等风险'],
            'mean_reversion': ['均值回归', '震荡行情']
        };

        const riskTags = {
            'low': ['低风险'],
            'medium': ['中等风险'],
            'high': ['高风险']
        };

        return [...(baseTags[category] || []), ...(riskTags[riskLevel] || []), '固定参数'];
    }

    /**
     * 渲染策略选择界面
     */
    render() {
        const html = `
            <div class="strategy-selector">
                <div class="mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">选择交易策略</h3>
                    <p class="text-sm text-gray-600">所有策略参数已经过专业优化，无需手动调整</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    ${this.templates.map(template => this.renderStrategyCard(template)).join('')}
                </div>

                <div class="mt-6 p-4 bg-blue-50 rounded-lg">
                    <div class="flex items-start">
                        <i class="fas fa-info-circle text-blue-500 mt-1 mr-3"></i>
                        <div>
                            <h4 class="text-sm font-medium text-blue-900">关于固定参数</h4>
                            <p class="text-sm text-blue-700 mt-1">
                                为了确保策略的稳定性和有效性，所有参数都经过专业团队优化。
                                后续版本将根据用户反馈考虑开放自定义参数功能。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.container.innerHTML = html;
    }

    /**
     * 渲染策略卡片
     */
    renderStrategyCard(template) {
        const colorClasses = {
            blue: 'border-blue-200 hover:border-blue-300 hover:bg-blue-50',
            green: 'border-green-200 hover:border-green-300 hover:bg-green-50',
            orange: 'border-orange-200 hover:border-orange-300 hover:bg-orange-50',
            purple: 'border-purple-200 hover:border-purple-300 hover:bg-purple-50'
        };

        const iconColorClasses = {
            blue: 'text-blue-500',
            green: 'text-green-500',
            orange: 'text-orange-500',
            purple: 'text-purple-500'
        };

        const riskColorClasses = {
            '低风险': 'bg-green-100 text-green-800',
            '中等风险': 'bg-yellow-100 text-yellow-800',
            '中高风险': 'bg-orange-100 text-orange-800',
            '高风险': 'bg-red-100 text-red-800'
        };

        const isSelected = this.selectedStrategy === template.id;
        const selectedClass = isSelected ? 'ring-2 ring-blue-500 bg-blue-50' : '';

        return `
            <div class="strategy-card border-2 rounded-lg p-4 cursor-pointer transition-all duration-200 ${colorClasses[template.color] || 'border-gray-200 hover:border-gray-300'} ${selectedClass}"
                 data-strategy-id="${template.id}">

                <div class="flex items-start justify-between mb-3">
                    <div class="flex items-center">
                        <i class="${template.icon || 'fas fa-chart-bar'} text-xl ${iconColorClasses[template.color] || 'text-gray-500'} mr-3"></i>
                        <div>
                            <h4 class="font-semibold text-gray-900">${template.name}</h4>
                            <span class="text-xs text-gray-500">${template.difficulty_level || template.difficulty}</span>
                        </div>
                    </div>
                    ${isSelected ? '<i class="fas fa-check-circle text-blue-500 text-xl"></i>' : ''}
                </div>

                <p class="text-sm text-gray-600 mb-3 leading-relaxed">${template.description}</p>

                <div class="flex items-center justify-between mb-3">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${riskColorClasses[template.risk_level] || riskColorClasses[template.riskLevel] || 'bg-gray-100 text-gray-800'}">
                        ${template.risk_level || template.riskLevel}
                    </span>
                    <span class="text-xs text-gray-500">
                        建议资金: ${(template.recommended_capital || template.recommendedCapital || 0).toLocaleString()} USDT
                    </span>
                </div>

                <div class="flex flex-wrap gap-1">
                    ${(template.tags || []).map(tag => `
                        <span class="inline-flex items-center px-2 py-1 rounded text-xs bg-gray-100 text-gray-600">
                            ${tag}
                        </span>
                    `).join('')}
                </div>
            </div>
        `;
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 策略卡片点击事件
        this.container.addEventListener('click', (e) => {
            const card = e.target.closest('.strategy-card');
            if (card) {
                const strategyId = card.dataset.strategyId;
                this.selectStrategy(strategyId);
            }
        });
    }

    /**
     * 选择策略
     */
    selectStrategy(strategyId) {
        // 移除之前的选中状态
        this.container.querySelectorAll('.strategy-card').forEach(card => {
            card.classList.remove('ring-2', 'ring-blue-500', 'bg-blue-50');
            const checkIcon = card.querySelector('.fa-check-circle');
            if (checkIcon) {
                checkIcon.remove();
            }
        });

        // 添加新的选中状态
        const selectedCard = this.container.querySelector(`[data-strategy-id="${strategyId}"]`);
        if (selectedCard) {
            selectedCard.classList.add('ring-2', 'ring-blue-500', 'bg-blue-50');

            // 添加选中图标
            const header = selectedCard.querySelector('.flex.items-start.justify-between');
            if (header && !header.querySelector('.fa-check-circle')) {
                header.insertAdjacentHTML('beforeend', '<i class="fas fa-check-circle text-blue-500 text-xl"></i>');
            }
        }

        this.selectedStrategy = strategyId;

        // 触发选择事件
        this.onStrategySelected(strategyId);

        this.config.log('info', '选择策略:', strategyId);
    }

    /**
     * 策略选择回调
     */
    onStrategySelected(strategyId) {
        const template = this.templates.find(t => t.id === strategyId);
        if (template) {
            // 触发自定义事件
            const event = new CustomEvent('strategySelected', {
                detail: {
                    strategyId,
                    template,
                    config: template.default_config
                }
            });
            this.container.dispatchEvent(event);
        }
    }

    /**
     * 获取选中的策略
     */
    getSelectedStrategy() {
        if (!this.selectedStrategy) {
            return null;
        }

        const template = this.templates.find(t => t.id === this.selectedStrategy);
        return {
            strategyId: this.selectedStrategy,
            template,
            config: template.default_config
        };
    }

    /**
     * 重置选择
     */
    reset() {
        this.selectedStrategy = null;
        this.container.querySelectorAll('.strategy-card').forEach(card => {
            card.classList.remove('ring-2', 'ring-blue-500', 'bg-blue-50');
            const checkIcon = card.querySelector('.fa-check-circle');
            if (checkIcon) {
                checkIcon.remove();
            }
        });
        this.config.log('info', '策略选择已重置');
    }

    /**
     * 获取推荐策略（基于资金规模）
     */
    getRecommendedStrategy(capital) {
        const suitableTemplates = this.templates.filter(template => {
            const recommendedCapital = template.recommended_capital || template.recommendedCapital || 0;
            return capital >= recommendedCapital * 0.5; // 允许50%的弹性
        });

        // 优先推荐新手友好的策略
        const beginnerFriendly = suitableTemplates.filter(t =>
            (t.difficulty_level === 'beginner' || t.difficulty === '新手友好') &&
            (t.risk_level === 'conservative' || t.risk_level === 'moderate' || t.riskLevel === '低风险' || t.riskLevel === '中等风险')
        );

        return beginnerFriendly.length > 0 ? beginnerFriendly[0] : suitableTemplates[0];
    }

    /**
     * 显示策略推荐
     */
    showRecommendation(capital) {
        const recommended = this.getRecommendedStrategy(capital);
        if (recommended) {
            // 高亮推荐的策略
            const card = this.container.querySelector(`[data-strategy-id="${recommended.id}"]`);
            if (card) {
                card.classList.add('ring-2', 'ring-green-400');

                // 添加推荐标签
                const badge = document.createElement('div');
                badge.className = 'absolute -top-2 -right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full';
                badge.textContent = '推荐';
                card.style.position = 'relative';
                card.appendChild(badge);
            }
        }
    }
}

// 导出组件
window.StrategySelector = StrategySelector;

// 兼容CommonJS
if (typeof module !== 'undefined' && module.exports) {
    module.exports = StrategySelector;
}
