//! Coinbase交易所实现

use async_trait::async_trait;
use sigmax_core::{
    Balance, Candle, Exchange, ExchangeId, Order, OrderBook, OrderId, SigmaXResult, TimeFrame,
    TradingPair, Trade,
};

/// Coinbase交易所
pub struct CoinbaseExchange {
    // API密钥、客户端等
}

impl CoinbaseExchange {
    pub fn new() -> Self {
        Self {}
    }
}

#[async_trait]
impl Exchange for CoinbaseExchange {
    fn id(&self) -> ExchangeId {
        ExchangeId::Coinbase
    }

    async fn get_balances(&self) -> SigmaXResult<Vec<Balance>> {
        todo!("实现Coinbase余额查询")
    }

    async fn place_order(&self, _order: &Order) -> SigmaXResult<OrderId> {
        todo!("实现Coinbase下单")
    }

    async fn cancel_order(&self, _order_id: OrderId) -> SigmaXResult<()> {
        todo!("实现Coinbase取消订单")
    }

    async fn get_order(&self, _order_id: OrderId) -> SigmaXResult<Order> {
        todo!("实现Coinbase订单查询")
    }

    async fn get_order_book(&self, _trading_pair: &TradingPair) -> SigmaXResult<OrderBook> {
        todo!("实现Coinbase订单簿查询")
    }

    async fn get_candles(
        &self,
        _trading_pair: &TradingPair,
        _timeframe: TimeFrame,
        _limit: Option<u32>,
    ) -> SigmaXResult<Vec<Candle>> {
        todo!("实现Coinbase K线查询")
    }

    async fn get_trades(&self, _trading_pair: &TradingPair) -> SigmaXResult<Vec<Trade>> {
        todo!("实现Coinbase交易历史查询")
    }
}
