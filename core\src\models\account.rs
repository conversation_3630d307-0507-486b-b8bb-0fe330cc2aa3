
//! 账户余额与持仓模型

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use std::str::FromStr;
use validator::{Validate, ValidationError};

use crate::{
    Amount, ExchangeId, Price, Quantity, SigmaXResult, TradingPair,
    traits::{DatabaseConnection, DatabaseOperations},
};

/// 账户余额
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct Balance {
    pub exchange_id: ExchangeId,
    #[validate(length(min = 1, message = "Asset name cannot be empty"))]
    pub asset: String,
    #[validate(custom = "validate_non_negative_amount")]
    pub free: Amount,
    #[validate(custom = "validate_non_negative_amount")]
    pub locked: Amount,
    pub updated_at: DateTime<Utc>,
}

impl Balance {
    /// 执行完整的余额验证
    pub fn validate_complete(&self) -> SigmaXResult<()> {
        // 首先执行基础验证（通过validator derive宏）
        if let Err(validation_errors) = <Self as Validate>::validate(self) {
            return Err(crate::SigmaXError::from(validation_errors));
        }

        // 然后执行跨字段验证
        // 验证交易所ID
        if self.exchange_id.to_string().is_empty() {
            return Err(crate::SigmaXError::ValidationError(
                "Exchange ID cannot be empty".to_string()
            ));
        }

        Ok(())
    }

    /// 创建一个新的余额实例.
    pub fn new(exchange_id: ExchangeId, asset: String, free: Amount, locked: Amount) -> Self {
        Self {
            exchange_id,
            asset,
            free,
            locked,
            updated_at: Utc::now(),
        }
    }

    /// 计算总余额（可用 + 冻结）.
    pub fn total(&self) -> Amount {
        self.free + self.locked
    }

    /// 更新余额.
    pub fn update(&mut self, free: Amount, locked: Amount) {
        self.free = free;
        self.locked = locked;
        self.updated_at = Utc::now();
    }

    /// 锁定资金（例如，因下达限价单）.
    pub fn lock_funds(&mut self, amount: Amount) -> SigmaXResult<()> {
        if self.free < amount {
            return Err(crate::SigmaXError::InvalidParameter("Insufficient free balance".to_string()));
        }
        self.free -= amount;
        self.locked += amount;
        self.updated_at = Utc::now();
        Ok(())
    }

    /// 释放资金（例如，因订单取消）.
    pub fn unlock_funds(&mut self, amount: Amount) -> SigmaXResult<()> {
        if self.locked < amount {
            return Err(crate::SigmaXError::InvalidParameter("Insufficient locked balance".to_string()));
        }
        self.locked -= amount;
        self.free += amount;
        self.updated_at = Utc::now();
        Ok(())
    }

    /// 检查余额数据是否一致（非负）.
    pub fn is_consistent(&self) -> bool {
        self.free >= Amount::ZERO && self.locked >= Amount::ZERO
    }

    /// 根据交易所和资产加载余额.
    ///
    /// # 移植说明
    /// 从 models_old.rs 移植了更健壮的错误处理和数据解析逻辑
    pub async fn load_by_exchange_and_asset(
        exchange_id: ExchangeId,
        asset: &str,
        db: &dyn DatabaseConnection,
    ) -> SigmaXResult<Option<Balance>> {
        let query = r#"
            SELECT exchange_id, asset, free, locked, updated_at
            FROM balances WHERE exchange_id = $1 AND asset = $2
        "#;

        let exchange_id_str = exchange_id.to_string();
        let params = vec![exchange_id_str, asset.to_string()];

        if let Some(row) = db.query_one(query, &params).await? {
            // 🔥 移植说明：使用 models_old.rs 中更健壮的解析逻辑
            let balance = Balance {
                exchange_id: ExchangeId::from(row.get("exchange_id").unwrap().as_str().unwrap()),
                asset: row.get("asset").unwrap().as_str().unwrap().to_string(),
                free: rust_decimal::Decimal::from_str(row.get("free").unwrap().as_str().unwrap())
                    .map_err(|e| crate::SigmaXError::InvalidParameter(format!("Invalid free amount: {}", e)))?,
                locked: rust_decimal::Decimal::from_str(row.get("locked").unwrap().as_str().unwrap())
                    .map_err(|e| crate::SigmaXError::InvalidParameter(format!("Invalid locked amount: {}", e)))?,
                updated_at: chrono::DateTime::parse_from_rfc3339(row.get("updated_at").unwrap().as_str().unwrap())
                    .map_err(|e| crate::SigmaXError::InvalidParameter(format!("Invalid updated_at: {}", e)))?
                    .with_timezone(&chrono::Utc),
            };
            Ok(Some(balance))
        } else {
            Ok(None)
        }
    }

    /// 根据交易所和资产删除余额.
    pub async fn delete_by_exchange_and_asset(
        exchange_id: ExchangeId,
        asset: &str,
        db: &dyn DatabaseConnection,
    ) -> SigmaXResult<()> {
        let query = "DELETE FROM balances WHERE exchange_id = $1 AND asset = $2";
        let exchange_id_str = exchange_id.to_string();
        let params = vec![exchange_id_str, asset.to_string()];
        db.execute_query(query, &params).await?;
        Ok(())
    }
}

impl Default for Balance {
    fn default() -> Self {
        Self {
            exchange_id: "binance".to_string().into(),
            asset: "USDT".to_string(),
            free: rust_decimal::Decimal::new(1000, 0), // 1000 USDT
            locked: rust_decimal::Decimal::ZERO,
            updated_at: Utc::now(),
        }
    }
}

#[async_trait::async_trait]
impl DatabaseOperations<Balance> for Balance {
    /// 将余额信息保存或更新到数据库.
    async fn save(&self, db: &dyn DatabaseConnection) -> SigmaXResult<()> {
        let query = r#"
            INSERT INTO balances (exchange_id, asset, free, locked, updated_at)
            VALUES ($1, $2, $3, $4, $5)
            ON CONFLICT (exchange_id, asset) DO UPDATE SET
                free = EXCLUDED.free,
                locked = EXCLUDED.locked,
                updated_at = EXCLUDED.updated_at
        "#;

        let exchange_id_str = self.exchange_id.to_string();
        let free_str = self.free.to_string();
        let locked_str = self.locked.to_string();
        let updated_at_str = self.updated_at.to_rfc3339();

        let params = vec![
            exchange_id_str,
            self.asset.clone(),
            free_str,
            locked_str,
            updated_at_str,
        ];

        db.execute_query(query, &params).await?;
        Ok(())
    }

    /// Balance使用复合主键(exchange_id, asset)，不支持通过单个UUID加载.
    async fn load(_id: Uuid, _db: &dyn DatabaseConnection) -> SigmaXResult<Option<Balance>> {
        Err(crate::SigmaXError::InvalidOperation("Use load_by_exchange_and_asset for Balance".to_string()))
    }

    /// 更新数据库中的余额记录.
    async fn update(&self, db: &dyn DatabaseConnection) -> SigmaXResult<()> {
        self.save(db).await // `save` 方法已包含 "upsert" 逻辑
    }

    /// Balance使用复合主键(exchange_id, asset)，不支持通过单个UUID删除.
    async fn delete(_id: Uuid, _db: &dyn DatabaseConnection) -> SigmaXResult<()> {
        Err(crate::SigmaXError::InvalidOperation("Use delete_by_exchange_and_asset for Balance".to_string()))
    }
}



// ============================================================================
// 自定义验证函数 (用于validator derive宏)
// ============================================================================

/// 验证金额非负数
fn validate_non_negative_amount(amount: &Amount) -> Result<(), ValidationError> {
    if *amount < Amount::ZERO {
        return Err(ValidationError::new("Amount cannot be negative"));
    }
    Ok(())
}

/// 持仓信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Position {
    pub trading_pair: TradingPair,
    pub quantity: Quantity,
    pub average_price: Price,
    pub unrealized_pnl: Amount,
    pub realized_pnl: Amount,
}

impl Default for Position {
    fn default() -> Self {
        Self {
            trading_pair: crate::TradingPair::new("BTC".to_string(), "USDT".to_string()),
            quantity: rust_decimal::Decimal::ZERO,
            average_price: rust_decimal::Decimal::ZERO,
            unrealized_pnl: rust_decimal::Decimal::ZERO,
            realized_pnl: rust_decimal::Decimal::ZERO,
        }
    }
}