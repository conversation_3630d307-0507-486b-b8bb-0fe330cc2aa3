//! 实时进度处理器
//!
//! 负责接收回测引擎的进度事件并通过WebSocket推送给前端

use std::sync::Arc;
use tokio::sync::{mpsc, RwLock};
use tracing::{info, warn, error};
use serde_json;
use crate::{
    websocket::WebSocketServer,
    error::ApiError,
};

/// 回测进度事件（从engines模块导入的简化版本）
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct BacktestProgressEvent {
    pub backtest_id: String,
    pub progress_percentage: f64,
    pub current_date: chrono::DateTime<chrono::Utc>,
    pub processed_candles: u64,
    pub total_candles: u64,
    pub estimated_completion: Option<chrono::DateTime<chrono::Utc>>,
    pub current_balance: rust_decimal::Decimal,
    pub total_trades: u64,
    pub unrealized_pnl: rust_decimal::Decimal,
    pub max_drawdown: f64,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

/// 实时进度处理器
pub struct RealtimeProgressHandler {
    /// WebSocket服务器
    websocket_server: Arc<WebSocketServer>,
    /// 进度事件接收器
    progress_receiver: Arc<RwLock<Option<mpsc::UnboundedReceiver<BacktestProgressEvent>>>>,
    /// 是否正在运行
    is_running: Arc<RwLock<bool>>,
    /// 后台任务句柄
    task_handle: Arc<RwLock<Option<tokio::task::JoinHandle<()>>>>,
}

impl RealtimeProgressHandler {
    /// 创建新的实时进度处理器
    pub fn new(websocket_server: Arc<WebSocketServer>) -> Self {
        Self {
            websocket_server,
            progress_receiver: Arc::new(RwLock::new(None)),
            is_running: Arc::new(RwLock::new(false)),
            task_handle: Arc::new(RwLock::new(None)),
        }
    }

    /// 设置进度事件接收器
    pub async fn set_progress_receiver(&self, receiver: mpsc::UnboundedReceiver<BacktestProgressEvent>) {
        let mut progress_receiver = self.progress_receiver.write().await;
        *progress_receiver = Some(receiver);
    }

    /// 启动实时进度处理
    pub async fn start(&self) -> Result<(), ApiError> {
        let mut is_running = self.is_running.write().await;
        if *is_running {
            return Err(ApiError::InvalidOperation("实时进度处理器已经在运行".to_string()));
        }

        *is_running = true;
        drop(is_running);

        // 启动后台任务
        let websocket_server = self.websocket_server.clone();
        let progress_receiver = self.progress_receiver.clone();
        let is_running_flag = self.is_running.clone();

        let handle = tokio::spawn(async move {
            Self::progress_handling_loop(websocket_server, progress_receiver, is_running_flag).await;
        });

        let mut task_handle = self.task_handle.write().await;
        *task_handle = Some(handle);

        info!("实时进度处理器已启动");
        Ok(())
    }

    /// 停止实时进度处理
    pub async fn stop(&self) -> Result<(), ApiError> {
        let mut is_running = self.is_running.write().await;
        if !*is_running {
            return Ok(()); // 已经停止
        }

        *is_running = false;
        drop(is_running);

        // 等待后台任务完成
        let mut task_handle = self.task_handle.write().await;
        if let Some(handle) = task_handle.take() {
            if let Err(e) = handle.await {
                warn!("等待实时进度处理器任务完成时出错: {}", e);
            }
        }

        info!("实时进度处理器已停止");
        Ok(())
    }

    /// 进度处理循环
    async fn progress_handling_loop(
        websocket_server: Arc<WebSocketServer>,
        progress_receiver: Arc<RwLock<Option<mpsc::UnboundedReceiver<BacktestProgressEvent>>>>,
        is_running: Arc<RwLock<bool>>,
    ) {
        info!("开始实时进度处理循环");

        loop {
            // 检查是否应该继续运行
            {
                let running = is_running.read().await;
                if !*running {
                    break;
                }
            }

            // 尝试接收进度事件
            let mut receiver_guard = progress_receiver.write().await;
            if let Some(receiver) = receiver_guard.as_mut() {
                match receiver.try_recv() {
                    Ok(progress_event) => {
                        drop(receiver_guard); // 释放锁
                        
                        // 处理进度事件
                        if let Err(e) = Self::handle_progress_event(&websocket_server, progress_event).await {
                            error!("处理进度事件失败: {}", e);
                        }
                    }
                    Err(mpsc::error::TryRecvError::Empty) => {
                        drop(receiver_guard); // 释放锁
                        // 没有新事件，短暂休眠
                        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
                    }
                    Err(mpsc::error::TryRecvError::Disconnected) => {
                        drop(receiver_guard); // 释放锁
                        warn!("进度事件发送器已断开连接");
                        break;
                    }
                }
            } else {
                drop(receiver_guard); // 释放锁
                // 没有接收器，等待设置
                tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;
            }
        }

        info!("实时进度处理循环已结束");
    }

    /// 处理单个进度事件
    async fn handle_progress_event(
        websocket_server: &Arc<WebSocketServer>,
        progress_event: BacktestProgressEvent,
    ) -> Result<(), ApiError> {
        // 转换为WebSocket消息格式
        let websocket_data = serde_json::json!({
            "backtest_id": progress_event.backtest_id,
            "progress": {
                "percentage": progress_event.progress_percentage,
                "current_candle": progress_event.processed_candles,
                "total_candles": progress_event.total_candles,
                "current_date": progress_event.current_date.to_rfc3339(),
                "estimated_completion": progress_event.estimated_completion.map(|t| t.to_rfc3339()),
            },
            "stats": {
                "current_balance": progress_event.current_balance.to_string(),
                "total_trades": progress_event.total_trades,
                "unrealized_pnl": progress_event.unrealized_pnl.to_string(),
                "max_drawdown": progress_event.max_drawdown,
            },
            "timestamp": progress_event.timestamp.to_rfc3339(),
        });

        // 发送到backtest_progress频道
        websocket_server.broadcast_message_to_channel(
            "backtest_progress",
            "progress_update",
            websocket_data,
        ).await.map_err(|e| ApiError::Internal(format!("发送WebSocket消息失败: {}", e)))?;

        info!("已推送回测进度: {}% ({}/{})", 
              progress_event.progress_percentage,
              progress_event.processed_candles,
              progress_event.total_candles);

        Ok(())
    }

    /// 创建进度事件发送器和接收器对
    pub fn create_progress_channel() -> (mpsc::UnboundedSender<BacktestProgressEvent>, mpsc::UnboundedReceiver<BacktestProgressEvent>) {
        mpsc::unbounded_channel()
    }

    /// 发送测试进度事件（用于测试）
    pub async fn send_test_progress_event(&self) -> Result<(), ApiError> {
        let test_event = BacktestProgressEvent {
            backtest_id: "test-backtest-123".to_string(),
            progress_percentage: 45.5,
            current_date: chrono::Utc::now(),
            processed_candles: 455,
            total_candles: 1000,
            estimated_completion: Some(chrono::Utc::now() + chrono::Duration::minutes(10)),
            current_balance: rust_decimal::Decimal::new(12500, 2), // 125.00
            total_trades: 23,
            unrealized_pnl: rust_decimal::Decimal::new(250, 2), // 2.50
            max_drawdown: 5.2,
            timestamp: chrono::Utc::now(),
        };

        Self::handle_progress_event(&self.websocket_server, test_event).await
    }

    /// 获取运行状态
    pub async fn is_running(&self) -> bool {
        let is_running = self.is_running.read().await;
        *is_running
    }
}

impl Drop for RealtimeProgressHandler {
    fn drop(&mut self) {
        // 确保在销毁时停止处理器
        // 注意：这里不能使用async，所以只是设置停止标志
        if let Ok(mut is_running) = self.is_running.try_write() {
            *is_running = false;
        }
    }
}
