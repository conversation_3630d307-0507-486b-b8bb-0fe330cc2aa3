# SigmaX 交易系统 🚀

> **高性能Rust交易系统** - 支持网格交易、多交易所、实时数据处理

[![Rust](https://img.shields.io/badge/rust-1.70+-orange.svg)](https://www.rust-lang.org)
[![PostgreSQL](https://img.shields.io/badge/postgresql-13+-blue.svg)](https://www.postgresql.org)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Build Status](https://img.shields.io/badge/build-passing-brightgreen.svg)](#)

**当前版本**: v0.1.0-dev
**开发状态**: ✅ **核心功能完成** (5/5 阶段完成)
**最后更新**: 2024-12-19

## 🎯 项目概述

SigmaX是一个用Rust构建的高性能量化交易系统，专注于网格交易策略和多交易所支持。系统采用模块化架构，支持回测、实盘交易和策略管理。

## 开发规范与流程

### 核心设计原则

1. **高内聚，低耦合 (High Cohesion, Low Coupling)**
   - 模块内部功能紧密相关，模块间依赖最小化
   - 每个模块专注于单一职责

2. **关注点分离 (Separation of Concerns, SoC)**
   - 配置管理、业务逻辑、数据访问分离

3. **面向接口设计 (Design to an Interface)**
   - 定义清晰的接口契约
   - 依赖抽象而非具体实现

4. **可测试性设计 (Design for Testability)**
   - 支持单元测试和集成测试
   - 依赖注入，便于 Mock 测试

5. **简洁与可演化性 (Simplicity and Evolvability)**
   - 保持代码简洁易懂
   - 支持功能扩展和维护

## ✨ 核心特性

### 🎯 交易策略
- **网格交易** - 完整的网格交易策略实现
- **策略管理** - 动态策略创建、启停和监控
- **信号处理** - 智能交易信号生成和执行
- **风险控制** - 实时风险监控和限制

### 🏗️ 系统架构
- **模块化设计** - 清晰的模块分离和依赖管理
- **异步架构** - 基于Tokio的高并发处理
- **事件驱动** - 完整的事件系统和消息传递
- **类型安全** - Rust强类型系统保证运行时安全

### 📊 数据处理
- **多数据源** - 支持实时和历史数据聚合
- **智能缓存** - 高性能内存缓存和TTL管理
- **数据存储** - PostgreSQL数据库持久化
- **性能优化** - 索引优化和查询性能调优

### 🌐 Web界面
- **REST API** - 完整的RESTful API接口
- **实时推送** - WebSocket实时数据推送
- **管理界面** - 策略管理和监控界面
- **系统监控** - 健康检查和性能指标

## 📁 项目结构

```
SigmaX/
├── core/                        # 🏗️ 核心模块 - 基础类型和接口
├── data/                        # 📊 数据模块 - 市场数据处理
├── exchange/                    # 🏪 交易所模块 - 多交易所接口
├── strategies/                  # 🎯 策略模块 - 交易策略实现
├── engines/                     # ⚙️ 引擎模块 - 回测和实盘引擎
├── execution/                   # 📋 执行模块 - 订单执行管理
├── portfolio/                   # 💼 投资组合模块 - 资产管理
├── risk/                        # ⚠️ 风险模块 - 风险控制
├── reporting/                   # 📈 报告模块 - 性能分析
├── database/                    # 🗄️ 存储模块 - 数据持久化
├── performance_monitor/         # 📊 监控模块 - 性能监控
├── web/                         # 🌐 Web模块 - API和界面
├── database/                    # 🗃️ 数据库 - 部署和脚本
└── docs/                        # 📚 文档 - 技术文档和指南
```

## 🚀 快速开始

### 系统要求

- **Rust**: 1.70+
- **PostgreSQL**: 13+
- **操作系统**: Linux, macOS, Windows
- **内存**: 最低2GB，推荐4GB+

### 安装和运行

#### 1. 克隆项目
```bash
git clone https://github.com/your-org/SigmaX.git
cd SigmaX
```

#### 2. 设置数据库

**选项A: 使用云数据库（推荐）**
```bash
# Neon数据库（免费3GB）
DATABASE_URL="postgresql://user:<EMAIL>/neondb?sslmode=require"

# 初始化数据库
cd database/external-db
./setup.sh "$DATABASE_URL"
```

**选项B: 本地PostgreSQL**
```bash
# 启动本地PostgreSQL
createdb sigmax_trading

# 设置连接
export DATABASE_URL="postgresql://postgres:postgres@localhost:5432/sigmax_trading"
```

#### 3. 编译和运行
```bash
# 编译项目
cargo build --release

# 运行Web服务器
DATABASE_URL="your-database-url" cargo run --bin server

# 或运行回测引擎
cargo run --bin backtest_engine
```

#### 4. 访问界面
```bash
# Web界面
http://localhost:8080

# API文档
http://localhost:8080/api/docs

# 健康检查
http://localhost:8080/health
```

## 📊 开发状态

### 阶段完成情况

| 阶段 | 状态 | 进度 | 完成日期 | 主要功能 |
|------|------|------|----------|----------|
| 阶段1: 基础设施 | ✅ 已完成 | 100% | 2024-12-19 | 项目结构、核心模块 |
| 阶段2: 核心服务 | ✅ 已完成 | 100% | 2024-12-19 | 数据存储、缓存、事件系统 |
| 阶段3: Web界面 | ✅ 已完成 | 100% | 2024-12-19 | REST API、Web服务器 |
| 阶段4: 交易引擎 | ✅ 已完成 | 100% | 2024-12-19 | 回测引擎、实盘引擎 |
| 阶段5: 策略系统 | ✅ 已完成 | 100% | 2024-12-19 | 网格策略、策略管理 |

**总体进度**: 🎉 **100%** (5/5 阶段完成)

### 测试状态

```bash
# 运行所有测试
cargo test --workspace

# 测试结果统计
✅ 阶段2核心服务: 41/41 测试通过
✅ 阶段3Web界面: 5/5 测试通过
✅ 阶段4交易引擎: 8/8 测试通过
✅ 阶段5策略系统: 7/7 测试通过
✅ 总计: 61/61 测试通过 (100%)
```

### 编译状态

```bash
# 检查编译状态
cargo check --workspace
# ✅ 整个工作空间编译成功
# ⚠️ 仅有少量警告（未使用字段）
```

## 🎯 核心功能

### 网格交易策略
```rust
// 创建网格策略
let strategy = GridStrategy::new(
    grid_config,
    trading_pair,
    exchange_id,
    portfolio_manager,
    risk_manager,
    order_manager,
    data_provider,
).await?;

// 初始化策略
strategy.initialize().await?;
```

### 策略管理
```rust
// 创建策略管理器
let strategy_manager = StrategyManager::new(service_container);

// 创建和启动策略
let strategy_id = strategy_manager.create_strategy(config).await?;
strategy_manager.start_strategy(strategy_id).await?;
```

### REST API使用
```bash
# 获取系统状态
curl http://localhost:8080/api/system/status

# 获取订单列表
curl http://localhost:8080/api/orders

# 获取策略列表
curl http://localhost:8080/api/strategies
```

## 🛠️ 开发指南

### 环境配置

```bash
# 环境变量
export DATABASE_URL="postgresql://user:pass@host:port/db"
export WEB_HOST="0.0.0.0"
export WEB_PORT="8080"
export RUST_LOG="info"
```

### 开发工作流

```bash
# 1. 代码检查
cargo clippy --workspace

# 2. 格式化代码
cargo fmt --all

# 3. 运行测试
cargo test --workspace

# 4. 构建项目
cargo build --workspace

# 5. 运行特定模块测试
cargo test -p sigmax-strategies
```

### 添加新策略

1. **实现Strategy trait**
```rust
#[async_trait]
impl Strategy for YourStrategy {
    async fn initialize(&mut self) -> SigmaXResult<()> { /* ... */ }
    async fn on_market_data(&mut self, candle: &Candle) -> SigmaXResult<Vec<Order>> { /* ... */ }
    // ... 其他方法
}
```

2. **注册到工厂**
```rust
// 在factory.rs中添加新策略类型
pub enum StrategyType {
    Grid,
    YourStrategy, // 新增
}
```

3. **编写测试**
```rust
#[tokio::test]
async fn test_your_strategy() {
    // 测试策略逻辑
}
```

## 📚 文档资源

### 技术文档
- **[docs/project-status.md](docs/project-status.md)** - 项目状态总览
- **[docs/phase-5-strategies.md](docs/phase-5-strategies.md)** - 策略系统实现文档
- **[docs/phase-5-completion-summary.md](docs/phase-5-completion-summary.md)** - 阶段5完成总结
- **[docs/database-schema.md](docs/database-schema.md)** - 数据库架构设计

### 部署文档
- **[database/external-db/README.md](database/external-db/README.md)** - 外部数据库部署指南
- **[database/external-db/examples/](database/external-db/examples/)** - 云数据库设置指南

### API文档
- **REST API**: 启动服务器后访问 `http://localhost:8080/api/docs`
- **代码文档**: 运行 `cargo doc --open` 生成和查看

## 🔧 技术栈

### 核心技术
- **语言**: Rust 1.70+
- **异步运行时**: Tokio
- **数据库**: PostgreSQL 13+ / sqlx
- **Web框架**: Axum
- **序列化**: serde + serde_json

### 开发工具
- **构建系统**: Cargo workspace
- **测试框架**: Rust内置测试
- **代码格式化**: rustfmt
- **静态分析**: clippy
- **文档生成**: rustdoc

## 🤝 贡献指南

1. **Fork项目**
2. **创建功能分支** (`git checkout -b feature/amazing-feature`)
3. **提交更改** (`git commit -m 'Add amazing feature'`)
4. **推送分支** (`git push origin feature/amazing-feature`)
5. **创建Pull Request**

### 代码规范
- 遵循Rust官方代码风格
- 运行 `cargo fmt` 格式化代码
- 运行 `cargo clippy` 检查代码质量
- 为新功能编写测试
- 更新相关文档

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [Rust社区](https://www.rust-lang.org/) - 优秀的编程语言和生态
- [Tokio](https://tokio.rs/) - 异步运行时支持
- [PostgreSQL](https://www.postgresql.org/) - 可靠的数据库系统

---


##

https://mermaid-live.nodejs.cn/

回测执行循环-仓位更新流程图

graph TD
    A[开始回测循环] --> B[读取K线数据]
    B --> C[更新当前时间]
    C --> D[处理市场数据]
    D --> E[策略信号生成]
    
    E --> F{有新订单?}
    F -->|是| G[风险预检查]
    F -->|否| M[更新统计信息]
    
    G --> H{风险检查通过?}
    H -->|是| I[添加到待执行队列]
    H -->|否| J[拒绝订单]
    
    I --> K[处理待执行订单]
    J --> M
    
    K --> L{订单状态为Pending?}
    L -->|是| N[执行订单]
    L -->|否| M
    
    N --> O[计算执行价格]
    O --> P[计算手续费]
    P --> Q[创建交易记录]
    Q --> R[🔥 更新投资组合]
    
    R --> S[更新资产余额]
    S --> T[更新仓位信息]
    T --> U[计算已实现PnL]
    U --> V[扣除手续费]
    
    V --> W{需要创建快照?}
    W -->|是| X[创建投资组合快照]
    W -->|否| M
    
    X --> Y[计算总价值]
    Y --> Z[计算未实现PnL]
    Z --> AA[保存快照]
    
    AA --> M
    M --> BB[持仓风险检查]
    BB --> CC{还有更多K线?}
    CC -->|是| B
    CC -->|否| DD[计算最终结果]
    
    DD --> EE[结束回测]
    
    subgraph "仓位更新详细流程"
        R --> R1[买入订单处理]
        R --> R2[卖出订单处理]
        
        R1 --> R1A[增加持仓数量]
        R1A --> R1B[计算新平均价格]
        R1B --> R1C[更新基础资产余额]
        R1C --> R1D[减少计价资产余额]
        
        R2 --> R2A[减少持仓数量]
        R2A --> R2B[计算已实现盈亏]
        R2B --> R2C[减少基础资产余额]
        R2C --> R2D[增加计价资产余额]
    end
    
    subgraph "风险控制层"
        G --> G1[订单大小检查]
        G --> G2[资金充足性检查]
        G --> G3[持仓限制检查]
        G --> G4[策略规则检查]
        
        BB --> BB1[持仓集中度检查]
        BB --> BB2[最大回撤检查]
        BB --> BB3[杠杆比例检查]
    end
    
    style R fill:#ff9999
    style S fill:#ff9999
    style T fill:#ff9999
    style U fill:#ff9999
    style V fill:#ff9999


## SigmaX完整回测流程图

graph TD
    A[系统启动] --> B[初始化回测引擎]
    B --> C[加载配置参数]
    C --> D[验证配置有效性]
    D --> E{配置验证通过?}
    E -->|否| F[返回配置错误]
    E -->|是| G[初始化组件]
    
    G --> G1[创建投资组合管理器]
    G --> G2[创建风险管理器]
    G --> G3[创建策略管理器]
    G --> G4[创建数据管理器]
    
    G1 --> H[设置初始资金]
    G2 --> H
    G3 --> H
    G4 --> H
    
    H --> I[加载历史数据]
    I --> J{数据文件存在?}
    J -->|否| K[返回数据错误]
    J -->|是| L[解析K线数据]
    
    L --> M[验证数据完整性]
    M --> N{数据有效?}
    N -->|否| O[返回数据格式错误]
    N -->|是| P[创建初始投资组合快照]
    
    P --> Q[设置引擎状态为Starting]
    Q --> R[开始主回测循环]
    
    subgraph "主回测循环"
        R --> S[获取下一个K线数据]
        S --> T[更新当前时间戳]
        T --> U[处理市场数据]
        
        U --> V[策略信号生成]
        V --> W{有新的交易信号?}
        W -->|否| AA[跳过订单处理]
        W -->|是| X[生成订单]
        
        X --> Y[订单风险预检查]
        Y --> Z{风险检查通过?}
        Z -->|否| Z1[拒绝订单并记录]
        Z -->|是| Z2[添加到待执行队列]
        
        Z1 --> AA
        Z2 --> BB[处理待执行订单]
        AA --> BB
        
        BB --> CC{有待执行订单?}
        CC -->|否| II[跳过订单执行]
        CC -->|是| DD[订单执行处理]
        
        DD --> EE[计算执行价格]
        EE --> FF[计算交易手续费]
        FF --> GG[创建交易记录]
        GG --> HH[🔥 更新投资组合]
        
        HH --> HH1[更新资产余额]
        HH1 --> HH2[更新仓位信息]
        HH2 --> HH3[计算已实现PnL]
        HH3 --> HH4[扣除手续费]
        HH4 --> HH5[更新未实现PnL]
        
        HH5 --> II[检查快照条件]
        II --> JJ{需要创建快照?}
        JJ -->|是| KK[创建投资组合快照]
        JJ -->|否| LL[更新统计信息]
        
        KK --> KK1[计算总资产价值]
        KK1 --> KK2[计算当前PnL]
        KK2 --> KK3[保存快照数据]
        KK3 --> LL
        
        LL --> MM[持仓风险检查]
        MM --> NN{风险检查通过?}
        NN -->|否| OO[记录风险警告]
        NN -->|是| PP[更新进度指标]
        OO --> PP
        
        PP --> QQ{还有更多K线数据?}
        QQ -->|是| S
        QQ -->|否| RR[退出主循环]
    end
    
    RR --> SS[设置引擎状态为Stopped]
    SS --> TT[计算最终回测结果]
    
    TT --> UU[计算总收益率]
    UU --> VV[计算最大回撤]
    VV --> WW[计算夏普比率]
    WW --> XX[计算胜率]
    XX --> YY[计算盈亏比]
    YY --> ZZ[生成性能报告]
    
    ZZ --> AAA[保存回测结果]
    AAA --> BBB[清理资源]
    BBB --> CCC[返回结果给API]
    
    subgraph "风险控制层"
        Y --> Y1[订单大小检查]
        Y --> Y2[资金充足性检查]
        Y --> Y3[持仓限制检查]
        Y --> Y4[策略规则检查]
        
        MM --> MM1[持仓集中度检查]
        MM --> MM2[最大回撤检查]
        MM --> MM3[杠杆比例检查]
        MM --> MM4[止损检查]
    end
    
    subgraph "数据处理层"
        U --> U1[价格数据验证]
        U --> U2[技术指标计算]
        U --> U3[市场状态更新]
        
        V --> V1[策略参数获取]
        V --> V2[信号强度计算]
        V --> V3[订单参数生成]
    end
    
    subgraph "投资组合管理层"
        HH --> Portfolio1[余额管理]
        HH --> Portfolio2[仓位跟踪]
        HH --> Portfolio3[PnL计算]
        HH --> Portfolio4[价值评估]
        
        Portfolio1 --> Portfolio1a[基础资产更新]
        Portfolio1 --> Portfolio1b[计价资产更新]
        
        Portfolio2 --> Portfolio2a[持仓数量更新]
        Portfolio2 --> Portfolio2b[平均价格计算]
        
        Portfolio3 --> Portfolio3a[已实现盈亏]
        Portfolio3 --> Portfolio3b[未实现盈亏]
        
        Portfolio4 --> Portfolio4a[总资产价值]
        Portfolio4 --> Portfolio4b[净资产价值]
    end
    
    subgraph "性能分析层"
        TT --> Perf1[收益率分析]
        TT --> Perf2[风险指标分析]
        TT --> Perf3[交易统计分析]
        
        Perf1 --> Perf1a[总收益率]
        Perf1 --> Perf1b[年化收益率]
        Perf1 --> Perf1c[月度收益率]
        
        Perf2 --> Perf2a[最大回撤]
        Perf2 --> Perf2b[波动率]
        Perf2 --> Perf2c[夏普比率]
        
        Perf3 --> Perf3a[交易次数]
        Perf3 --> Perf3b[胜率]
        Perf3 --> Perf3c[盈亏比]
    end
    
    subgraph "错误处理"
        F --> Error1[配置错误处理]
        K --> Error2[数据错误处理]
        O --> Error3[格式错误处理]
        Z1 --> Error4[风险拒绝处理]
        OO --> Error5[风险警告处理]
    end
    
    style R fill:#e1f5fe
    style HH fill:#ffebee
    style TT fill:#f3e5f5
    style V fill:#e8f5e8
    style MM fill:#fff3e0
    style Portfolio1 fill:#ffebee
    style Portfolio2 fill:#ffebee
    style Portfolio3 fill:#ffebee
    style Portfolio4 fill:#ffebee

## SigmaX回测组件交互时序图
sequenceDiagram
    participant API as Web API
    participant Engine as BacktestEngine
    participant Config as ConfigManager
    participant Data as DataManager
    participant Strategy as StrategyManager
    participant Risk as RiskManager
    participant Portfolio as PortfolioManager
    participant DB as Database
    
    Note over API,DB: 回测初始化阶段
    API->>Engine: 创建回测引擎
    Engine->>Config: 加载配置参数
    Config-->>Engine: 返回配置信息
    Engine->>Data: 加载历史数据
    Data->>DB: 查询K线数据
    DB-->>Data: 返回历史数据
    Data-->>Engine: 返回解析后的数据
    Engine->>Portfolio: 初始化投资组合
    Portfolio-->>Engine: 创建初始快照
    Engine->>Strategy: 加载交易策略
    Strategy-->>Engine: 策略初始化完成
    Engine->>Risk: 初始化风险管理器
    Risk-->>Engine: 风险规则加载完成
    
    Note over API,DB: 主回测循环阶段
    loop 每个K线数据
        Engine->>Data: 获取下一个K线
        Data-->>Engine: 返回K线数据
        Engine->>Engine: 更新当前时间戳
        
        Note over Engine: 市场数据处理
        Engine->>Strategy: 处理市场数据
        Strategy-->>Engine: 数据处理完成
        
        Note over Engine: 策略信号生成
        Engine->>Strategy: 生成交易信号
        Strategy->>Strategy: 计算技术指标
        Strategy->>Strategy: 应用策略逻辑
        Strategy-->>Engine: 返回订单列表
        
        Note over Engine: 风险检查
        alt 有新订单
            loop 每个订单
                Engine->>Risk: 订单风险预检查
                Risk->>Risk: 检查订单大小
                Risk->>Risk: 检查资金充足性
                Risk->>Portfolio: 获取当前余额
                Portfolio-->>Risk: 返回余额信息
                Risk-->>Engine: 返回风险检查结果
                
                alt 风险检查通过
                    Engine->>Engine: 添加到待执行队列
                else 风险检查失败
                    Engine->>Engine: 拒绝订单并记录
                end
            end
        end
        
        Note over Engine: 订单执行
        alt 有待执行订单
            loop 每个待执行订单
                Engine->>Engine: 计算执行价格
                Engine->>Engine: 计算交易手续费
                Engine->>Engine: 创建交易记录
                
                Note over Engine: 🔥 投资组合更新
                Engine->>Portfolio: 更新资产余额
                Portfolio->>Portfolio: 更新基础资产
                Portfolio->>Portfolio: 更新计价资产
                Portfolio->>Portfolio: 扣除手续费
                Portfolio-->>Engine: 余额更新完成
                
                Engine->>Portfolio: 更新仓位信息
                Portfolio->>Portfolio: 计算平均价格
                Portfolio->>Portfolio: 计算已实现PnL
                Portfolio->>Portfolio: 更新未实现PnL
                Portfolio-->>Engine: 仓位更新完成
                
                Engine->>DB: 保存交易记录
                DB-->>Engine: 保存完成
            end
        end
        
        Note over Engine: 投资组合快照
        alt 需要创建快照
            Engine->>Portfolio: 计算总资产价值
            Portfolio->>Portfolio: 汇总各资产价值
            Portfolio-->>Engine: 返回总价值
            Engine->>Portfolio: 获取当前PnL
            Portfolio-->>Engine: 返回PnL数据
            Engine->>DB: 保存投资组合快照
            DB-->>Engine: 快照保存完成
        end
        
        Note over Engine: 统计信息更新
        Engine->>Engine: 更新交易统计
        Engine->>Portfolio: 获取投资组合状态
        Portfolio-->>Engine: 返回状态信息
        
        Note over Engine: 持仓风险检查
        Engine->>Risk: 检查持仓风险
        Risk->>Portfolio: 获取当前持仓
        Portfolio-->>Risk: 返回持仓信息
        Risk->>Risk: 检查持仓集中度
        Risk->>Risk: 检查最大回撤
        Risk-->>Engine: 返回风险检查结果
        
        alt 风险检查失败
            Engine->>Engine: 记录风险警告
        end
        
        Engine->>Engine: 更新进度指标
        Engine->>API: 更新进度状态
    end
    
    Note over API,DB: 结果计算阶段
    Engine->>Portfolio: 获取最终投资组合状态
    Portfolio-->>Engine: 返回最终状态
    Engine->>Engine: 计算总收益率
    Engine->>Engine: 计算最大回撤
    Engine->>Engine: 计算夏普比率
    Engine->>Engine: 计算胜率
    Engine->>Engine: 计算盈亏比
    Engine->>Engine: 生成性能报告
    
    Note over Engine: 风险报告生成
    Engine->>Risk: 生成风险统计报告
    Risk-->>Engine: 返回风险报告
    
    Engine->>DB: 保存回测结果
    DB-->>Engine: 结果保存完成
    Engine->>Engine: 清理资源
    Engine-->>API: 返回回测结果
    
    Note over API,DB: API响应阶段
    API->>DB: 查询回测结果
    DB-->>API: 返回结果数据
    API->>DB: 查询交易记录
    DB-->>API: 返回交易数据
    API->>DB: 查询投资组合快照
    DB-->>API: 返回快照数据


## SigmaX回测数据流图

graph LR
    subgraph "输入数据层"
        A1[配置参数] --> A2[回测配置]
        A3[历史K线数据] --> A4[市场数据]
        A5[策略参数] --> A6[策略配置]
        A7[风险参数] --> A8[风险配置]
        A9[初始资金] --> A10[投资组合配置]
    end
    
    subgraph "数据处理层"
        A2 --> B1[配置验证器]
        A4 --> B2[数据解析器]
        A6 --> B3[策略加载器]
        A8 --> B4[风险规则加载器]
        A10 --> B5[投资组合初始化器]
        
        B1 --> C1[有效配置]
        B2 --> C2[标准化K线数据]
        B3 --> C3[策略实例]
        B4 --> C4[风险规则集]
        B5 --> C5[初始投资组合状态]
    end
    
    subgraph "核心执行层"
        C1 --> D1[回测引擎]
        C2 --> D1
        C3 --> D1
        C4 --> D1
        C5 --> D1
        
        D1 --> E1[市场数据处理器]
        D1 --> E2[策略信号生成器]
        D1 --> E3[风险检查器]
        D1 --> E4[订单执行器]
        D1 --> E5[投资组合更新器]
        
        E1 --> F1[当前市场状态]
        E2 --> F2[交易信号列表]
        E3 --> F3[风险检查结果]
        E4 --> F4[执行交易记录]
        E5 --> F5[更新后的投资组合]
    end
    
    subgraph "状态管理层"
        F1 --> G1[市场状态缓存]
        F2 --> G2[订单队列]
        F3 --> G3[风险状态记录]
        F4 --> G4[交易历史]
        F5 --> G5[投资组合快照]
        
        G1 --> H1[价格数据]
        G1 --> H2[技术指标]
        G2 --> H3[待执行订单]
        G2 --> H4[已执行订单]
        G3 --> H5[风险警告]
        G3 --> H6[风险统计]
        G4 --> H7[买入记录]
        G4 --> H8[卖出记录]
        G5 --> H9[余额变化]
        G5 --> H10[仓位变化]
    end
    
    subgraph "计算分析层"
        H1 --> I1[价值计算器]
        H2 --> I2[指标计算器]
        H7 --> I3[盈亏计算器]
        H8 --> I3
        H9 --> I4[收益率计算器]
        H10 --> I5[风险指标计算器]
        
        I1 --> J1[总资产价值]
        I2 --> J2[技术分析结果]
        I3 --> J3[已实现/未实现PnL]
        I4 --> J4[收益率序列]
        I5 --> J5[风险指标]
    end
    
    subgraph "结果输出层"
        J1 --> K1[性能指标]
        J2 --> K2[策略效果分析]
        J3 --> K3[盈亏分析]
        J4 --> K4[收益率分析]
        J5 --> K5[风险分析]
        
        K1 --> L1[总收益率]
        K1 --> L2[年化收益率]
        K2 --> L3[信号准确率]
        K2 --> L4[策略胜率]
        K3 --> L5[最大盈利]
        K3 --> L6[最大亏损]
        K4 --> L7[夏普比率]
        K4 --> L8[最大回撤]
        K5 --> L9[风险暴露]
        K5 --> L10[风险调整收益]
    end
    
    subgraph "持久化层"
        G4 --> M1[(交易数据库)]
        G5 --> M2[(投资组合数据库)]
        G3 --> M3[(风险记录数据库)]
        K1 --> M4[(回测结果数据库)]
        K2 --> M4
        K3 --> M4
        K4 --> M4
        K5 --> M4
    end
    
    subgraph "API输出层"
        M1 --> N1[交易记录API]
        M2 --> N2[投资组合API]
        M3 --> N3[风险报告API]
        M4 --> N4[回测结果API]
        
        N1 --> O1[JSON交易数据]
        N2 --> O2[JSON投资组合数据]
        N3 --> O3[JSON风险数据]
        N4 --> O4[JSON回测报告]
    end
    
    style D1 fill:#e1f5fe
    style E5 fill:#ffebee
    style I3 fill:#f3e5f5
    style K1 fill:#e8f5e8
    style M4 fill:#fff3e0


## 完整回测流程架构

graph TB
    subgraph "🚀 SigmaX-R 完整回测流程架构"
        subgraph "第一阶段：初始化与配置 (95%)"
            A1[引擎初始化] --> A2[配置加载]
            A2 --> A3[组件注入]
            A3 --> A4[依赖验证]
        end
        
        subgraph "第二阶段：数据加载与验证 (95%)"
            B1[历史数据加载] --> B2[数据格式验证]
            B2 --> B3[数据完整性检查]
            B3 --> B4[数据质量报告]
        end
        
        subgraph "第三阶段：核心执行循环 (92%)"
            C1[时间驱动循环] --> C2[市场数据处理]
            C2 --> C3[策略信号生成]
            C3 --> C4[订单队列管理]
            C4 --> C5[投资组合更新]
            C5 --> C6[统计信息更新]
        end
        
        subgraph "第四阶段：订单执行与风险管理 (95%)"
            D1[订单风险预检] --> D2[资金充足性检查]
            D2 --> D3[统一风险验证]
            D3 --> D4[订单执行模拟]
            D4 --> D5[交易记录生成]
        end
        
        subgraph "第五阶段：投资组合管理 (88%)"
            E1[仓位跟踪更新] --> E2[PnL实时计算]
            E2 --> E3[风险指标计算]
            E3 --> E4[投资组合快照]
        end
        
        subgraph "第六阶段：结果分析与报告 (85%)"
            F1[性能指标计算] --> F2[风险分析报告]
            F2 --> F3[基准对比分析]
            F3 --> F4[可视化报告生成]
        end
    end
    
    A4 --> B1
    B4 --> C1
    C6 --> D1
    D5 --> E1
    E4 --> F1
    
    style A1 fill:#e1f5fe
    style B1 fill:#f3e5f5
    style C1 fill:#e8f5e8
    style D1 fill:#fff3e0
    style E1 fill:#fce4ec
    style F1 fill:#f1f8e9



回测流程示例
    sequenceDiagram
    participant Frontend as 🌐 前端界面
    participant WebAPI as 🔌 Web API
    participant Engine as ⚙️ 引擎层
    participant Strategy as 🎯 策略层
    participant Risk as ⚠️ 风险管理
    participant Portfolio as 💼 投资组合
    participant Exchange as 🏪 交易所
    participant Database as 🗄️ 数据库

    Note over Frontend,Database: 回测流程示例
    
    Frontend->>WebAPI: POST /api/v1/engines (创建引擎)
    WebAPI->>Engine: BacktestEngine::new()
    Engine->>Database: 保存引擎配置
    WebAPI-->>Frontend: 返回引擎ID
    
    Frontend->>WebAPI: POST /api/v1/engines/:id/backtest/config
    WebAPI->>Engine: 配置策略参数
    Engine->>Strategy: 加载策略配置
    WebAPI-->>Frontend: 配置成功
    
    Frontend->>WebAPI: POST /api/v1/engines/:id/start
    WebAPI->>Engine: start_backtest()
    
    loop 历史数据处理
        Engine->>Engine: load_historical_data()
        Engine->>Strategy: on_market_data(candle)
        Strategy->>Strategy: 分析市场数据
        Strategy->>Strategy: 生成交易信号
        Strategy-->>Engine: 返回订单列表
        
        Engine->>Risk: 风险检查
        Risk-->>Engine: 风险验证结果
        
        Engine->>Portfolio: 更新投资组合
        Engine->>Exchange: 模拟订单执行
        Exchange-->>Engine: 执行结果
        
        Engine->>Database: 保存交易记录
    end
    
    Engine->>Engine: 生成回测报告
    Engine-->>WebAPI: 回测完成
    WebAPI-->>Frontend: 返回结果

**版本**: v0.1.0-dev
**最后更新**: 2024-12-19
**维护者**: SigmaX Team
**状态**: ✅ 核心功能完成，准备生产部署
