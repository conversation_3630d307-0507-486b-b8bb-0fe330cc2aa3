#!/usr/bin/env python3
"""
SigmaX API 验证中心 - 主验证器
提供完整的API验证功能，支持多层次、多场景的验证测试

功能特性：
- 分层验证体系（基础、功能、性能、集成、端到端）
- 灵活的测试场景配置
- 实时监控和报告
- 自动化验证流程
- 详细的性能分析
"""

import asyncio
import argparse
import json
import logging
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from tools.validators.basic_validator import BasicValidator
from tools.validators.functional_validator import FunctionalValidator
from tools.validators.performance_validator import PerformanceValidator
from tools.validators.integration_validator import IntegrationValidator
from tools.validators.e2e_validator import E2EValidator
from tools.analyzers.report_generator import ReportGenerator
from tools.analyzers.performance_analyzer import PerformanceAnalyzer

# 配置日志
log_dir = Path("api_validation/logs/validation")
log_dir.mkdir(parents=True, exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_dir / 'validation.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class APIValidationCenter:
    """API验证中心"""

    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.load_configurations()
        self.validators = {}
        self.results = {}
        self.start_time = None
        self.end_time = None

    def load_configurations(self):
        """加载配置文件"""
        try:
            # 加载环境配置
            with open(self.config_dir / "environments.json", 'r', encoding='utf-8') as f:
                self.environments = json.load(f)

            # 加载测试场景
            with open(self.config_dir / "test_scenarios.json", 'r', encoding='utf-8') as f:
                self.test_scenarios = json.load(f)

            # 加载性能阈值
            with open(self.config_dir / "performance_thresholds.json", 'r', encoding='utf-8') as f:
                self.performance_thresholds = json.load(f)

            logger.info("配置文件加载成功")

        except Exception as e:
            logger.error(f"配置文件加载失败: {str(e)}")
            raise

    def get_active_environment(self) -> Dict[str, Any]:
        """获取活动环境配置"""
        for env_name, env_config in self.environments["environments"].items():
            if env_config.get("enabled", False):
                return env_config

        # 默认返回本地环境
        return self.environments["environments"]["local"]

    def initialize_validators(self, environment: Dict[str, Any]):
        """初始化验证器"""
        self.validators = {
            "basic": BasicValidator(environment, self.performance_thresholds),
            "functional": FunctionalValidator(environment, self.test_scenarios),
            "performance": PerformanceValidator(environment, self.performance_thresholds),
            "integration": IntegrationValidator(environment, self.test_scenarios),
            "e2e": E2EValidator(environment, self.test_scenarios)
        }
        logger.info("验证器初始化完成")

    async def run_validation_level(self, level: str) -> Dict[str, Any]:
        """运行指定层次的验证"""
        if level not in self.validators:
            raise ValueError(f"未知的验证层次: {level}")

        logger.info(f"开始运行 {level} 层次验证")
        validator = self.validators[level]

        start_time = time.time()
        result = await validator.validate()
        duration = time.time() - start_time

        result["duration"] = duration
        result["level"] = level
        result["timestamp"] = datetime.now().isoformat()

        self.results[level] = result
        logger.info(f"{level} 层次验证完成，耗时 {duration:.2f}s")

        return result

    async def run_validation_scenario(self, scenario_name: str) -> Dict[str, Any]:
        """运行指定场景的验证"""
        if scenario_name not in self.test_scenarios["test_scenarios"]:
            raise ValueError(f"未知的测试场景: {scenario_name}")

        scenario = self.test_scenarios["test_scenarios"][scenario_name]
        logger.info(f"开始运行场景验证: {scenario['name']}")

        # 根据场景类型选择合适的验证器
        if scenario.get("priority") == "critical":
            validator = self.validators["functional"]
        elif "performance" in scenario_name:
            validator = self.validators["performance"]
        elif "websocket" in scenario_name:
            validator = self.validators["integration"]
        else:
            validator = self.validators["basic"]

        start_time = time.time()
        result = await validator.validate_scenario(scenario_name, scenario)
        duration = time.time() - start_time

        result["duration"] = duration
        result["scenario"] = scenario_name
        result["timestamp"] = datetime.now().isoformat()

        self.results[f"scenario_{scenario_name}"] = result
        logger.info(f"场景验证完成: {scenario['name']}，耗时 {duration:.2f}s")

        return result

    async def run_all_validations(self) -> Dict[str, Any]:
        """运行所有验证"""
        logger.info("开始运行完整验证套件")

        validation_levels = ["basic", "functional", "performance", "integration", "e2e"]

        for level in validation_levels:
            try:
                await self.run_validation_level(level)
            except Exception as e:
                logger.error(f"{level} 层次验证失败: {str(e)}")
                self.results[level] = {
                    "status": "error",
                    "error": str(e),
                    "level": level,
                    "timestamp": datetime.now().isoformat()
                }

        return self.results

    def generate_summary_report(self) -> Dict[str, Any]:
        """生成验证摘要报告"""
        total_tests = 0
        passed_tests = 0
        failed_tests = 0
        error_tests = 0
        total_duration = 0

        for result in self.results.values():
            if isinstance(result, dict):
                total_duration += result.get("duration", 0)

                if result.get("status") == "pass":
                    passed_tests += result.get("test_count", 1)
                elif result.get("status") == "fail":
                    failed_tests += result.get("test_count", 1)
                elif result.get("status") == "error":
                    error_tests += result.get("test_count", 1)

                total_tests += result.get("test_count", 1)

        summary = {
            "validation_summary": {
                "start_time": self.start_time.isoformat() if self.start_time else None,
                "end_time": self.end_time.isoformat() if self.end_time else None,
                "total_duration": total_duration,
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": failed_tests,
                "error_tests": error_tests,
                "success_rate": (passed_tests / total_tests * 100) if total_tests > 0 else 0
            },
            "level_results": {},
            "scenario_results": {},
            "performance_metrics": self.extract_performance_metrics(),
            "recommendations": self.generate_recommendations()
        }

        # 分类结果
        for key, result in self.results.items():
            if key.startswith("scenario_"):
                summary["scenario_results"][key] = result
            else:
                summary["level_results"][key] = result

        return summary

    def extract_performance_metrics(self) -> Dict[str, Any]:
        """提取性能指标"""
        metrics = {
            "response_times": {},
            "throughput": {},
            "error_rates": {},
            "resource_usage": {}
        }

        # 从验证结果中提取性能数据
        for result in self.results.values():
            if isinstance(result, dict) and "performance_data" in result:
                perf_data = result["performance_data"]

                # 响应时间
                if "response_times" in perf_data:
                    metrics["response_times"].update(perf_data["response_times"])

                # 吞吐量
                if "throughput" in perf_data:
                    metrics["throughput"].update(perf_data["throughput"])

                # 错误率
                if "error_rates" in perf_data:
                    metrics["error_rates"].update(perf_data["error_rates"])

        return metrics

    def generate_recommendations(self) -> List[str]:
        """生成改进建议"""
        recommendations = []

        # 分析验证结果并生成建议
        for level, result in self.results.items():
            if isinstance(result, dict):
                if result.get("status") == "fail":
                    recommendations.append(f"修复 {level} 层次的失败测试")

                if result.get("status") == "error":
                    recommendations.append(f"解决 {level} 层次的错误问题")

                # 性能建议
                if "performance_data" in result:
                    perf_data = result["performance_data"]
                    if perf_data.get("avg_response_time", 0) > 1000:
                        recommendations.append(f"优化 {level} 层次的响应时间")

        if not recommendations:
            recommendations.append("所有验证都通过了，系统运行良好！")

        return recommendations

    async def save_results(self, output_file: Optional[str] = None):
        """保存验证结果"""
        if not output_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"reports/validation_report_{timestamp}.json"

        # 确保目录存在
        Path(output_file).parent.mkdir(parents=True, exist_ok=True)

        # 生成完整报告
        report = self.generate_summary_report()
        report["detailed_results"] = self.results

        # 保存到文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)

        logger.info(f"验证结果已保存到: {output_file}")
        return output_file

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="SigmaX API 验证中心")
    parser.add_argument("--level", choices=["basic", "functional", "performance", "integration", "e2e"],
                       help="运行指定层次的验证")
    parser.add_argument("--scenario", help="运行指定场景的验证")
    parser.add_argument("--all", action="store_true", help="运行所有验证")
    parser.add_argument("--output", help="输出文件路径")
    parser.add_argument("--config-dir", default="config", help="配置文件目录")

    args = parser.parse_args()

    # 创建验证中心
    validation_center = APIValidationCenter(args.config_dir)

    # 获取环境配置
    environment = validation_center.get_active_environment()
    logger.info(f"使用环境: {environment['name']}")

    # 初始化验证器
    validation_center.initialize_validators(environment)

    # 记录开始时间
    validation_center.start_time = datetime.now()

    try:
        # 根据参数运行相应的验证
        if args.level:
            await validation_center.run_validation_level(args.level)
        elif args.scenario:
            await validation_center.run_validation_scenario(args.scenario)
        elif args.all:
            await validation_center.run_all_validations()
        else:
            # 默认运行基础验证
            await validation_center.run_validation_level("basic")

        # 记录结束时间
        validation_center.end_time = datetime.now()

        # 保存结果
        output_file = await validation_center.save_results(args.output)

        # 生成摘要
        summary = validation_center.generate_summary_report()

        # 输出摘要
        print("\n" + "="*60)
        print("🎯 SigmaX API 验证摘要")
        print("="*60)
        print(f"总测试数: {summary['validation_summary']['total_tests']}")
        print(f"通过: {summary['validation_summary']['passed_tests']}")
        print(f"失败: {summary['validation_summary']['failed_tests']}")
        print(f"错误: {summary['validation_summary']['error_tests']}")
        print(f"成功率: {summary['validation_summary']['success_rate']:.1f}%")
        print(f"总耗时: {summary['validation_summary']['total_duration']:.2f}s")
        print(f"报告文件: {output_file}")

        # 输出建议
        if summary['recommendations']:
            print("\n📋 改进建议:")
            for i, rec in enumerate(summary['recommendations'], 1):
                print(f"  {i}. {rec}")

        # 返回适当的退出码
        if summary['validation_summary']['failed_tests'] > 0 or summary['validation_summary']['error_tests'] > 0:
            return 1
        else:
            return 0

    except Exception as e:
        logger.error(f"验证执行失败: {str(e)}")
        return 1

if __name__ == "__main__":
    exit(asyncio.run(main()))
