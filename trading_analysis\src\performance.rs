//! 交易绩效分析模块
//! 
//! 提供专业的交易绩效评估指标，包括夏普比率、最大回撤、卡尔马比率等。

use crate::{Result, FifoAnalysisResult, RealizedTradePair};
use rust_decimal::Decimal;
use rust_decimal::prelude::*;
use chrono::{DateTime, Utc};
use std::collections::HashMap;

/// 绩效分析结果
#[derive(Debug, Clone)]
pub struct PerformanceMetrics {
    /// 总收益率
    pub total_return: Decimal,
    /// 年化收益率
    pub annualized_return: Decimal,
    /// 夏普比率
    pub sharpe_ratio: Decimal,
    /// 最大回撤
    pub max_drawdown: Decimal,
    /// 卡尔马比率 (年化收益率 / 最大回撤)
    pub calmar_ratio: Decimal,
    /// 胜率
    pub win_rate: Decimal,
    /// 盈亏比
    pub profit_factor: Decimal,
    /// 平均持仓时间（天）
    pub average_holding_period: Decimal,
    /// 交易频率（每月交易次数）
    pub trading_frequency: Decimal,
    /// 波动率（年化）
    pub volatility: Decimal,
    /// 索提诺比率 (只考虑下行风险的夏普比率)
    pub sortino_ratio: Decimal,
}

impl PerformanceMetrics {
    /// 创建新的绩效指标实例
    pub fn new() -> Self {
        Self {
            total_return: Decimal::ZERO,
            annualized_return: Decimal::ZERO,
            sharpe_ratio: Decimal::ZERO,
            max_drawdown: Decimal::ZERO,
            calmar_ratio: Decimal::ZERO,
            win_rate: Decimal::ZERO,
            profit_factor: Decimal::ZERO,
            average_holding_period: Decimal::ZERO,
            trading_frequency: Decimal::ZERO,
            volatility: Decimal::ZERO,
            sortino_ratio: Decimal::ZERO,
        }
    }

    /// 判断是否为优秀的交易表现
    pub fn is_excellent_performance(&self) -> bool {
        self.sharpe_ratio > Decimal::from(2) && 
        self.max_drawdown < Decimal::from_f32_retain(0.1).unwrap() &&
        self.win_rate > Decimal::from_f32_retain(0.6).unwrap()
    }

    /// 获取风险等级
    pub fn risk_level(&self) -> RiskLevel {
        if self.max_drawdown > Decimal::from_f32_retain(0.3).unwrap() {
            RiskLevel::High
        } else if self.max_drawdown > Decimal::from_f32_retain(0.15).unwrap() {
            RiskLevel::Medium
        } else {
            RiskLevel::Low
        }
    }
}

/// 风险等级枚举
#[derive(Debug, Clone, PartialEq)]
pub enum RiskLevel {
    /// 低风险
    Low,
    /// 中等风险
    Medium,
    /// 高风险
    High,
}

/// 计算交易绩效指标
/// 
/// # 参数
/// * `fifo_result` - FIFO分析结果
/// * `initial_capital` - 初始资金
/// * `risk_free_rate` - 无风险利率（年化，如0.03表示3%）
/// 
/// # 返回值
/// * `PerformanceMetrics` - 完整的绩效分析结果
pub fn calculate_performance_metrics(
    fifo_result: &FifoAnalysisResult,
    initial_capital: Decimal,
    risk_free_rate: Decimal,
) -> Result<PerformanceMetrics> {
    if fifo_result.realized_pnl_pairs.is_empty() {
        return Ok(PerformanceMetrics::new());
    }

    let mut metrics = PerformanceMetrics::new();
    
    // 基本指标
    let total_pnl = fifo_result.total_realized_pnl();
    metrics.total_return = total_pnl / initial_capital;
    metrics.win_rate = fifo_result.win_rate();
    metrics.profit_factor = fifo_result.profit_factor();

    // 时间相关计算
    let (start_date, end_date) = get_trading_period(&fifo_result.realized_pnl_pairs)?;
    let trading_days = (end_date - start_date).num_days() as f64;
    let trading_years = trading_days / 365.25;

    // 年化收益率
    if trading_years > 0.0 {
        let annual_factor = Decimal::from_f64_retain(1.0 / trading_years).unwrap_or(Decimal::ONE);
        metrics.annualized_return = metrics.total_return * annual_factor;
    }

    // 计算每日收益率序列
    let daily_returns = calculate_daily_returns(&fifo_result.realized_pnl_pairs, initial_capital)?;
    
    // 波动率（年化）
    metrics.volatility = calculate_volatility(&daily_returns, trading_years)?;
    
    // 夏普比率
    if metrics.volatility > Decimal::ZERO {
        metrics.sharpe_ratio = (metrics.annualized_return - risk_free_rate) / metrics.volatility;
    }

    // 索提诺比率（只考虑下行波动率）
    let downside_volatility = calculate_downside_volatility(&daily_returns, trading_years)?;
    if downside_volatility > Decimal::ZERO {
        metrics.sortino_ratio = (metrics.annualized_return - risk_free_rate) / downside_volatility;
    }

    // 最大回撤
    metrics.max_drawdown = calculate_max_drawdown(&fifo_result.realized_pnl_pairs, initial_capital)?;
    
    // 卡尔马比率
    if metrics.max_drawdown > Decimal::ZERO {
        metrics.calmar_ratio = metrics.annualized_return / metrics.max_drawdown;
    }

    // 平均持仓时间
    metrics.average_holding_period = calculate_average_holding_period(&fifo_result.realized_pnl_pairs)?;
    
    // 交易频率（每月交易次数）
    if trading_years > 0.0 {
        let months = trading_years * 12.0;
        let trade_count = fifo_result.realized_pnl_pairs.len() as f64;
        metrics.trading_frequency = Decimal::from_f64_retain(trade_count / months).unwrap_or(Decimal::ZERO);
    }

    Ok(metrics)
}

/// 获取交易周期的开始和结束时间
fn get_trading_period(trades: &[RealizedTradePair]) -> Result<(DateTime<Utc>, DateTime<Utc>)> {
    if trades.is_empty() {
        return Err(crate::FifoAnalysisError::InvalidInput("交易列表为空".to_string()));
    }

    let start_date = trades.iter()
        .map(|t| t.entry_timestamp)
        .min()
        .unwrap();
    
    let end_date = trades.iter()
        .map(|t| t.exit_timestamp)
        .max()
        .unwrap();

    Ok((start_date, end_date))
}

/// 计算每日收益率序列
fn calculate_daily_returns(
    trades: &[RealizedTradePair], 
    initial_capital: Decimal
) -> Result<Vec<Decimal>> {
    // 按日期分组交易
    let mut daily_pnl: HashMap<chrono::NaiveDate, Decimal> = HashMap::new();
    
    for trade in trades {
        let date = trade.exit_timestamp.date_naive();
        *daily_pnl.entry(date).or_insert(Decimal::ZERO) += trade.pnl;
    }

    // 转换为收益率
    let daily_returns: Vec<Decimal> = daily_pnl.values()
        .map(|pnl| pnl / initial_capital)
        .collect();

    Ok(daily_returns)
}

/// 计算波动率（年化）
fn calculate_volatility(daily_returns: &[Decimal], _trading_years: f64) -> Result<Decimal> {
    if daily_returns.len() < 2 {
        return Ok(Decimal::ZERO);
    }

    let mean = daily_returns.iter().sum::<Decimal>() / Decimal::from(daily_returns.len());
    
    let variance: Decimal = daily_returns.iter()
        .map(|r| (*r - mean) * (*r - mean))
        .sum::<Decimal>() / Decimal::from(daily_returns.len() - 1);

    let daily_volatility = variance.sqrt().unwrap_or(Decimal::ZERO);
    
    // 年化波动率 = 日波动率 * sqrt(252)
    let annualization_factor = Decimal::from_f64_retain(252.0_f64.sqrt()).unwrap_or(Decimal::ONE);
    Ok(daily_volatility * annualization_factor)
}

/// 计算下行波动率（用于索提诺比率）
fn calculate_downside_volatility(daily_returns: &[Decimal], _trading_years: f64) -> Result<Decimal> {
    if daily_returns.len() < 2 {
        return Ok(Decimal::ZERO);
    }

    // 只考虑负收益率
    let negative_returns: Vec<Decimal> = daily_returns.iter()
        .filter(|&&r| r < Decimal::ZERO)
        .copied()
        .collect();

    if negative_returns.is_empty() {
        return Ok(Decimal::ZERO);
    }

    let mean_negative = negative_returns.iter().sum::<Decimal>() / Decimal::from(negative_returns.len());
    
    let downside_variance: Decimal = negative_returns.iter()
        .map(|r| (*r - mean_negative) * (*r - mean_negative))
        .sum::<Decimal>() / Decimal::from(negative_returns.len() - 1);

    let daily_downside_volatility = downside_variance.sqrt().unwrap_or(Decimal::ZERO);
    
    // 年化下行波动率
    let annualization_factor = Decimal::from_f64_retain(252.0_f64.sqrt()).unwrap_or(Decimal::ONE);
    Ok(daily_downside_volatility * annualization_factor)
}

/// 计算最大回撤
fn calculate_max_drawdown(trades: &[RealizedTradePair], initial_capital: Decimal) -> Result<Decimal> {
    if trades.is_empty() {
        return Ok(Decimal::ZERO);
    }

    let mut sorted_trades = trades.to_vec();
    sorted_trades.sort_by(|a, b| a.exit_timestamp.cmp(&b.exit_timestamp));

    let mut running_capital = initial_capital;
    let mut peak_capital = initial_capital;
    let mut max_drawdown = Decimal::ZERO;

    for trade in sorted_trades {
        running_capital += trade.pnl;
        
        if running_capital > peak_capital {
            peak_capital = running_capital;
        }
        
        let current_drawdown = (peak_capital - running_capital) / peak_capital;
        if current_drawdown > max_drawdown {
            max_drawdown = current_drawdown;
        }
    }

    Ok(max_drawdown)
}

/// 计算平均持仓时间（天）
fn calculate_average_holding_period(trades: &[RealizedTradePair]) -> Result<Decimal> {
    if trades.is_empty() {
        return Ok(Decimal::ZERO);
    }

    let total_days: i64 = trades.iter()
        .map(|trade| (trade.exit_timestamp - trade.entry_timestamp).num_days())
        .sum();

    let average_days = total_days as f64 / trades.len() as f64;
    Ok(Decimal::from_f64_retain(average_days).unwrap_or(Decimal::ZERO))
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::{RealizedTradePair, FifoAnalysisResult};
    use sigmax_core::TradingPair;
    use chrono::TimeZone;

    #[test]
    fn test_performance_metrics_calculation() {
        let trading_pair = TradingPair::new("BTC", "USDT");
        let trades = vec![
            RealizedTradePair::new(
                trading_pair.clone(),
                Utc.with_ymd_and_hms(2023, 1, 1, 0, 0, 0).unwrap(),
                Utc.with_ymd_and_hms(2023, 1, 2, 0, 0, 0).unwrap(),
                "1.0".parse().unwrap(),
                "50000.0".parse().unwrap(),
                "52000.0".parse().unwrap(),
            ),
        ];

        let fifo_result = FifoAnalysisResult {
            realized_pnl_pairs: trades,
            unrealized_positions: vec![],
        };

        let initial_capital = Decimal::from(100000);
        let risk_free_rate = Decimal::from_f32_retain(0.03).unwrap();

        let metrics = calculate_performance_metrics(&fifo_result, initial_capital, risk_free_rate).unwrap();
        
        assert!(metrics.total_return > Decimal::ZERO);
        assert!(metrics.win_rate > Decimal::ZERO);
    }

    #[test]
    fn test_risk_level_classification() {
        let mut metrics = PerformanceMetrics::new();
        
        metrics.max_drawdown = Decimal::from_f32_retain(0.05).unwrap();
        assert_eq!(metrics.risk_level(), RiskLevel::Low);
        
        metrics.max_drawdown = Decimal::from_f32_retain(0.2).unwrap();
        assert_eq!(metrics.risk_level(), RiskLevel::Medium);
        
        metrics.max_drawdown = Decimal::from_f32_retain(0.4).unwrap();
        assert_eq!(metrics.risk_level(), RiskLevel::High);
    }
}
